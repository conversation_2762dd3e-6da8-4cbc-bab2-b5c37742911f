image: node:14.17.6

stages:
  - install  
  - build_and_deploy


cache:
  key: 
    files:
      - package.json
  paths:
    - node_modules

install:
  only: 
    - test
  stage: install 
  tags:
    - font
  script:
    - echo "install开始1"
    - node -v
    - npm install --registry=https://registry.npmmirror.com
    - echo "install结束" 


build:
  only: 
    - test
  stage: build_and_deploy
  tags:
    - font
  script:
    - echo "build开始1"
    - npm run build
    - echo "build结束1"
    - echo $PWD
    - ls
  artifacts:
    paths:
      - dist

deploy:
  only: 
    - test
  stage: build_and_deploy
  tags:
    - font
  before_script:
    - 'which ssh-agent || (yum update update -y && yum install openssh-client git -y )'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    
    - ssh-keyscan ************** >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - echo "拷贝文件开始"
    - echo $PWD
    - scp -r dist/* root@**************:/data/data2/app/amlf/nginx/html
    - echo "拷贝文件结束"
    # 此处直接用ssh命令，并且将后面要重启jar包的命令放在和ssh同一行执行   
  needs:
    - build
 
