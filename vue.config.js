const path = require('path')
const CompressionPlugin = require('compression-webpack-plugin')
function resolve(dir) {
  return path.join(__dirname, dir)
}

// vue.config.js
module.exports = {
  /*
    Vue-cli3:
    Crashed when using Webpack `import()` #2463
    https://github.com/vuejs/vue-cli/issues/2463
   */
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: false,
  // 多入口配置
  // pages: {
  //   index: {
  //     entry: 'src/main.js',
  //     template: 'public/index.html',
  //     filename: 'index.html',
  //   }
  // },
  // 打包app时放开该配置
  // publicPath:'./',

  configureWebpack: config => {
    // 确保optimization对象被初始化
    config.optimization = config.optimization || {};

    // 生产环境取消 console.log
    if (process.env.NODE_ENV === 'production') {
      config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true
    }

    // 编写webpack代码分割配置，提升项目启动和打包速度
    config.optimization.splitChunks = {
      chunks: 'all'
    }
    // 忽略 `vue-server-renderer` 内部对 `module` 模块的引用
    config.externals = config.externals || {};
    config.externals['vue-server-renderer/basic'] = 'vue-server-renderer/basic';
  },
  chainWebpack: (config) => {
    // 减少文件搜索范围
    config.resolve.extensions
      .clear()
      .add('.js')
      .add('.vue')
      .add('.json');

    config.resolve.alias
      .set('@$', resolve('src'))
      .set('@api', resolve('src/api'))
      .set('@assets', resolve('src/assets'))
      .set('@comp', resolve('src/components'))
      .set('@views', resolve('src/views'))

    // 开启或关闭进度条
    // config.plugins.delete('progress');
    // config.plugin('progress').use(new ProgressPlugin());

    // 生产环境，开启js\css压缩
    if (process.env.NODE_ENV === 'production') {
      config.plugin('compressionPlugin').use(new CompressionPlugin({
        test: /\.(js|css|less)$/, // 匹配文件名
        threshold: 10240, // 对超过10k的数据压缩
        deleteOriginalAssets: false // 不删除源文件
      }))
    }

    // 使用thread-loader、cache-loader
    // config.module
    //   .rule('js')
    //   .test(/\.m?js$/)
    //   .exclude.add(/node_modules/).add(/dist/)
    //   .end()
    //   .use('thread-loader') // 使用多线程打包
    //   .loader('thread-loader')
    //   .end()
    //   .use('cache-loader') // 添加 cache-loader 和其配置
    //   .loader('cache-loader') // 纠正为 'cache-loader'
    //   .options({
    //     cacheDirectory: resolve('node_modules/.cache') // 确保使用统一的缓存目录
    //   })
    //   .end()
    //   .use('babel-loader')
    //   .loader('babel-loader')
    //   .end();

    // 配置 webpack 识别 markdown 为普通的文件
    config.module
      .rule('markdown')
      .test(/\.md$/)
      .use()
      .loader('file-loader')
      .end()

    // 编译vxe-table包里的es6代码，解决IE11兼容问题
    config.module
      .rule('vxe')
      .test(/\.js$/)
      .include
      .add(resolve('node_modules/vxe-table'))
      .add(resolve('node_modules/vxe-table-plugin-antd'))
      .end()
      .use()
      .loader('babel-loader')
      .end()
  },

  css: {
    loaderOptions: {
      less: {
        modifyVars: {
          /* less 变量覆盖，用于自定义 ant design 主题 */
          'primary-color': '#066FEC',
          'link-color': '#066FEC',
          'border-radius-base': '4px'
        },
        javascriptEnabled: true
      }
    }
  },

  devServer: {
    port: 8059,
    hot: true,
    open: true, // 自动打开浏览器
    overlay: { // 当出现编译警告或错误时，在浏览器中显示全屏覆盖层
      warnings: false,
      errors: true
    },
    proxy: {
      '/api': {
        target: 'http://*************:8041', // 后端API服务器地址
        ws: false,
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'  // 保持路径中的/api前缀
        }
      }
    }

    // proxy: {
    //  /** '/api': {
    //     target: 'https://mock.ihx.me/mock/5baf3052f7da7e07e04a5116/antd-pro', //mock API接口系统
    //     ws: false,
    //     changeOrigin: true,
    //     pathRewrite: {
    //       '/jeecg-boot': ''  //默认所有请求都加了jeecg-boot前缀，需要去掉
    //     }
    //   },**/
    //   '/jeecg-boot': {
    //     target: 'http://localhost:8080', // 请求本地 需要jeecg-boot后台项目
    //     ws: false,
    //     changeOrigin: true
    //   }
    // }
  },
  lintOnSave: false
}
