{"name": "vue-antd-jeecg", "version": "2.4.0", "private": true, "scripts": {"pre": "cnpm install || yarn --registry https://registry.npmmirror.com || npm install", "serve": "vue-cli-service serve", "build:test": "vue-cli-service build --mode test", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@antv/data-set": "^0.11.4", "@chenfengyuan/vue-qrcode": "^1.0.2", "@fullcalendar/core": "^4.4.0", "@fullcalendar/daygrid": "^4.4.0", "@fullcalendar/interaction": "^4.4.0", "@fullcalendar/resource-timeline": "^4.4.0", "@fullcalendar/timegrid": "^4.4.0", "@fullcalendar/vue": "^4.4.0", "@tinymce/tinymce-vue": "^2.1.0", "@toast-ui/editor": "^2.1.2", "amfe-flexible": "^2.2.1", "ant-design-vue": "^1.7.2", "area-data": "^5.0.6", "autoprefixer": "^8.0.0", "axios": "^0.18.0", "clipboard": "^2.0.4", "codemirror": "^5.46.0", "crypto-js": "^4.2.0", "dayjs": "^1.8.0", "dom-align": "1.12.0", "echarts": "^5.5.0", "enquire.js": "^2.1.6", "js-cookie": "^2.2.0", "js-message": "^1.0.5", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "md5": "^2.2.1", "moment": "^2.30.1", "nprogress": "^0.2.0", "pdfjs-dist": "2.0.943", "postcss": "^7.0.39", "postcss-pxtorem": "^5.1.1", "qrcodejs2": "^0.0.2", "tinymce": "^5.3.2", "viser-vue": "^2.4.8", "vue": "^2.6.10", "vue-area-linkage": "^5.1.0", "vue-cropper": "^0.5.4", "vue-i18n": "^8.7.0", "vue-infinite-scroll": "^2.0.2", "vue-loader": "^15.7.0", "vue-ls": "^3.2.0", "vue-lunar-full-calendar": "^1.2.7", "vue-photo-preview": "^1.1.3", "vue-pop-colorpicker": "^1.0.2", "vue-print-nb-jeecg": "^1.0.9", "vue-router": "^3.0.1", "vue-splitpane": "^1.0.4", "vuedraggable": "^2.20.0", "vuex": "^3.1.0", "vxe-table": "2.9.13", "vxe-table-plugin-antd": "1.8.10", "xe-utils": "2.4.8", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/polyfill": "^7.2.5", "@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-eslint": "^3.3.0", "@vue/cli-service": "^3.3.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "7.2.3", "babel-plugin-import": "^1.13.0", "compression-webpack-plugin": "^3.1.0", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.1.0", "html-webpack-plugin": "^4.2.0", "less": "^3.9.0", "less-loader": "^4.1.0", "sass": "^1.26.2", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.10", "webpack": "^4.46.0", "worker-loader": "^3.0.8"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/strongly-recommended", "@vue/standard"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"generator-star-spacing": "off", "no-mixed-operators": 0, "vue/max-attributes-per-line": [2, {"singleline": 5, "multiline": {"max": 1, "allowFirstLine": false}}], "vue/attribute-hyphenation": 0, "vue/html-self-closing": 0, "vue/component-name-in-template-casing": 0, "vue/html-closing-bracket-spacing": 0, "vue/singleline-html-element-content-newline": 0, "vue/no-unused-components": 0, "vue/multiline-html-element-content-newline": 0, "vue/no-use-v-if-with-v-for": 0, "vue/html-closing-bracket-newline": 0, "vue/no-parsing-error": 0, "no-tabs": 0, "indent": ["off", 2], "no-console": 0, "space-before-function-paren": 0, "semi": 0}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}