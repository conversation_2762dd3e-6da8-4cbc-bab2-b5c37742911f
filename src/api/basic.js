import { getAction } from '@/api/manage'

// 字典
const queryTreeList = (params) => getAction('/project/myCategory/queryTreeList', params);

// 基础信息 - 查询
// const getBasicItem = (params) => getAction('/project/projectInfo/list', params);

// 工序定义 - 查询
const getProjectProcess = (params) => getAction('/project/projectProcess/list', params);

// 设备定义 - 查询
const equipmentQueryList = (params) => getAction('/project/projectEquipment/list', params);
export {
    queryTreeList,
    // getBasicItem,
    getProjectProcess,
    equipmentQueryList
}
