<template>
  <div class="page-card">
    <div class="card-header">
      <div v-if="isShowLine" class="line"></div>
      <span v-if="title" class="title">{{ title }}</span>
      <div class="division"></div>
      <div v-if="this.$slots.action" class="action">
        <slot name="action"></slot>
      </div>
    </div>
    <div class="card-content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'CardNew',
    props: {
      isShowLine: {
        type: Boolean,
        default: true
      },
      title: {
        type: String,
        default: null
      }
    }
  }
</script>

<style lang="less" scoped>
.page-card {
//   padding: 0 20px;
    .card-header {
        width: 100%;
        display: flex;
        align-items: center;
        width: 100%;
        padding-bottom: 12px;
        margin-bottom: 16px;
        .line {
            width: 4px;
            height: 16px;
            background: #3254FF;
            margin-right: 11px;
        }
        .title {
            font-size: 18px;
            color: #212121;
            line-height: 28px;
            font-weight: 600;
        }
        .action {
            margin-left: 56px;
            flex: 0 1 auto;
            text-align: right;
            &:empty {
                display: none;
            }
        }
        .division {
            flex: auto;
            height: 1px;
            border-bottom: 1px solid #E5E6EB;
            margin-left: 11px;
        }
    }
  .card-content {
    margin: 16px 20px 0;
  }
}
</style>
