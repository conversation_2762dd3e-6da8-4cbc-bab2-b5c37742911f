<template>
  <div class="card-layout">
    <div class="card-header">
      <div class="header-top">
        <img v-if="logo" :src="logo" class="logo"/>
        <span v-if="title" class="title">{{ title }}</span>
        <div class="action">
          <slot name="action"></slot>
        </div>
      </div>
      <div class="header-main">
        <div v-if="avatar" class="avatar">
          <a-avatar :src="avatar"/>
        </div>
        <div v-if="this.$slots.headerContent" class="header-content">
          <slot name="headerContent"></slot>
        </div>
        <div v-if="this.$slots.extra" class="extra">
          <slot name="extra"></slot>
        </div>
      </div>
      <div>
      </div>
    </div>
    <div v-if="this.$slots.content" class="card-content">
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script>

  export default {
    name: 'CardLayoutNew',
    props: {
      logo: {
        type: String,
        default: '/public/page/card-icon-1.png'
      },
      title: {
        type: String,
        default: null
      },
      avatar: {
        type: String,
        default: null
      }
    }
  }
</script>

<style lang="less" scoped>
.card-layout {
  padding: 20px;
  background: #FFFFFF;
  border-radius: 12px 12px 12px 12px;
    .card-header {
        width: 100%;
        flex: 0 1 auto;
        .header-top {
            display: flex;
            align-items: center;
            width: 100%;
            padding-bottom: 12px;
            margin-bottom: 16px;
            border-bottom: 1px solid #E5E6EB;
            .avatar {
            // margin-bottom: 16px;
            }
        }
        .title {
            font-size: 18px;
            color: #212121;
            line-height: 28px;
            font-weight: 600;
            flex: auto;
        }
        .logo {
            width: 14px;
            height: 14px;
            margin-right: 11px;
        }
        .action {
            margin-left: 56px;
            min-width: 266px;
            flex: 0 1 auto;
            text-align: right;
            &:empty {
            display: none;
            }
        }
        .header-main {

        }
        .header-content {
            flex: auto;
            color: #1D2129;
        }
        .extra {
            flex: 0 1 auto;
            margin-left: 88px;
            min-width: 242px;
            text-align: right;
        }
    }
  .card-content {
    margin: 16px 20px 0;
  }
  .card-header[data-v-6740ec88] {
    margin: 0px 24px 0;
  }
}
</style>
