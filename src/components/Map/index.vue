<template>
  <div class="mapBox">
    <!-- 地图 -->
    <div id="map" ref="mapContainer"></div>
    <!-- 左上 -->
    <div class="top-left">
      <a-radio-group v-if="defaultTypeChange" button-style="solid" :value="type || defaultType" @change="radioChange">
        <a-radio-button value="2"> 形象进度 </a-radio-button>
        <a-radio-button value="1"> 风场总览 </a-radio-button>
      </a-radio-group>
      <!-- 左上角插槽 -->
      <slot name="top-left"></slot>
    </div>
    <!-- 右侧 -->
    <div class="right-box" v-if="showRightBox">
      <div class="right-top">
        <slot name="right-top" />
      </div>
      <!-- 图例 -->
      <Legend v-if="defaultLegend"></Legend>
    </div>
    <!-- 工具栏 -->
    <div class="tools">
      <img :src="require('@/assets/map/img/compass.png')" />
      <img :src="require('@/assets/map/img/position.png')" @click="setCenter" />
      <img :src="require('@/assets/map/img/plus.png')" @click="setZoom(1)" />
      <img :src="require('@/assets/map/img/minus.png')" @click="setZoom(-1)" />
    </div>
    <!-- 坐标尺 -->
    <div class="mouse-Lnglat">
      <div class="lng">经度：{{ mousemoveLnglat.lng }}</div>
      <div class="lat">纬度：{{ mousemoveLnglat.lat }}</div>
    </div>
    <!-- 其他 -->
    <slot></slot>
  </div>
</template>

<script>
import MapService from '@/utils/MapService'
import Legend from './legend.vue'
import { getAction } from '@/api/manage'
export default {
  components: { Legend },
  data() {
    return {
      map: null,
      type: '',
      mousemoveLnglat: {
        lng: '-',
        lat: '-',
      },
      mapCenter: [],
      siteRange: [],
      porjectName: '',
    }
  },
  props: {
    showRightBox: {
      type: Boolean,
      default: true,
    },
    markPosition: {
      type: Boolean,
      default: false,
    },
    position: {
      type: String,
      default: '',
    },
    // 显示风机
    defaultFan: {
      type: Boolean,
      default: true,
    },
    // 显示海缆
    defaultCable: {
      type: Boolean,
      default: true,
    },
    // 显示升压站
    defaultBoosterStation: {
      type: Boolean,
      default: true,
    },
    defaultShip: {
      type: Boolean,
      default: true,
    },
    // 显示图例
    defaultLegend: {
      type: Boolean,
      default: true,
    },
    // 显示左上角切换
    defaultTypeChange: {
      type: Boolean,
      default: true,
    },
    // 默认显示形象进度
    defaultType: {
      type: String,
      default: '2',
    },
    defaultZoom: {
      type: Number,
      default: 13,
    },
  },
  created() {},
  mounted() {
    this.getProjectInfo()
  },
  computed: {
    zoom() {
      if (this.map) return this.map.getZoom()
    },
  },
  methods: {
    /**
     * @description 初始化地图
     */
    async initMap(type) {
      let mapConfig = {
        mapContainer: this.$refs.mapContainer,
        defaultFan: this.defaultFan,
        defaultCable: this.defaultCable,
        defaultShip: this.defaultShip,
        defaultBoosterStation: this.defaultBoosterStation,
        lng: this.mapCenter[0],
        lat: this.mapCenter[1],
        zoom: this.defaultZoom,
        porjectName: this.porjectName,
        siteRange: this.siteRange,
        type: type || this.defaultType,
      }
      console.log('底图配置', typeof this.position)

      // 定义地图点击事件的回调函数
      const clickMapCallback = (event, markerType) => {
        // console.log('定义地图点击事件的回调函数', event, markerType)
        if (this.markPosition) {
          if (event.lnglat) {
            this.addMarkPosition(event)
          }
        }
        this.$emit('clickMap', event, markerType)
      }
      try {
        const { map, shipData } = await MapService.initMap(mapConfig, clickMapCallback)
        this.map = map
        this.$emit('initMapCompleted', map, shipData)
        map.addEventListener('mousemove', (event) => {
          // 鼠标移动事件
          this.mousemoveLnglat = event.lnglat
        })
        this.$emit('initMap', map, shipData)
        if (this.position && this.position.split(',').length > 0)
          this.addMarkPosition({ lnglat: { lng: this.position.split(',')[0], lat: this.position.split(',')[1] } })
      } catch (error) {
        console.error('地图初始化失败', error)
      }
    },
    /**
     * 标点
     */
    addMarkPosition(event) {
      const { lng, lat } = event.lnglat
      if (!lng || !lat) return
      const allMarker = this.map.getOverlays()
      const currentMarker = allMarker.find((item) => {
        return item.markerType === 'biaodian'
      })
      if (currentMarker) {
        currentMarker.setLngLat(new T.LngLat(lng, lat))
      } else {
        console.log('this.map', lng, lat)
        const markerLayer = new T.Marker(new T.LngLat(lng, lat))
        markerLayer.markerType = 'biaodian'
        this.map.addOverLay(markerLayer)
      }
    },
    /**
     * 销毁地图
     */
    destroyMap() {
      this.map.destroy()
    },
    /**
     * @description 设计施工 实际施工
     */
    radioChange(e) {
      this.type = e.target.value
      this.changeType(this.type)
    },
    /**
     * @description 取基本信息 （中心点 风场）
     */
    getProjectInfo() {
      getAction('/project/projectInfo/list')
        .then((res) => {
          const center = JSON.parse(res.result.records[0].point)
          this.siteRange = JSON.parse(res.result.records[0].siteRange)
          if (Array.isArray(center)) {
            this.mapCenter = center
          } else {
            this.mapCenter = [108.76236, 21.44643]
          }
          this.porjectName = res.result.records[0].name
          this.initMap()
        })
        .catch(() => {
          this.$message.error('请求失败')
        })
    },
    /**
     * @description 修改显示设计图或施工图
     * @param {*} type
     */
    changeType(type) {
      MapService.changeType(type)
    },
    /**
     * @description 层级修改
     */
    setZoom(type) {
      if (type > 0) this.map.zoomIn()
      else this.map.zoomOut()
    },
    /**
     * @description 回到中心点
     */
    setCenter() {
      this.map.centerAndZoom(new T.LngLat(this.mapCenter[0], this.mapCenter[1]), this.defaultZoom)
    },
    setFence(points, config, callback) {
      MapService.setFence(points, config, callback)
    },
    /**
     * @description 设置多个点
     */
    setPoits() {
      MapService.setPoits(arguments)
    },
    /**
     * @param {*} type 点的类型
     * @description 清理点位
     */
    clearPoits() {
      MapService.clearPoits(arguments)
    },
  },
}
</script>

<style scoped lang="less">
.mapBox {
  width: 100%;
  height: 100%;
  position: relative;
  #map {
    width: 100%;
    height: 100%;
    ::v-deep .tdt-label {
      background-color: transparent;
      // color: #fff;
      // padding: 0;
      line-height: normal;
      box-shadow: none;
      // border: none;
      // font-size: 14px !important;
    }
    ::v-deep .tdt-bottom {
      bottom: 5px;
    }
    ::v-deep .tdt-left {
      left: 76px;
    }
  }
  .tools {
    // 工具
    position: absolute;
    left: 26px;
    bottom: 16px;
    display: flex;
    flex-direction: column;
    z-index: 500;
    align-items: center;
    img {
      margin-top: 10px;
      width: 24px;
      height: 24px;
      cursor: pointer;
      &:first-child {
        width: 32px;
        height: 32px;
      }
    }
  }
  //   坐标尺
  .mouse-Lnglat {
    position: absolute;
    bottom: 16px;
    left: 180px;
    z-index: 500;
    // width: 262px;
    // height: 48px;
    // background: #f2f3f5;
    // box-shadow: 0px 2px 4px 0px rgba(195, 195, 195, 0.25);
    // border-radius: 4px 4px 4px 4px;
    // border: 1px solid #e5e6eb;
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-size: 14px;
    line-height: 16px;
    // .lng-lat-mixin(@bg-color) {
    //   display: flex;
    //   align-items: center;
    //   .name {
    //     margin-right: 10px;
    //     color: #1d2129;
    //     font-weight: bold;
    //   }
    //   .value {
    //     box-shadow: inset 0px 2px 3px 0px @bg-color;
    //     padding: 2px 8px;
    //     background-color: @bg-color;
    //     border-radius: 4px;
    //   }
    // }
    .lng {
      margin-right: 16px;
    }
    .lat {
    }
  }
  // 左上角
  .top-left {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 500;
    display: flex;
    .ant-radio-group {
      margin-right: 16px;
    }
  }
  //   右侧
  .right-box {
    position: absolute;
    bottom: 16px;
    right: 16px;
    z-index: 500;
    top: 16px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    gap: 16px;
  }
}
</style>
