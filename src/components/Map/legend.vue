<template>
  <div class="legend">
    <div class="title">风场图例</div>
    <div class="fengji">
      <div class="imgBox">
<!--        <img v-for="item in fanList" :key="item.id" :src="getFileAccessHttpUrl(item.installedIcon)" />-->
        <img :src="require('@/assets/mask.png')"  />
      </div>
      <div class="nameBox">
<!--        <div v-for="item in fanList" :key="item.id">{{ item.fanType }}</div>-->
        <div>金风EN252-10.5</div>
      </div>
    </div>
    <div class="hailan">
      <div v-for="item in cableList" :key="item.id">
        <span class="color" :style="{ backgroundColor: item.color }"></span>
        {{ item.cableType }}
      </div>
    </div>
    <div class="equipment">
      <div>
        <img :src="require('@/assets/map/img/legend/BoosterStation.png')" />
        升压站
      </div>
      <div>
        <div class="icon"></div>
        电子围栏
      </div>
    </div>
<!--    <div class="ship">-->
<!--      <div><img :src="require('@/assets/map/img/legend/WarningShip.png')" />告警船舶</div>-->
<!--      <div><img :src="require('@/assets/map/img/legend/ExternalShip.png')" />外部船舶</div>-->
<!--      <div><img :src="require('@/assets/map/img/legend/InternalShip.png')" />内部船舶</div>-->
<!--    </div>-->
<!--    <div class="tip">注：图例箭头代表船舶行驶方向。</div>-->
  </div>
</template>
  
  <script>
import { getAction } from '@/api/manage'
import { getFileAccessHttpUrl } from '@/api/manage'
export default {
  data() {
    return {
      fanList: [],
      cableList: [],
    }
  },
  created() {},
  mounted() {
    this.getLegendData()
  },
  methods: {
    getFileAccessHttpUrl,
    /**
     * @description 获取风机海缆数据
     */
    getLegendData() {
      getAction('/data/dataFanInfo/list').then((res) => {
        this.fanList = res.result.records
      })
      getAction('/data/dataCableInfo/list').then((res) => {
        this.cableList = res.result.records
      })
    },
  },
}
</script>
  
<style scoped lang="less">
.legend {
  padding: 16px 20px 11px 20px;
  width: 289px;
  background: #ffffff;
  box-shadow: 0px 3px 8px 0px rgba(31, 49, 141, 0.15);
  border-radius: 6px;
  overflow: auto;
  flex: 1;
  .title {
    height: 28px;
    background: linear-gradient(90deg, #3254ff 0%, #4f95ff 100%);
    border-radius: 36px 36px 36px 36px;
    font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
    font-size: 16px;
    color: #ffffff;
    line-height: 28px;
    text-align: center;
    margin-bottom: 18px;
  }
  .fengji {
    border-bottom: 1px solid #e5e6eb;
    padding-bottom: 6px;
    display: flex;
    .imgBox {
      margin-left: 40px;
      margin-right: 12px;
      width: 40px;
      background: #3254ff;
      border-radius: 6px;
      // padding: 6px 0 2px 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      img {
        width: 100%;
        height: 40px;
        // margin-bottom: 4px;
      }
    }
    .nameBox {
      // padding: 6px 0 2px 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      > div {
        // margin-bottom: 4px;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
        color: #1d2129;
      }
    }
  }
  .hailan {
    display: flex;
    flex-wrap: wrap;
    border-bottom: 1px solid #e5e6eb;
    padding: 7px 0 0 0;
    > div {
      width: 50%;
      height: 22px;
      line-height: 22px;
      font-size: 14px;
      color: #4e5969;
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      .color {
        display: inline-block;
        width: 32px;
        margin-right: 8px;
        height: 3px;
      }
    }
  }
  .equipment {
    display: flex;
    padding: 14px 0;
    border-bottom: 1px solid #e5e6eb;
    > div {
      width: 50%;
      line-height: 32px;
      font-size: 14px;
      color: #4e5969;
      display: flex;
      img,
      .icon {
        width: 32px;
        height: 32px;
        margin-right: 13px;
      }
      .icon {
        border: 1px dashed #ff7d00;
      }
    }
  }
  .ship {
    display: flex;
    flex-wrap: wrap;
    padding-top: 8px;
    > div {
      width: 50%;
      line-height: 28px;
      font-size: 14px;
      margin-bottom: 8px;
      color: #4e5969;
      img {
        width: 28px;
        height: 22px;
        margin-right: 5px;
      }
    }
  }
  .tip {
    font-size: 12px;
    color: #b6bed1;
    line-height: 17px;
  }
}
</style>
  