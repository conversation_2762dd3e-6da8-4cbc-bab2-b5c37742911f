```markdown
# CustomTree 组件使用文档

`CustomTree` 是一个基于 Vue 和 Ant Design Vue 实现的树形控件组件，支持动态数据加载、节点选择、多选、自定义节点字段、节点展开/收起等功能。

## 引入组件

首先，你需要导入 `CustomTree` 组件：

```javascript
import CustomTree from '@/components/tableLayout/CustomTree';

export default {
  components: {
    CustomTree
  }
};
```

## 基本用法

以下是一个基本的 `CustomTree` 组件用法示例：

```html
<template>
  <CustomTree
    :treeData="treeData"
    :checkable="true"
    :defaultExpandedKeys="defaultExpandedKeys"
    @on-select="handleSelect"
  />
</template>

<script>
export default {
  data() {
    return {
      treeData: [
        { title: 'Node1', key: '0-0', children: [{ title: 'Child Node1', key: '0-0-1' }] },
        { title: 'Node2', key: '0-1' }
      ],
      defaultExpandedKeys: ['0-0'],
    };
  },
  methods: {
    handleSelect(selectedKeys, e) {
      console.log('Selected:', selectedKeys, e);
    }
  }
};
</script>
```

## Props

| 属性名                | 说明                                           | 类型    | 默认值             |
|-----------------------|------------------------------------------------|---------|--------------------|
| customRef             | 组件的 ref 属性                                | String  | `'treeRef'`        |
| treeLoading           | 控制树加载状态的布尔值                         | Boolean | `false`            |
| dictCode              | 数据字典                                       | String  | `''`               |
| checkable             | 是否显示复选框                                 | Boolean | `false`            |
| replaceFields         | 替换字段，定义树节点对象的键名映射             | Object  | `{ title: 'name', key: 'code' }` |
| treeData              | 树数据源                                       | Array   | `[]`               |
| checkStrictly         | 父子节点选中状态不再关联                       | Boolean | `false`            |
| autoExpandParent      | 是否自动展开父节点                             | Boolean | `false`            |
| defaultExpandedKeys   | 默认展开指定的树节点                           | Array   | `[]`               |
| defaultCheckedKeys    | 默认选中指定的树节点                           | Array   | `[]`               |
| defaultSelectedKeys   | 默认选中指定的树节点                           | Array   | `[]`               |
| defaultExpandAll      | 是否默认展开所有树节点                         | Boolean | `true`             |
| isSelectParentNodes   | 是否允许选择父节点                             | Boolean | `true`             |
| isSelectFirstChild    | 在树节点展开时是否选中第一个子节点             | Boolean | `true`             |

## 事件

| 事件名        | 说明                                       | 回调参数                                         |
|---------------|--------------------------------------------|-------------------------------------------------|
| on-select     | 节点选中时触发                             | `{ selectedKeys: Array, e: Event }`             |
| on-check      | 节点复选框状态改变时触发                   | `{ checkedKeys: Array, e: Event }`              |
| on-expand     | 节点展开/收起时触发                        | `{ expandedKeys: Array, e: Event }`             |
| on-right-click| 节点右键点击时触发                         | `{ event: MouseEvent, node: Object }`           |
| initTree      | 树初始化完成时触发                         | `{ treeData: Array, selectedKeys: Array, checkedKeys: Array }` |
| on-update     | 树数据更新完成时触发                       | `{ treeData: Array, selectedKeys: Array, checkedKeys: Array }` |
| on-clear      | 清除选中状态时触发                         | `{ treeData: Array, selectedKeys: Array, checkedKeys: Array }` |

## 方法

- `expandAll`：展开所有节点。
- `closeAll`：收起所有节点。
- `onClearSelected`：清除所有节点的选中状态。

## 自定义样式

```less
<style lang="less" scoped>
.base-tree {
  width: 100%;
  height: 100%;
  overflow: auto;
  // 自定义样式
}
</style>
```

`CustomTree` 组件提供了灵活的配置选项，便于在不同场景下实现树形结构的展示和交互。
```

请根据你的需求修改示例代码片段及属性说明，以确保它们符合你的项目规范和命名习惯。