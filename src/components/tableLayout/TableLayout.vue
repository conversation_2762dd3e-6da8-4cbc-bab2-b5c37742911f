<template>
  <div class="table-layout">
    <left-right-layout v-bind="baseProps">
      <!-- 布局左侧 -->
      <template #left v-if="isShowLeft">
        <slot name="left">
          <custom-tree
            v-if="treeProps.dictCode || (treeProps.treeData &&treeProps.treeData.length > 0)"
            ref="customTreeRef"
            v-bind="treeProps"
            @update:treeLoading="handleUpdateLoading"
            @on-select="handleTreeSelect"
            @on-check="handleTreeCheck"
            @on-expand="handleTreeExpand"
            @initTree="handleInitTree"
          />
          <template v-else>
            <div class="empty">
              <a-empty />
            </div>
          </template>
        </slot>
      </template>

      <!-- 布局右侧 -->
      <template #right>
        <slot name="right">
          <!-- 右侧头部 -->
          <template v-if="isShowRightHeader">
            <slot name="rightHeader">
              <div class="right-header">
                <img  class="icon" src="@/assets/custom/title-icon.svg"/>
                <span class="title">{{ rightTitle}}</span>
              </div>
            </slot>
            <a-divider class="divider" />
          </template>

          <!-- 搜索区域 -->
          <template v-if="isShowSearch">
             <slot name="search">
                <search-form 
                  ref="searchFormRef" 
                  :modelValue.sync="searchProps.formModel"
                  v-bind="searchProps"
                  @submit="handleSearchSubmit" 
                  @reset="handleSearchReset"
                  @initSearchForm="handleInitSearchForm"
                >
                  <template v-for="(slot, name) in $scopedSlots" :slot="name" slot-scope="text, record, index">
                    <slot :name="name" v-bind="{text, record, index}"></slot>
                  </template>
                </search-form>
                <a-divider class="divider" />
              </slot>
          </template>
         
          <!-- 表格区域 -->
          <template v-if="isShowTable">
            <slot name="table">
              <custom-table 
                ref="customTableRef" 
                v-bind="tableProps" 
                @tableChange="handleTableChange"
                @initTable="handleInitTable"
                @on-selected="onSelectChange"
                :expandedRowKeys="internalExpandedRowKeys"
                @update:expandedRowKeys="onExpandedRowsChange"
                @on-expand="onExpandChange"
              >
                <!-- 头部插槽 -->
                <template #header>
                  <slot name="header"></slot>
                </template>

                <!-- 数据列插槽 -->
                <template v-for="slotName in tableColumnSlots" v-slot:[slotName]="{ text, record, index }">
                  <slot :name="slotName" v-bind="{ text, record, index }"></slot>
                </template>
              </custom-table>
            </slot>
          </template>
          <template v-else>
            <slot />
          </template>
        </slot>
      </template>
    </left-right-layout>
  </div>
</template>

<script>
import LeftRightLayout from './LeftRightLayout'
import CustomTree from './CustomTree'
import SearchForm from './SearchForm'
import CustomTable from './CustomTable'
// import debounce from 'lodash/debounce';

export default {
  name: 'TableLayout',
  components: {
    LeftRightLayout,
    CustomTree,
    SearchForm,
    CustomTable
  },
  props: {
    // 传入LeftRightLayout组件的属性对象
    baseProps: {
      type: Object,
      default: () => ({})
    },
    // 右侧头部
    rightTitle: {
      type: String | Number,
      default: ''
    },
    // 传入CustomTree组件的属性对象
    treeProps: {
      type: Object,
      default: () => ({})
    },
    // 传入SearchForm组件的属性对象
    searchProps: {
      type: Object,
      default: () => ({})
    },
    // 传入CustomTable组件的属性对象
    tableProps: {
      type: Object,
      default: () => ({
        // 是否显示复选框
        checkable: {
          type: Boolean,
          default: false
        },
        selectedRowKeys: Array,
        expandedRowKeys: Array
      })
    }
  },
  data() {
    return {
      internalCheckedKeys: this.treeProps.checkedKeys || [], // 树被勾选的节点
      internalSelectedKeys: this.treeProps.selectedKeys || [], // 树被选择的节点
      internalFormModel: this.searchProps.formModel || {}, // 搜索条件
      internalPagination: this.tableProps.ipagination || false, // 表格分页信息

      // internalSelectedRowKeys: this.tableProps.selectedRowKeys || [], // 表格被选择的行
      internalExpandedRowKeys: this.tableProps.expandedRowKeys || [] // 表格被展开的节点
    }
  },
  computed: {
    // 是否展示左侧
    isShowLeft() {
      return this.$scopedSlots.left || Object.keys(this.treeProps).length > 0
    },
    // 是否展示右侧头部
    isShowRightHeader() {
      return this.$scopedSlots.rightHeader || this.rightTitle
    },
    // 是否展示搜索
    isShowSearch() {
      return this.$scopedSlots.search || Object.keys(this.searchProps).length > 0
    },
    // 是否显示表格
    isShowTable() {
      return this.$scopedSlots.table || Object.keys(this.tableProps).length > 0
    },
    // 获取表格数据插槽
    tableColumnSlots() {
      return this.tableProps.columns && this.tableProps.columns.filter(column => column.scopedSlots && column.scopedSlots.customRender)
        .map(column => column.scopedSlots.customRender)
    },
    mergeParams() {
      return {
        params: this.handleMergeParams(),
        checkedKeys: this.internalCheckedKeys,
        selectedKeys: this.internalSelectedKeys, // 被选择的树节点
        formModel: this.internalFormModel, // 搜索表单数据，需要你在其他地方定义和更新
        pagination: this.internalPagination // 表格分页数据
      };
    }
  },
  watch: {
    // 监听数据变化
    // mergeParams: {
    //   handler(newVal) {
    //     this.$emit('init-params', newVal)
    //   },
    //   // handler: debounce(function(newVal) {
    //   //   this.$emit('init-params', newVal)
    //   // }, 600),
    //   deep: true
    // }
    // 监听搜索数据
    'searchProps.formModel': {
      deep: true,
      handler() {
        this.internalFormModel = this.searchProps.formModel
        this.$emit('init-params', this.mergeParams)
      }
    },
    'tableProps.expandedRowKeys': {
      deep: true,
      handler() {
        this.internalExpandedRowKeys = this.tableProps.expandedRowKeys
        this.$emit('init-params', this.mergeParams)
      }
    }
  },
  mounted() {
    !this.treeProps.treeField && (this.treeProps.treeField = 'treeId')
  },
  methods: {
    onSelectChange(selectedRowKeys, selectedRows) {
      console.log('onSelectChange===>', selectedRows)
      // this.internalSelectedRowKeys  = selectedRowKeys;
      // 这里处理行选中的逻辑，通常你可能需要通知父级组件
      this.$emit('update:selectedRowKeys', selectedRowKeys);
      this.$emit('table-selected', selectedRowKeys, selectedRows)
    },
    onExpandedRowsChange(expandedRowKeys) {
      this.internalExpandedRowKeys  = expandedRowKeys;
      this.$emit("update:expandedRowKeys", expandedRowKeys);
    },
    onExpandChange(expanded, record) {
      this.$emit('table-expand', expanded, record);
    },
    // 树组件初始化完成
    handleInitTree({ treeData, selectedKeys, checkedKeys }) {
      return new Promise((resolve) => {
        this.internalSelectedKeys = selectedKeys || []
        this.$emit('init-params', this.mergeParams)
        this.$emit('tree-init', { 
          ...this.mergeParams,
          treeData,
          selectedKeys,
          checkedKeys
        })
        resolve({ treeData, selectedKeys, checkedKeys })
      })
    },
    // 树loading
    handleUpdateLoading(val) {
      this.$emit('update:treeLoading', val)
    },
    // 树节点选择前的操作
    // beforeTreeSelect({ selectedKeys, e }) {
    //   return new Promise((resolve, reject) => {
    //     this.$emit('before-tree-select', { selectedKeys, e }, { resolve, reject });
    //   });
    // },
    // 树节点选择事件处理
    handleTreeSelect({ selectedKeys, e }) {
      this.internalSelectedKeys = selectedKeys || []
      this.$emit('init-params', this.mergeParams)
      this.$emit('tree-select', {
        ...this.mergeParams,
        selectedKeys,
        e
      })
      // if (this.$listeners['before-tree-select']) {
      //   this.beforeTreeSelect({ selectedKeys, e }).then(() => {
      //     this.treeSelectDefault({ selectedKeys, e })
      //   }).then(() => {
      //     // 检查是否定义了 'after-tree-select' 事件监听器
      //     if (this.$listeners['after-tree-select']) {
      //       // 通过 $emit 触发 'after-tree-select' 事件
      //       this.$emit('after-tree-select', {
      //         ...this.mergeParams,
      //         e
      //       });
      //     }
      //   }).catch((error) => {
      //     console.error("Tree check was not allowed", error);
      //   });
      // } else {
      //   this.treeSelectDefault({ selectedKeys, e })
      //   if (this.$listeners['after-tree-select']) {
      //     // 通过 $emit 触发 'after-tree-select' 事件
      //     this.$emit('after-tree-select', {
      //       ...this.mergeParams,
      //       e
      //     });
      //   }
      // }
    },
    // treeSelectDefault({ selectedKeys, e }) {
    //   this.selectedKeys = selectedKeys || []
    //   this.$emit('tree-select', {
    //     ...this.mergeParams,
    //     e
    //   })
    // },
    // 树节点勾选事件处理
    handleTreeCheck({checkedKeys, e}) {
      this.internalCheckedKeys = checkedKeys;
      this.$emit('init-params', this.mergeParams)
      this.$emit('tree-check', {...this.mergeParams, checkedKeys, e });
    },
    // 树节点展开事件处理
    handleTreeExpand({expandedKeys, e}) {
      this.$emit('init-params', this.mergeParams)
      this.$emit('tree-expand', { ...this.mergeParams, expandedKeys, e })
    },
    // 搜索组件初始化
    handleInitSearchForm(formModel) {
      return new Promise((resolve) => {
        this.internalFormModel = formModel
        this.$emit('init-params', this.mergeParams)
        this.$emit('search-init', { 
          ...this.mergeParams, 
          formModel 
        })
        resolve(formModel)
      })
    },
    // 搜索前的操作
    beforeSearch() {
      return new Promise((resolve, reject) => {
        // 这里可以执行一些前置操作
        this.$emit('before-search', { ...this.mergeParams }, { resolve, reject });
      });
    },
    // 搜索提交事件处理 $emit
    handleSearchSubmit(formModel) {
      if (!this.tableProps.noResetPagination) {
        this.internalPagination && (this.internalPagination.current = 1)
        this.$refs.customTableRef.ipagination && (this.$refs.customTableRef.ipagination.current = 1)
      }
      this.$emit('init-params', this.mergeParams)
      // const params = this.handleMergeParams();
      // 如果外部组件已注册before-search事件监听器，则触发事件
      if (this.$listeners['before-search']) {
        this.beforeSearch().then(() => {
          this.searchDefault(formModel)
        }).catch((error) => {
          console.error("was not allowed", error);
        });
      } else {
        this.searchDefault(formModel)
      }
    },
    searchDefault(formModel) {
      // 原来执行搜索的代码放到这个新方法中
      this.internalFormModel = formModel;
      this.$emit('init-params', this.mergeParams)
      this.$emit('search-submit', {
        ...this.mergeParams,
        formModel
      });
    },
    // 重置前的操作
    beforeReset() {
      return new Promise((resolve, reject) => {
        // 这里可以执行一些前置操作
        this.$emit('before-reset', { ...this.mergeParams }, { resolve, reject });
      });
    },
    // 搜索重置事件处理
    handleSearchReset(formModel) {
      this.$emit('init-params', this.mergeParams)
      // 自定义重置
      if (this.$listeners['search-reset']) {
        // 如果存在外部定义的 custom-reset 事件监听器，则触发它
        this.$emit('search-reset', { ...this.mergeParams });
      } else {
        if (this.$listeners['before-reset']) {
          this.beforeReset().then(() => {
            this.resetDefault(formModel)
          }).catch((error) => {
            console.error("was not allowed", error);
          });
        } else {
          this.resetDefault(formModel)
        }
      }
    }, 
    resetDefault(formModel) {
      this.internalFormModel = formModel
      this.internalPagination && (this.internalPagination.current = 1)
      this.$refs.customTableRef.ipagination && (this.$refs.customTableRef.ipagination.current = 1)
      this.searchDefault(formModel)
    },

    // 表格初始化获取分页数据
    handleInitTable({ pagination }) {
      return new Promise((resolve) => {
        this.internalPagination = pagination
        this.$emit('init-params', this.mergeParams)
        this.$emit('table-init', { 
          ...this.mergeParams,
          pagination
        })
        resolve({ pagination })
      })
    },
    // 表格变化事件处理
     handleTableChange(pagination, filters, sorter) {
        this.internalPagination = pagination
        console.error('TableLayout-handleTableChange', pagination, filters, sorter);
        this.$emit('init-params', this.mergeParams)
        this.$emit('table-change', { ...this.mergeParams, pagination, filters, sorter } )
    },
    // 合并参数方法
    handleMergeParams() {
      let that = this
      const params = { ...that.internalFormModel }
      that.tableProps.ipagination && (params.pageNo = that.internalPagination.current)
      that.tableProps.ipagination && (params.pageSize = that.internalPagination.pageSize)
      if (that.internalSelectedKeys && that.internalSelectedKeys.length > 0) {
        Object.assign(params, { [this.treeProps.treeField]: that.internalSelectedKeys.join() })
      }
      return params
    }
  }
}
</script>

<style lang="less" scoped>
.table-layout {
  display: flex;
  height: 100%;
  .empty {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .right-header {
    display: flex;
    align-items: center;
    width: 100%;
    .icon {
      width: 14px;
      height: 14px;
      margin-right: 11px;
    }
    .title {
      font-size: 18px;
      color: #212121;
      line-height: 28px;
      font-weight: 600;
      flex: auto;
    }
  }
  .divider {
    margin: 12px 0;
  }
}
</style>
