```markdown
# SearchForm 组件文档

`SearchForm` 是一个用于生成动态搜索表单的 Vue 组件，支持多种表单项类型、自定义栅格布局、动态显示与隐藏部分表单项等功能。

## 引入组件

首先，需要在你的项目中引入 `SearchForm` 组件:

```javascript
import SearchForm from '@/components/tableLayout/SearchForm';

export default {
  components: {
    SearchForm
  }
};
```

## 使用示例

以下是一个基本的 `SearchForm` 组件用法示例：

```html
<template>
  <SearchForm
    :customRef="'mySearchForm'"
    :modelValue="searchModel"
    :formItems="formItems"
    :gutter="24"
    @submit="handleSubmit"
    @reset="handleReset"
  />
</template>

<script>
export default {
  data() {
    return {
      searchModel: {},
      formItems: [
        {
          label: '用户名',
          key: 'username',
          placeholder: '请输入用户名'
        },
        {
          label: '用户类型',
          key: 'userType',
          type: 'select',
          options: [{label: '管理员', value: 'admin'}, {label: '普通用户', value: 'normal'}],
          placeholder: '请选择用户类型'
        }
        // 更多表单项配置...
      ],
    };
  },
  methods: {
    handleSubmit(searchModel) {
      console.log('搜索条件提交', searchModel);
      // 进行搜索操作...
    },
    handleReset(searchModel) {
      console.log('表单已重置', searchModel);
      // 可以执行重置后的操作...
    }
  }
};
</script>
```

## Props

| 属性名       | 说明                   | 类型     | 默认值         |
|------------|------------------------|----------|--------------|
| customRef  | 组件的 ref 属性        | String   | 'searchRef'  |
| modelValue | 表单的数据模型         | Object   | `{}`         |
| formItems  | 表单的搜索项配置数组   | Array    | `[]`         |
| gutter     | 栅格之间的间隙大小     | Number   | `24`         |
| buttonSpan | 操作按钮栅格跨度配置   | Object   | 见下文      |

### `formItems` 配置项

每个表单项为一个对象，主要属性如下：

- `label`：标签名。
- `key`：绑定 `modelValue` 中的属性。
- `type`：表单项类型（例如：'select', 'input', 'date' 等）。
- `options`：当类型为 `select` 时，提供选项列表。

## 事件

- `submit`：当点击提交按钮时触发，返回当前表单模型。
- `reset`：点击重置按钮时触发，返回重置后的表单模型。

## 插槽

- 支持使用具名插槽自定义部分表单项的内容。

## 自定义样式

`SearchForm` 组件支持通过修改 scoped CSS 来自定义表单的样式。

```css
<style scoped>
.search-form {
  /* 自定义样式 */
}
</style>
```

本文档提供了 `SearchForm` 组件的详细使用方法。
```


```markdown
## 支持的表单项类型 `type`

`SearchForm` 组件的 `formItems` 属性中可以配置不同类型的表单项，以下是支持的 `type` 类型详细说明：

### Input 类型

- `type: 'input'`
  - 文本输入框
  - 用于输入文本信息

### Select 类型

- `type: 'select'`
  - 下拉选择框
  - 通过配置 `options` 属性来定义下拉列表项

### Date 类型

- `type: 'date'`
  - 日期选择器
  - 用户可以选择或输入日期

### DateTime 类型

- `type: 'datetime'`
  - 日期时间选择器
  - 用户可以选择或输入日期和时间

### Time 类型

- `type: 'time'`
  - 时间选择器
  - 用于选择或输入时间

### DateTime Range 类型

- `type: 'datetime_range'`
  - 日期时间范围选择器
  - 允许用户选择一个日期时间范围

### Area Linkage 类型

- `type: 'area-linkage'` 或 `type: 'pca'`
  - 地区联动选择器
  - 用户通过选择省市区来定位地点

### Select User 类型

- `type: 'select-user'` 或 `type: 'sel_user'`
  - 用户选择器
  - 允许用户从用户列表中选择一个或多个用户

### Select Depart 类型

- `type: 'select-depart'` 或 `type: 'sel_depart'`
  - 部门选择器
  - 允许用户从部门列表中选择一个或多个部门

### Number 类型

- `type: 'int'` 或 `type: 'number'`
  - 数字输入框
  - 用于输入数字，支持设置步进值

### Switch 类型

- `type: 'switch'`
  - 切换选择器
  - 常用于选择“是/否”类型的选项，一般以滑动开关的形式展现

### Popup 类型

- `type: 'popup'`
  - 弹出框选择器
  - 允许用户从弹出框中的列表选择项

### Multi Select Tag 类型

- `type: 'list_multi'`
  - 多选标签选择器
  - 允许用户从列表中选择多个标签项

### Sel Search 类型

- `type: 'sel_search'`
  - 带搜索功能的选择器
  - 允许用户输入文本进行搜索并从结果列表中选择项

---
该文档列出了 SearchForm 组件中表单项支持的所有 `type` 类型及其用途。
```