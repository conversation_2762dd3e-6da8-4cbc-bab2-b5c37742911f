<template>
  <div class="left-right-layout">
    <div v-if="$scopedSlots.left" class="left-panel" :style="leftStyle">
      <slot name="left"></slot>
    </div>
    <div v-if="$scopedSlots.right" class="right-panel" :style="rightStyle">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'LeftRightLayout',
    props: {
      // gutter 属性定义左右间隔的 1/2
      gutter: {
        type: String,
        default: '6px'
      },
      // spanConfig 属性用于定义左右占据的空间
      spanConfig: {
        type: Object,
        default: () => ({ left: 4, right: 20 })
      }
    },
    computed: {
      // 动态计算左右占比
      leftStyle() {
        return {
          'flex': this.spanConfig.left,
          'margin-right': (this.$scopedSlots.right && this.$scopedSlots.left) ? this.gutter : 0
        }
      },
      rightStyle() {
        return {
          'flex': this.spanConfig.right, 
          'margin-left': (this.$scopedSlots.left && this.$scopedSlots.right) ? this.gutter : 0
        }
      }
    }
  };
</script>

<style lang="less" scoped>
.left-right-layout {
  width: 100%;
  height: 100%; /* 占满屏幕高度 */
  overflow: hidden; /* 防止内容溢出 */
  display: flex;
  justify-content: space-between;
}
.left-panel,
.right-panel {
  height: 100%; /* 面板高度占满容器 */
  padding: 12px;
  background: #FFFFFF;
  border-radius: 2px;
}
.right-panel {
  display: flex;
  flex-direction: column;
}
</style>
