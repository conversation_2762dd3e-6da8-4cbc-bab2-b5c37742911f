<template>
  <div class="custom-table" v-if="columns.length > 0">
    <!-- 头部操作区域 -->
    <div class="table-header" v-if="hasHeaderButton">
      <slot name="header">
        <template v-for="(button, index) in headerButtons">
          <slot v-if="button.slot" :name="button.slot"></slot>
          <a-button v-else :key="index" type="primary" v-bind="{ ...button }" @click="button.handler()">
            {{ button.text }}
          </a-button>
        </template>
      </slot>
    </div>
    <!-- 表格区域 -->
    <div ref="tableBody" class="table-body">
      <a-table
        :ref="customRef"
        size="small"
        
        :rowKey="rowKey"
        :columns="mergedColumns"
        :dataSource="dataSource"
        :pagination="false"
        :scroll="scroll"
        bordered
        v-bind="$attrs"
        v-on="$listeners"
        :rowSelection="
          checkable ? rowSelection || { selectedRowKeys: internalSelectedRowKeys, onChange: onSelectChange } : null
        "
        :expandedRowKeys="internalExpandedRowKeys"
        @update:expandedRowKeys="onExpandedRowsChange"
        @expand="onExpandChange"
        @change="handleTableChange"
        :rowClassName="rowClassName"
      >
        <!-- 表格列插槽 -->
        <template v-for="slotName in tableColumnSlots" :slot="slotName" slot-scope="text, record, index">
          <slot v-if="slotName !== 'action'" :name="slotName" v-bind="{ text, record, index }"></slot>
          <!-- 表格默认操作列 -->
          <slot v-else name="action" :record="record">
            <div class="actions" :key="slotName">
              <template v-for="(button, index) in actionButtons">
                <a
                  :key="index"
                  v-if="(index < 3 && actionButtons.length <= 3) || (index < 2 && actionButtons.length > 3)"
                  v-bind="{ ...button.attr }"
                  :disabled="isActionDisabled(button, record)"
                  :class="typeClass(button, record)"
                  @click="button.handler(record)"
                >
                  {{ button.text }}
                </a>
                <a-divider
                  v-if="index < 2 && index < actionButtons.length - 1"
                  :key="index + actionButtons.length"
                  type="vertical"
                />
              </template>
              <a-dropdown v-if="actionButtons.length > 3">
                <a class="ant-dropdown-link" @click="(e) => e.preventDefault()"> 更多 <a-icon type="down" /> </a>
                <a-menu slot="overlay">
                  <a-menu-item v-for="(button, index) in actionButtons.slice(2)" :key="index + 2">
                    <slot v-if="button.slot" :name="button.slot" :record="record"></slot>
                    <a
                      v-else
                      v-bind="{ ...button }"
                      :disabled="isActionDisabled(button, record)"
                      :class="typeClass(button, record)"
                      @click="button.handler(record)"
                    >
                      {{ button.text }}
                    </a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </div>
          </slot>
        </template>
      </a-table>
    </div>
    
    <!-- 分页组件 -->
     <div class="table-footer" style="display: flex;justify-content: flex-end; margin-top: 10px" v-if="ipagination">
      <a-pagination
        :current="ipagination.current"
        :pageSize="ipagination.pageSize"
        :pageSizeOptions="ipagination.pageSizeOptions"
        :total="ipagination.total"
        :showTotal="ipagination.showTotal"
        :showSizeChanger="ipagination.showSizeChanger"
        @change="onPageChange"
        @showSizeChange="onPageSizeChange"
      />
     </div>
  </div>
</template>

<script>
export default {
  name: 'CustomTable',
  inheritAttrs: false,
  props: {
    customRef: {
      type: String,
      default: 'tableRef',
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    headerButtons: {
      type: Array,
      default: () => [],
    },
    columns: {
      type: Array,
      required: true,
    },
    dataSource: {
      type: Array,
      default: () => [],
    },
    rowSelection: {
      type: Object,
      default: () => null,
    },
    checkable: {
      type: Boolean,
      default: false,
    },
    selectedRowKeys: {
      type: Array,
      default: () => [],
    },
    expandedRowKeys: {
      type: Array,
      default: () => [],
    },
    ipagination: {
      type: Object | Boolean,
      default: () => ({
        current: 1,
        pageSize: 20,
        pageSizeOptions: ['10', '20', '50', '100'],
        showTotal: total => `共 ${total} 条`,
        showSizeChanger: true,
        total: 0,
      }),
    },
    actionColumnConfig: {
      type: Object,
      default: () => ({
        title: '操作',
        dataIndex: 'action',
        scopedSlots: { customRender: 'action' },
        align: 'center',
        fixed: 'right',
        width: 175,
      }),
    },
    actionButtons: {
      type: Array,
      default: () => [],
    },
    useZebraStripes: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      scroll: { y: 1000 },
      tableHeight: 0,
      tableHeaderHeight: 38,
      internalSelectedRowKeys: this.selectedRowKeys,
      internalExpandedRowKeys: this.expandedRowKeys,
    };
  },
  computed: {
    mergedColumns() {
      let allColumns = [...this.columns];
      let actionIndex = allColumns.findIndex(item => item.dataIndex === 'action');
      if (actionIndex === -1 && this.actionButtons.length > 0) {
        allColumns.push(this.actionColumnConfig);
      }
      return allColumns;
    },
    tableColumnSlots() {
      return this.mergedColumns
        .filter(column => column.scopedSlots && column.scopedSlots.customRender)
        .map(column => column.scopedSlots.customRender);
    },
    hasHeaderButton() {
      return this.$scopedSlots.header || this.headerButtons.length > 0;
    },
    actionColWidth() {
      return this.actionButtons.length < 3 ? this.actionButtons.length * 56 : 174;
    },
  },
  watch: {
    selectedRowKeys: {
      handler(newVal) {
        this.internalSelectedRowKeys = newVal;
      },
      immediate: true,
      deep: true,
    },
    expandedRowKeys: {
      handler(newVal) {
        this.internalExpandedRowKeys = newVal;
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.initTable();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateTableHeight);
  },
  methods: {
    initTable() {
      return new Promise(resolve => {
        window.addEventListener('resize', this.updateTableHeight);
        setTimeout(() => {
          this.tableHeaderHeight = this.$refs[this.customRef]
            ? this.$refs[this.customRef].$el.querySelector('.ant-table-thead').clientHeight
            : 0;
          this.updateTableHeight();
          resolve({ pagination: this.ipagination });
        }, 500);
        this.$emit('initTable', { pagination: this.ipagination });
      });
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.internalSelectedRowKeys = selectedRowKeys;
      this.$emit('on-selected', selectedRowKeys, selectedRows);
    },
    onExpandedRowsChange(expandedRowKeys) {
      this.internalExpandedRowKeys = expandedRowKeys;
      this.$emit('update:expandedRowKeys', expandedRowKeys);
    },
    onExpandChange(expanded, record) {
      this.$emit('on-expand', expanded, record);
    },
    updateTableHeight() {
      this.tableHeight = this.$refs.tableBody
        ? this.$refs.tableBody.clientHeight
        : 0;
      this.resetTableScroll();
    },
    resetTableScroll() {
      this.scroll = { y: this.tableHeight - this.tableHeaderHeight - 3 };
    },
    handleTableChange(pagination, filters, sorter) {
      this.ipagination.current = pagination.current;
      this.ipagination.pageSize = pagination.pageSize;
      this.$emit('tableChange', pagination, filters, sorter);
    },
    onPageChange(current) {
      this.ipagination.current = current;
      this.$emit('tableChange', this.ipagination);
    },
    onPageSizeChange(current, pageSize) {
      this.ipagination.pageSize = pageSize;
      this.ipagination.current = current;
      this.$emit('tableChange', this.ipagination);
    },
    isActionDisabled(button, record) {
      if (button.hasOwnProperty('disabled')) {
        return typeof button.disabled === 'function' ? button.disabled(record) : button.disabled;
      } else {
        return false;
      }
    },
    typeClass(button, record) {
      return !this.isActionDisabled(button, record) ? button.type : null;
    },
    rowClassName(record, index) {
      return this.useZebraStripes ? (index % 2 === 1 ? 'zebra-row' : '') : '';
    },
  },
};
</script>

<style lang="less" scoped>
.custom-table {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .table-header {
    flex-shrink: 0;
    margin-bottom: 12px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    > * {
      margin-right: 8px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .table-body {
    flex-grow: 1;
    overflow: hidden;
    .actions {
      .default {
        color: #066fec;
      }
      .warning {
        color: #ff7d00;
      }
      .danger {
        color: #ff4d4f;
      }
    }
    ::v-deep .ant-spin-container {
      .ant-table-tbody > tr.zebra-row {
        background: #EFF5FF;
      }
      .ant-table-header {
        width: calc(100% + 9px);
      }
      .ant-table-thead {
        tr > th {
          background: #066fec !important;
          color: #ffffff;
        }
      }
    }
  }
  .table-footer {
    flex-shrink: 1;
  }
}
</style>
