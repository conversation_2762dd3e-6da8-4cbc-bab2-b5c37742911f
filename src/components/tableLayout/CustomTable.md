```markdown
# CustomTable 组件使用文档

`CustomTable` 是一个基于 Ant Design Vue 的表格组件封装，支持自定义表头操作区、行操作、动态列渲染、分页、以及响应式布局等功能。

## 引入组件

```javascript
import CustomTable from '@/components/tableLayout/CustomTable';

export default {
  components: {
    CustomTable
  }
};
```

## 基本用法

以下是一个基本的 `CustomTable` 组件用法示例：

```html
<template>
  <CustomTable
    :columns="columns"
    :dataSource="dataSource"
    @on-expand="handleExpand"
  />
</template>

<script>
export default {
  data() {
    return {
      columns: [
        {
          title: '姓名',
          dataIndex: 'name',
        },
        {
          title: '年龄',
          dataIndex: 'age',
        }
      ],
      dataSource: [
        { id: 1, name: '张三', age: 28 },
        { id: 2, name: '李四', age: 24 }
      ]
    };
  },
  methods: {
    handleExpand(expanded, record) {
      console.log('Row expanded:', expanded, record);
    }
  }
};
</script>
```

## Props

| 属性名                | 说明                   | 类型            | 默认值          |
|-----------------------|------------------------|-----------------|-----------------|
| customRef             | 表格的 ref 属性        | String          | `'tableRef'`    |
| rowKey                | 表格行的唯一标识       | String          | `'id'`          |
| headerButtons         | 表头按钮配置数组       | Array           | `[]`            |
| columns               | 表格列的配置描述       | Array           | `[]`            |
| dataSource            | 数据源                 | Array           | `[]`            |
| checkable             | 是否显示复选框         | Boolean         | `false`         |
| selectedRowKeys       | 默认选中的行           | Array           | `[]`            |
| expandedRowKeys       | 默认展开的行           | Array           | `[]`            |
| ipagination           | 分页配置               | Object/Boolean  | 默认配置见下文  |
| actionColumnConfig    | 操作列配置             | Object          | 默认配置见下文  |
| actionButtons         | 行操作按钮配置数组     | Array           | `[]`            |

### 默认 `ipagination` 配置

```javascript
{
  current: 1,
  pageSize: 20,
  pageSizeOptions: ['10', '20', '50', '100'],
  showTotal: total => `共 ${total}条`,
  showSizeChanger: true,
  total: 0
}
```

### 默认 `actionColumnConfig` 配置

```javascript
{
  title: '操作',
  dataIndex: 'action',
  scopedSlots: { customRender: 'action' },
  align: 'left',
  fixed: 'right',
  width: 175
}
```

## 插槽

- `header`：自定义表格头部操作区域。
- 各 `column` 的 `customRender` 属性对应的插槽：自定义某一列的渲染方式。
- `action`：自定义行操作列的内容。

## 事件

| 事件名             | 说明         | 回调参数                                       |
|-------------------|--------------|------------------------------------------------|
| on-expand         | 节点展开事件 | `(expanded, record)` 展开状态、当前行数据      |
| initTable         | 表格初始化完成后触发   | `{ pagination: Object }`                         |
| tableChange       | 表格分页、筛选、排序变化时触发 | `(pagination, filters, sorter)` 分页信息、筛选条件、排序条件 |

## 方法

以下方法可通过引用（ref）调用：

- `updateTableHeight`：手动更新表格高度，适用于窗口尺寸变化导致表格布局需要更新的场合。
- `resetTableScroll`：重新设置表格的滚动设置，通常与 `updateTableHeight` 一起使用。

## 样式定制

`CustomTable` 组件支持通过修改 `scoped` CSS样式来自定义表格样式，包括但不限于表格头部颜色、行动作按钮样式等：

```less
.custom-table {
  // 自定义样式
  .table-header {
    // 表头样式
  }
  .table-body {
    // 表体样式

    .actions {
      // 动作按钮样式
    }
  }
}
```

请根据你的实际需求调整上述属性、插槽、事件以及方法的具体使用，并遵循你的项目规范进行组件引入和使用。
```

本文档提供了 `CustomTable` 组件的详细使用方式，包括基础用法、API、插槽、事件以及方法调用等，帮助开发者更好地在项目中应用和定制该组件。