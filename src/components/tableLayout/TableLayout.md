```markdown
# TableLayout 组件使用文档

`TableLayout` 旨在协助开发具有左右布局的页面。左侧部分通常用于显示树形结构的导航菜单，而右侧部分用于展示详细的内容，如数据表格和搜索表单。本文档将详细介绍如何使用 `TableLayout` 组件，包括组件的属性、事件、插槽以及自定义样式。

## 基础使用

首先，需要确保你已经导入了 `TableLayout` 组件：

```javascript
import TableLayout from '@/components/tableLayout/TableLayout';
```

在模板中使用：

```html
<template>
  <TableLayout :baseProps="baseProps" :treeProps="treeProps" :searchProps="searchProps" :tableProps="tableProps">
    <!-- 插槽可选 -->
  </TableLayout>
</template>
```

## Props 配置

`TableLayout` 组件接收以下 prop 参数：

### baseProps

- 类型：`Object`
- 说明：传递给 `LeftRightLayout` 组件的属性对象，用于设置左右布局的基础属性。
- 示例：

  ```javascript
  baseProps: {
    splitPercentage: 20, // 左侧宽度占比
    minHeight: '500px', // 最小高度
  }
  ```

### treeProps

- 类型：`Object`
- 说明：传递给 `CustomTree` 组件的属性对象，用于定义树形结构的导航菜单。
- 示例：

  ```javascript
  treeProps: {
    treeData: [], // 树形数据
    checkable: true, // 是否显示复选框
  }
  ```

### searchProps

- 类型：`Object`
- 说明：传递给 `SearchForm` 组件的属性对象，用于定义搜索表单的字段和行为。
- 示例：

  ```javascript
  searchProps: {
    formItems: [
      { label: '姓名', type: 'input', key: 'name' },
      { label: '年龄', type: 'number', key: 'age' }
    ],
  }
  ```

### tableProps

- 类型：`Object`
- 说明：传递给 `CustomTable` 组件的属性对象，用于定义数据表格的列、数据源和行为。
- 示例：

  ```javascript
  tableProps: {
    columns: [
      { title: '姓名', dataIndex: 'name' },
      { title: '年龄', dataIndex: 'age' }
    ],
    dataSource: [],
    rowKey: 'id',
  }
  ```

### rightTitle

- 类型：`String | Number`
- 说明：定义右侧内容区域的标题。

## 插槽 Slot

`TableLayout` 组件提供以下插槽，用于自定义内容：

- `left`：自定义左侧内容。
- `right`：自定义右侧内容。
- `rightHeader`：自定义右侧顶部标题区域。
- `search`：自定义搜索表单的布局和元素。
- `table`：自定义表格区域。

## 事件 Event

`TableLayout` 组件支持以下事件：

- `init-params`：参数初始化事件。
- `tree-init`：树组件初始化完成事件。
- `update:treeLoading`：更新树节点加载状态事件。
- `tree-select`：树节点被选中事件。
- `tree-check`：树节点被勾选事件。
- `tree-expand`：树节点展开/收起事件。
- `search-init`：搜索组件初始化事件。
- `search-submit`：搜索提交事件。
- `search-reset`：搜索重置事件。
- `table-init`：表格初始化事件。
- `table-change`：表格变化事件。

## 自定义样式

`TableLayout` 组件支持通过修改 scoped CSS 来自定义样式：

```css
<style scoped>
.table-layout {
  /* 自定义布局样式 */
}
.right-header {
  /* 自定义右侧标题样式 */
}
</style>
```

## 示例：完整配置

详细示例参照 \src\views\basic\test\test6.vue

```html
<template>
  <TableLayout :baseProps="baseProps" :treeProps="treeProps" :searchProps="searchProps" :tableProps="tableProps" rightTitle="用户管理">
    <template #rightHeader>
      <!-- 自定义右侧头部内容 -->
    </template>
    <template #search>
      <!-- 自定义搜索表单 -->
    </template>
    <template #table>
      <!-- 自定义表格 -->
    </template>
  </TableLayout>
</template>

<script>
export default {
  data() {
    return {
      baseProps: {
        splitPercentage: 25
      },
      treeProps: {
        treeData: [...],
      },
      searchProps: {
        formItems: [...],
      },
      tableProps: {
        columns: [...],
        dataSource: [...],
      },
    };
  }
};
</script>
```

本文档详细介绍了 `TableLayout` 组件的使用方法、属性配置、插槽以及事件。
```