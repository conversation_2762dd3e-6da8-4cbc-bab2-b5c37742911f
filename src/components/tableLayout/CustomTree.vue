<template>
  <a-spin :spinning="internalTreeLoading" class="base-tree">
    <a-tree
      blockNode
      multiple
      v-bind="$attrs"
      v-on="$listeners"
      :ref="customRef"
      :checkable="checkable"
      :treeData="internalTreeData"
      :replaceFields="replaceFields"
      :checkedKeys="internalCheckedKeys"
      :selectedKeys="internalSelectedKeys"
      :expandedKeys="internalExpandedKeys"
      :loadData="lazy ? loadData : null"
      @check="handleCheck"
      @select="handleSelect"
      @expand="handleExpand"
      @rightClick="handleRightClick"
    />
  </a-spin>
</template>

<script>
import { queryTreeList } from '@/api/basic';
import { cloneDeep } from 'lodash'
// 默认的 queryTreeList 方法
const defaultQueryTreeList = queryTreeList

export default {
  name: 'CustomTree',
  inheritAttrs: false, // 不让根元素继承属性
  props: {
    customRef: { type: String, default: 'treeRef' }, // 组件的 ref 属性
    treeLoading: { type: Boolean, default: false }, // 控制树加载状态的布尔值
    dictCode: { type: String, default: '' }, // 数据字典
    checkable: { type: Boolean, default: false }, // 是否显示复选框
    replaceFields: { type: Object, default: () => ({ title: 'name', key: 'code' }) }, // 替换字段，用于定义树节点对象的键名映射
    treeData: { type: Array, default: () => [] }, // 树数据源
    checkStrictly: { type: Boolean, default: false }, // 父子节点选中状态不再关联
    autoExpandParent: { type: Boolean, default: false }, // 是否自动展开父节点
    selectedKeys: { type: Array, default: () => [] }, // 选中值
    checkedKeys: { type: Array, default: () => [] }, // 勾选值
    expandedKeys: { type: Array, default: () => [] }, // 展开值
    defaultExpandAll: { type: Boolean, default: true }, // 是否默认展开所有树节点
    isSelectParentNodes: { type: Boolean, default: true }, // 是否允许选择父节点
    isSelectFirstChild: { type: Boolean, default: true }, // 在树节点展开时是否选中第一个子节点
    lazy: { type: Boolean, default: false }, // 是否启用懒加载模式
    hasChildrenField: { type: String, default: 'hasChild' }, // 是否有子数据字段标识
    fetchTreeData: { // 用于外部传递自定义的请求方法
      type: Function,
      default: defaultQueryTreeList
    }
  },
  data() {
    return {
      // 动态设置 defaultExpandAll 和 isSelectFirstChild 的默认值
      internalDefaultExpandAll: this.lazy ? false : this.defaultExpandAll,
      internalIsSelectFirstChild: this.lazy ? false : this.isSelectFirstChild,

      internalTreeData: [], // 存储转换后的树数据
      internalCheckedKeys: [], // 勾选的节点
      internalSelectedKeys: [], // 选中的节点
      internalExpandedKeys: [], // 展开的节点
      allTreeKeys: [] // 存储所有节点的键值
    };
  },
  computed: {
    // 用于处理树加载的状态
    internalTreeLoading: {
      get() {
        return this.treeLoading;
      },
      set(val) {
        // 用来通知父组件更新treeLoading的值
        this.$emit('update:treeLoading', val);
      }
    },
    // 合并数据，用于传递给父组件
    mergeData() {
      return {
        treeData: this.internalTreeData, 
        selectedKeys: this.internalSelectedKeys,
        checkedKeys: this.internalCheckedKeys,
        expandedKeys: this.internalExpandedKeys,
      };
    }
  },
  watch: {
    // 监听treeData的变化，同步更新内部数据
    treeData: {
      handler(newVal) {
        const clonedData = cloneDeep(newVal);
        this.internalTreeData = this.processTreeData(clonedData);
      },
      immediate: true,
      deep: true
    },
    // 监听checkedKeys的变化，同步更新内部数据
    checkedKeys: {
      handler(newVal) {
        this.internalCheckedKeys = newVal;
      },
      immediate: true,
      deep: true
    },
    // 监听selectedKeys的变化，同步更新内部数据
    selectedKeys: {
      handler(newVal) {
        this.internalSelectedKeys = newVal;
      },
      immediate: true,
      deep: true
    },
    // 监听expandedKeys的变化，同步更新内部数据
    expandedKeys: {
      handler(newVal) {
        this.internalExpandedKeys = newVal;
      },
      immediate: true,
      deep: true
    }
  },
  beforeMount() {
    // 如果启用了懒加载且没有传入 treeData，则初始化时请求数据 pid 传 0
    if (this.lazy) {
      this.initTree('0');
    } else {
      this.initTree();
    }
  },
  methods: {
    // 初始化树
    async initTree(pid = null) {
      const data = await this.loadTree(pid);
      this.internalTreeData = data;
      if (this.internalDefaultExpandAll && data.length > 0) {
        if (this.internalIsSelectFirstChild) {
          const firstChild = this.getFirstChildWithoutChildren(data);
          if (firstChild) {
            this.internalSelectedKeys = [firstChild[this.replaceFields.key]];
          }
        }
        this.expandAllNodes(data);
      }
      this.$emit('initTree', { ...this.mergeData });
    },
    // 获取树数据
    loadTree(pid = null) {
      this.internalTreeLoading = true;
      return new Promise((resolve, reject) => {
        if (!this.dictCode) {
          resolve(this.internalTreeData);
        } else {
          const fetchPid = this.lazy && !pid ? '0' : pid;
          this.fetchTreeData({ code: this.dictCode, pid: fetchPid })
            .then((res) => {
              if (res.success) {
                // 遍历数据，移除叶子节点的 children 属性
                const processedData = this.processTreeData(res.result);
                resolve(processedData);
              } else {
                reject(new Error('Failed to load tree data'));
              }
            })
            .catch((error) => reject(error))
            .finally(() => {
              this.internalTreeLoading = false;
            });
        }
      });
    },
    // 更新树数据
    async updateTree(pid = null) {
      const data = await this.loadTree(pid);
      this.allTreeKeys = [];
      this.internalTreeData = []; // 清空现有数据
      data.forEach(item => {
        this.internalTreeData.push(item);
        if (this.internalDefaultExpandAll) {
          this.setThisExpandedKeys(item);
        }
        this.getAllKeys(item);
      });
      this.$emit('on-update', { ...this.mergeData });
      return this.internalTreeData;
    },
    // 处理树数据，设置 isLeaf 属性
    processTreeData(data = []) {
      const processNode = (node) => {
        if (!(node[this.hasChildrenField] === '1' || (node.children && node.children.length > 0))) {
          node.isLeaf = true;
        } else if (node.children && node.children.length > 0) {
          node.children = node.children.map(child => processNode(child));
        }
        return node;
      };

      return data.map(node => processNode(node));
    },
    // 处理节点选择变更
    handleSelect(selectedKeys, e) {
      let record = e.node.dataRef;
      // 检查 node.children 是否存在且为数组
      if (!this.isSelectParentNodes && record && ((record[this.hasChildrenField] === '1') || (record.children && record.children.length > 0))) return;
      
      this.internalSelectedKeys = [record[this.replaceFields.key]];
      this.$emit('on-select', { selectedKeys: this.internalSelectedKeys, e });
    },
    // 处理复选框变更
    handleCheck(checkedKeys, e) {
      this.internalCheckedKeys = this.checkStrictly ? checkedKeys.checked : checkedKeys;
      this.$emit('on-check', { checkedKeys: this.internalCheckedKeys, e });
    },
    // 处理节点展开变更
    handleExpand(expandedKeys, e) {
      this.internalExpandedKeys = expandedKeys;
      this.$emit('on-expand', { expandedKeys, e });
    },
    // 处理右键点击
    handleRightClick({ event, node }) {
      this.$emit('on-right-click', { event, node });
    },
    // 设置节点展开
    setThisExpandedKeys(node) {
      if (node.children && node.children.length > 0) {
        this.internalExpandedKeys.push(node[this.replaceFields.key]);
        node.children.forEach(child => this.setThisExpandedKeys(child));
      }
    },
    // 获取第一个没有子节点的节点
    getFirstChildWithoutChildren(nodes) {
      for (const node of nodes) {
        if (node.children && node.children.length > 0) {
          const child = this.getFirstChildWithoutChildren(node.children);
          if (child) return child;
        } else {
          return node;
        }
      }
      return null;
    },
    // 获取所有节点的键值
    getAllKeys(node) {
      this.allTreeKeys.push(node[this.replaceFields.key]);
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => this.getAllKeys(child));
      }
    },
    // 展开所有节点
    expandAllNodes(nodes) {
      const stack = [...nodes];
      while (stack.length) {
        const node = stack.pop();
        this.internalExpandedKeys.push(node[this.replaceFields.key]);
        if (node.children) {
          stack.push(...node.children);
        }
      }
    },
    // 懒加载节点数据
    async loadData(e) {
      if (e.dataRef.children && e.dataRef.children.length > 0) return;

      const res = await this.fetchTreeData({ code: this.dictCode, pid: e.dataRef['id'] });
      if (res.success) {
        e.dataRef.children = this.processTreeData(res.result);
        this.internalTreeData = [...this.internalTreeData]; // 触发视图更新
      }
    },
    // 展开所有节点
    expandAll() {
      this.internalExpandedKeys = this.allTreeKeys;
    },
    // 关闭所有节点
    closeAll() {
      this.internalExpandedKeys = [];
    },
    // 清除选择
    onClearSelected() {
      this.internalCheckedKeys = [];
      this.internalSelectedKeys = [];
      this.$emit('on-clear', { ...this.mergeData });
    }
  }
};
</script>

<style lang="less" scoped>
.base-tree {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: auto;
  ::v-deep .ant-tree > li:first-child {
    padding-top: 0;
  }
}
</style>
