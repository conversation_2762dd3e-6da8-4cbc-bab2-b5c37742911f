```markdown
# LeftRightLayout 组件文档

`LeftRightLayout` 是一个 Vue.js 左右布局组件，提供了简洁的API和灵活性，允许开发者自定义左右面板的大小比例和间隔。

## 快速开始

### 引入组件

```javascript
import LeftRightLayout from '@/components/tableLayout/LeftRightLayout';

export default {
  components: {
    LeftRightLayout
  }
};
```

### 基本布局

```html
<LeftRightLayout>
  <template v-slot:left>
    <!-- 这里插入左侧面板的内容 -->
    <div>左侧内容</div>
  </template>
  <template v-slot:right>
    <!-- 这里插入右侧面板的内容 -->
    <div>右侧内容</div>
  </template>
</LeftRightLayout>
```

## Props

| 属性名      | 说明                   | 类型   | 默认值              |
| ----------- | ---------------------- | ------ | ------------------- |
| gutter      | 定义左右间隔的1/2      | String | '6px'               |
| spanConfig  | 定义左右面板占据的空间 | Object | { left: 4, right: 20 } |

## 样式

```less
<style scoped lang="less">
.left-right-layout {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
}
.left-panel,
.right-panel {
  height: 100%;
  padding: 12px;
  background: #FFFFFF;
  border-radius: 2px;
}
.right-panel {
  display: flex;
  flex-direction: column;
}
</style>
```

`LeftRightLayout` 组件的样式使用了 `scoped` 属性，意味着它们不会影响到你的其他组件或全局样式。如果需要调整组件的样式，可以直接修改上述 `<style>` 标签内的内容。

## 动态样式

使用 `computed` 属性来动态计算左右面板的样式：

```javascript
computed: {
  leftStyle() {
    return {
      'flex': this.spanConfig.left,
      'margin-right': this.$scopedSlots.right && this.$scopedSlots.left ? this.gutter : 0
    };
  },
  rightStyle() {
    return {
      'flex': this.spanConfig.right,
      'margin-left': this.$scopedSlots.left && this.$scopedSlots.right ? this.gutter : 0
    };
  }
}
```

通过调整 `spanConfig` 和 `gutter` 的值，你可以实现不同的布局效果。

```

请根据你的需求添加或修改上述文档，并在Markdown编辑器中预览以确保格式的正确性。这份文档包含用法示例、属性说明和内联样式定义，帮助开发者快速掌握如何使用 `LeftRightLayout` 组件。