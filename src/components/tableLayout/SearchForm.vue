<template>
  <div class="search-form" v-if="formItems.length > 0">
    <a-form :model="formModel" @submit.prevent="handleSubmit" layout="inline" :ref="customRef" class="table-search-form">
      <a-row :gutter="gutter" align="middle" type="flex">
        <template v-for="(item, index) in formItems">
          <a-col
            v-show="index < showCount"
            :key="`item-${index}`"
            v-bind="item.colConfig || colConfig">
            <a-form-item
              :style="item.itemStyle || { width: '100%', display: 'flex' }"
              labelAlign="left"
              :label="item.label"
              :labelCol="item.labelCol || labelCol"
              :wrapperCol="item.wrapperCol || wrapperCol">
              <!-- 支持自定义插槽 -->
              <template v-if="item.slot">
                <slot :name="item.slot"></slot>
              </template>
              <!-- 根据type使用不同的组件 -->
              <form-item-wrapper 
                v-else
                :item="item"
                v-model="formModel[item.key]" 
                v-bind="item"
                v-on="item.events"
                @datetime-change="handleComponentChange(item, ...arguments)"
              />
            </a-form-item>
          </a-col>
        </template>
        
        <!-- 操作按钮 -->
        <a-col v-bind="buttonSpan" class="col-button">
          <a-button type="primary" @click="handleSubmit">查询</a-button>
          <a-button @click="handleReset" style="margin-left: 10px;">重置</a-button>
          <span v-if="isHasMore" @click="toggleMore" style="margin-left: 10px;">
            <a-icon type="double-left" :style="{ transform: isCollapsed ? 'rotate(270deg)' : 'rotate(90deg)', transition: 'transform 0.5s ease' }" />
          </span>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script>
import { mixinDevice } from '@/mixins/DeviceMixin'
import FormItemWrapper from './FormItemWrapper.vue'

export default {
  name: 'SearchForm',
  components: { FormItemWrapper },
  mixins: [mixinDevice],
  props: {
    // 组件的 ref 属性
    customRef: {
      type: String,
      default: 'searchRef',
    },
    // 表单的数据模型
    modelValue: {
      type: Object,
      default: () => ({}),
    },
    // 表单的搜索项，包括表单项的标签名、key、插槽占位符等
    formItems: {
      type: Array,
      default: () => [],
    },
    // 指定栅格之间的间隙大小
    gutter: {
      type: Number,
      default: 24,
    },
    // 指定列元素的栅格跨度
    defaultSpan: {
      type: Number,
      default: 6,
    },
    // 指定列元素在不同的屏幕尺寸下的跨度
    colConfig: {
      type: Object,
      default: () => ({ sm: 24, md: 12, lg: 8, xl: 6 }),
    },
    buttonSpan: {
      type: Object,
      default: () => ({ sm: 24, md: 12, lg: 8, xl: 6 }),
    },
    // 指定表单标签在不同屏幕尺寸下的跨度
    labelCol: {
      type: Object,
      default: () => ({
        style: { width: '90px' },
      }),
    },
    // 指定表单内容在不同屏幕尺寸下的跨度
    wrapperCol: {
      type: Object,
      default: () => ({
        style: { flex: '1' },
      }),
    },
  },
  data() {
    return {
      formModel: this.modelValue,
      showCount: this.formItems.length > 4 ? 3 : this.formItems.length,
      isCollapsed: this.formItems.length > 4,
    }
  },
  computed: {
    isHasMore() {
      return this.formItems.length > 3
    },
  },
  created() {
    this.initSearchForm()
  },
  methods: {
    initSearchForm() {
      return new Promise((resolve) => {
        this.$emit('initSearchForm', this.formModel)
        resolve({ formModel: this.formModel })
      })
    },
    handleComponentChange(item, dates, dateString) {
      if (item.type === 'datetime_range' && item.keyParams && Array.isArray(item.keyParams)) {
        this.formModel[item.keyParams[0]] = dateString[0] || ''
        this.formModel[item.keyParams[1]] = dateString[1] || ''
      }
    },
    handleSubmit() {
      this.$emit('submit', this.formModel)
    },
    handleReset() {
      Object.keys(this.formModel).forEach((key) => {
        this.formModel[key] = null
      })
      this.$emit('reset', this.formModel)
    },
    toggleMore() {
      this.isCollapsed = !this.isCollapsed
      this.showCount = this.isCollapsed ? 3 : this.formItems.length
    },
  },
}
</script>

<style scoped>
.search-form {
  width: 100%;
}
.col-button {
  height: 40px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
</style>
