<template>
  <component 
    :is="componentName" 
    v-model="innerValue" 
    v-bind="mergedProps"
    v-on="mergeListeners"
  >
  </component>
</template>

<script>
import moment from 'moment'
import JSearchSelectTag from '@/components/dict/JSearchSelectTag'
import JMultiSelectTag from '@/components/dict/JMultiSelectTag'
import JSelectMultiUser from '@/components/jeecgbiz/JSelectMultiUser'
import JSelectDepart from '@/components/jeecgbiz/JSelectDepart'
import JAreaLinkage from '@/components/jeecg/JAreaLinkage'
import JDate from '@/components/jeecg/JDate.vue'

export default {
  name: 'FormItemWrapper',
  components: {
    JSearchSelectTag,
    JMultiSelectTag,
    JSelectMultiUser,
    JSelectDepart,
    JAreaLinkage,
    JDate,
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    value: [String, Number, Array, Object]
  },
  computed: {
    componentName() {
      const { type, dictCode } = this.item;
      if (type === 'sel_search') return 'JSearchSelectTag';
      if (type === 'list_multi') return 'JMultiSelectTag';
      if (type === 'table-dict') return 'JPopup';
      if (type === 'select-user' || type === 'sel_user') return 'JSelectMultiUser';
      if (type === 'select-depart' || type === 'sel_depart') return 'JSelectDepart';
      if (type === 'area-linkage' || type === 'pca') return 'JAreaLinkage';
      if (type === 'date') return 'JDate';
      if (type === 'datetime') return 'JDate';
      if (type === 'time') return 'ATimePicker';
      if (type === 'datetime_range') return 'ARangePicker';
      if (type === 'int' || type === 'number') return 'AInputNumber';
      if (type === 'switch') return 'ASwitch';
      if (type === 'select') return 'ASelect';
      if (dictCode) return this.allowMultiple(this.item) ? 'JMultiSelectTag' : 'JDictSelectTag';
      return 'AInput';
    },
    componentProps() {
      const { type, dictCode, placeholder, options, showTime, format, customReturnField, val, keyParams } = this.item;
      if (type === 'sel_search') return { dict: this.getDictInfo(this.item), placeholder: placeholder || '请选择', allowClear: true };
      if (type === 'list_multi') return { placeholder: placeholder || '请选择', allowClear: true, options, dictCode: this.getDictInfo(this.item) };
      if (type === 'table-dict') return { code: this.item.dictTable, field: dictCode, orgFields: dictCode, destFields: dictCode, multi: true, allowClear: true };
      if (type === 'select-user' || type === 'sel_user') return { buttons: false, multiple: false, placeholder: '请选择用户', returnKeys: ['id', customReturnField || 'username'], allowClear: true };
      if (type === 'select-depart' || type === 'sel_depart') return { multi: false, placeholder: '请选择部门', customReturnField: customReturnField || 'id', allowClear: true };
      if (type === 'area-linkage' || type === 'pca') return { style: 'width: 100%', allowClear: true };
      if (type === 'date') return { placeholder: '请选择日期', style: 'width: 100%', allowClear: true };
      if (type === 'datetime') return { placeholder: '请选择时间', style: 'width: 100%', allowClear: true, showTime: true, dateFormat: 'YYYY-MM-DD HH:mm:ss' };
      if (type === 'time') return { value: val ? moment(val, 'HH:mm:ss') : null, format: 'HH:mm:ss', style: 'width: 100%', allowClear: true };
      if (type === 'datetime_range') return { style: 'width: 100%', allowClear: true, format: format || 'YYYY-MM-DD HH:mm:ss', showTime: showTime || (showTime === false ? false : { format: 'HH:mm:ss', defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')] }), placeholder: placeholder || ['开始时间', '结束时间'] };
      if (type === 'int' || type === 'number') return { placeholder: '请输入数值', style: 'width: 100%', allowClear: true };
      if (type === 'switch') return { placeholder: '请选择', allowClear: true };
      if (type === 'select') return { options, placeholder: placeholder || '请选择', allowClear: true, mode: this.allowMultiple(this.item) ? 'multiple' : '' };
      if (dictCode) return this.allowMultiple(this.item) ? { dictCode, placeholder: placeholder || '请选择', allowClear: true } : { dictCode, placeholder: placeholder || '请选择', allowClear: true };
      return { placeholder: placeholder || '请输入', allowClear: true };
    },
    mergedProps() {
      return Object.assign({}, this.componentProps, this.$attrs);
    },
    mergeListeners() {
      const listeners = Object.assign({}, this.$listeners);
      // 合并内部事件和外部事件，优先使用外部事件
      if (this.item.type === 'datetime_range') {
        listeners.change = this.$listeners.change || this.handleDatetimeRangeChange;
      }
      // 修改input返回对象的问题
      listeners.input = (event) => {
        const value = event && event.target ? event.target.value : event;
        this.$emit('input', value);
      };
      return listeners;
    },
    innerValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    },
  },
  methods: {
    getDictInfo(item) {
      return item.dictTable ? `${item.dictTable},${item.dictText},${item.dictCode}` : item.dictCode;
    },
    allowMultiple(item) {
      return item.rule === 'in';
    },
    handleDatetimeRangeChange(value, dateString) {
      if (this.$attrs.type === 'datetime_range') {
        this.$emit('datetime-change', value, dateString);
      }
    }
  }
}
</script>
