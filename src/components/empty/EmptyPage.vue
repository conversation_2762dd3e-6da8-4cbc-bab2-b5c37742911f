<template>
  <div class="empty-page" :style="containerStyle">
    <slot>
      <!-- 文本区域 -->
      <div class="empty-text" v-if="text">
        <slot name="text">{{ text }}</slot>
      </div>
    </slot>
  </div>
</template>

<script>
export default {
  name: 'EmptyPage',
  props: {
    // 自定义图片
    image: {
      type: String,
      default: ''
    },
    // 文本内容
    text: {
      type: String,
      default: ''
    },
    // 自定义样式
    customStyle: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    containerStyle() {
      const defaultImage = require('@/assets/image/inDevelopment.png')
      return {
        background: `url(${this.image || defaultImage}) center 40% no-repeat`,
        ...this.customStyle
      }
    }
  }
}
</script>

<style scoped>
.empty-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.empty-text {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  text-align: center;
}
</style>