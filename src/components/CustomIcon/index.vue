<template>
  <svg :class="`anticon anticon-${name} icon custom-${name}`" aria-hidden="true" @click="handleClick">
    <title>{{ name }}</title>
    <use :xlink:href="`#custom-${name}`"></use>
  </svg>
</template>

<script>
export default {
  name: 'CustomIcon',
  props: {
    name: {
      type: String,
      required: true
    }
  },
  methods: {
    handleClick(event) {
      this.$emit('click', event);
    }
  }
}
</script>

<style scoped>
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
</style>
