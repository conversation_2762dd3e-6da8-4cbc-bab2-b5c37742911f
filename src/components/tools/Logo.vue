<template>
  <div class="logo" :class="{ active: sidebarOpened }">
    <router-link :to="{name:'dashboard'}">
      <!-- update-begin- author:sunjianlei --- date:20190814 --- for: logo颜色根据主题颜色变化 -->
      <img v-if="sidebarOpened" src="~@/assets/login-logo.png" alt="logo">
      <!-- <img v-if="navTheme === 'dark'" src="~@/assets/logo.svg" alt="logo"> -->
      <img v-else src="~@/assets/logo.png" alt="logo">
      <!-- update-begin- author:sunjianlei --- date:20190814 --- for: logo颜色根据主题颜色变化 -->

      <!-- <h1 v-if="showTitle">{{ title }}</h1> -->
    </router-link>
  </div>
</template>

<script>
  import { mixin } from '@/mixins/DeviceMixin.js'

  export default {
    name: '<PERSON>go',
    mixins: [mixin],
    props: {
      title: {
        type: String,
        default: 'Jeecg-Boot Pro',
        required: false
      },
      showTitle: {
        type: Boolean,
        default: true,
        required: false
      }
    }
  }
</script>
<style lang="less" scoped>
  /*缩小首页布 局顶部的高度*/
  @height: 59px;

  .sider {
    box-shadow: none !important;
    .logo {
      height: @height !important;
      line-height: @height !important;
      box-shadow: none !important;
      transition: background 300ms;

      a {
        color: white;
        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
    .active {
      a {
        display: inline-block;
        width: 100%;
        height: 100%;
        padding-right: 24px;
      }
      img {
        width: 100%;
        height: 100%;
      }
    }

    &.light .logo {
      background-color: @primary-color;
    }
  }
</style>
