@import "size/tiny";

.j-vxe-table-box {

  // 工具栏
  .j-vxe-toolbar {
    margin-bottom: 8px;
  }

  // 分页器
  .j-vxe-pagination {
    margin-top: 8px;
    text-align: right;

    .ant-pagination-options-size-changer.ant-select {
      margin-right: 0;
    }

    &.show-quick-jumper {
      .ant-pagination-options-size-changer.ant-select {
        margin-right: 8px;
      }
    }
  }

  // 更改 header 底色
  .vxe-table.border--default .vxe-table--header-wrapper,
  .vxe-table.border--full .vxe-table--header-wrapper,
  .vxe-table.border--outer .vxe-table--header-wrapper {
    background-color: #FFFFFF;
  }

}

// 更改 tooltip 校验失败的颜色
.vxe-table--tooltip-wrapper.vxe-table--valid-error {
  background-color: #f5222d !important;
}

// 更改 输入框 校验失败的颜色
.col--valid-error > .vxe-cell > .ant-input,
.col--valid-error > .vxe-cell > .ant-select .ant-input,
.col--valid-error > .vxe-cell > .ant-select .ant-select-selection,
.col--valid-error > .vxe-cell > .ant-input-number,
.col--valid-error > .vxe-cell > .ant-cascader-picker .ant-cascader-input,
.col--valid-error > .vxe-cell > .ant-calendar-picker .ant-calendar-picker-input,
.col--valid-error > .vxe-tree-cell > .ant-input,
.col--valid-error > .vxe-tree-cell > .ant-select .ant-input,
.col--valid-error > .vxe-tree-cell > .ant-select .ant-select-selection,
.col--valid-error > .vxe-tree-cell > .ant-input-number,
.col--valid-error > .vxe-tree-cell > .ant-cascader-picker .ant-cascader-input,
.col--valid-error > .vxe-tree-cell > .ant-calendar-picker .ant-calendar-picker-input {
  border-color: #f5222d !important;
}

// 拖拽排序列样式
.vxe-table .col--row-drag-sort .vxe-cell {
  height: 100%;
}