<template>
  <div class="img">
    <a-upload
      name="file"
      listType="picture-card"
      :multiple="isMultiple"
      :action="uploadAction"
      :headers="headers"
      :data="{biz:bizPath}"
      :fileList="fileList"
      :beforeUpload="beforeUpload"
      :disabled="disabled"
      :isMultiple="isMultiple"
      :showUploadList="isMultiple"
      @change="handleChange"
      @preview="handlePreview">
      <template v-if="!limit || (fileList.length < limit)">
        <template v-if="!(disabled && isView)">
          <div class="iconp">
            <a-icon :type="uploadLoading ? 'loading' : 'plus'" />
            <div class="ant-upload-text">{{ text }}</div>
          </div>
        </template>
        <template v-else>
          <template v-if="!fileList.length">暂无数据</template>
        </template>
      </template>
    </a-upload>
    <a-modal :visible="previewVisible" :footer="null"  width="800px" @cancel="handleCancel()">
      <div class="modal-content">
        <video ref="previewVideo" class="responsive-image" controls :src="previewVideo" style="width: 100%; height: 100%;"></video>
      </div>
    </a-modal>
  </div>
</template>

<script>
  import Vue from 'vue'
  import { ACCESS_TOKEN } from "@/store/mutation-types"
  import { getFileAccessHttpUrl } from '@/api/manage'

  const uidGenerator=()=>{
    return '-'+parseInt(Math.random()*10000+1,10);
  }
  const getFileName=(path)=>{
    if(path.lastIndexOf("\\")>=0){
      let reg=new RegExp("\\\\","g");
      path = path.replace(reg,"/");
    }
    return path.substring(path.lastIndexOf("/")+1);
  }
  export default {
    name: 'JImageUpload',
    data(){
      return {
        isMultiple: true,
        isAccept: true,
        uploadAction:window._CONFIG['domianURL']+"/sys/common/upload",
        uploadLoading:false,
        // picUrl:false,
        headers:{},
        fileList: [],
        previewVideo:"",
        previewVisible: false,
      }
    },
    props:{
      // 查看模式
      isView: {
        type: Boolean,
        default: false
      },
      // 上传个数限制
      limit: {
        type: Number | String,
        default: 0
      },
      text:{
        type:String,
        required:false,
        default:"上传"
      },
      /*这个属性用于控制文件上传的业务路径*/
      bizPath:{
        type:String,
        required:false,
        default:"temp"
      },
      value:{
        type:[String,Array],
        required:false
      },
      disabled:{
        type:Boolean,
        required:false,
        default: false
      },
      // isMultiple:{
      //   type:Boolean,
      //   required:false,
      //   default: true
      // },
    },
    watch:{
      value: {
        handler(val,oldValue) {
          if (val instanceof Array) {
            this.initFileList(val.join(','))
          } else {
            this.initFileList(val)
          }
          // if(!val || val.length==0){
          //   this.picUrl = false;
          // }
        },
        //立刻执行handler
        immediate: true
      }
    },
    created(){
      const token = Vue.ls.get(ACCESS_TOKEN);
      this.headers = {"X-Access-Token":token}
    },
    methods:{
      initFileList(paths){
        let that = this
        if(!paths || paths.length==0){
          that.fileList = [];
          return;
        }
        // that.picUrl = true;
        let fileList = [];
        let arr = paths.split(",")
        for(var a = 0; a < arr.length; a++) {
          let url = getFileAccessHttpUrl(arr[a]);
          fileList.push({
            uid: uidGenerator(),
            name: getFileName(arr[a]),
            status: 'done',
            url: url,
            thumbUrl: '',
            response:{
              status:"history",
              message:arr[a]
            }
          })
        }
        that.fileList = fileList
        that.fileList.forEach(item => {
            that.cutPicture(item)
        })
      },

      // 加载视频元数据完成后提取和更新首帧图片URL
      cutPicture(videoObj) {
        let video = document.createElement("video");
        video.style = 'position:fixed;top:9999px;left:9999px;z-index:-9999'
        video.preload = 'metadata'
        video.currentTime = 1   //截取的视频第一秒作为图片
        video.src = getFileAccessHttpUrl(videoObj.url) 
        video.setAttribute('crossOrigin', 'anonymous')
        video.width = 104
        video.height = 104
        document.body.appendChild(video)
        video.onloadeddata = function () {
            let canvas = document.createElement('canvas')
            canvas.width = video.width
            canvas.height = video.height
            canvas.getContext('2d').drawImage(video, 0, 0, canvas.width, canvas.height)
            const thumbUrl = canvas.toDataURL('image/jpeg');
            videoObj.thumbUrl = thumbUrl
        }
    },
      beforeUpload(file) {
        var fileType = file.type;
        if(fileType.indexOf('video') < 0){
          this.$message.warning('请上传视频');
          this.isAccept = false
        } else {
          this.isAccept = true
        }
        return this.isAccept;
      },
      handleChange(info) {
        // this.picUrl = false;
        console.error('info.fileList', info)
        // 校验不通过时
        if (!this.isAccept) {
          info.fileList.slice(0, -1)
          this.isAccept = true
          return
        }
        let fileList = info.fileList
        if(info.file.status==='done'){
          if(info.file.response.success){
            // this.picUrl = true;
            fileList = fileList.map((file) => {
              if (file.response) {
                file.url = file.response.message;
              }
              return file;
            });
          } else {
            this.$message.error(`${info.file.response.message}`);
          }
          //this.$message.success(`${info.file.name} 上传成功!`);
        }else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 上传失败.`);
        }else if(info.file.status === 'removed'){
          this.handleDelete(info.file)
        }
        this.fileList = fileList
        if(info.file.status==='done' || info.file.status === 'removed'){
          this.handlePathChange()
        }
      },
      // 预览
      handlePreview (file) {
        this.previewVideo = file.url || file.thumbUrl
        this.previewVisible = true
        this.$nextTick(() => {
          const video = this.$refs.previewVideo;
          if (video) {
            video.play();
          }
        })
      },
      getAvatarView(){
        if(this.fileList.length>0){
          let url = this.fileList[0].url
          return getFileAccessHttpUrl(url)
        }
      },
      handlePathChange(){
        let uploadFiles = this.fileList
        let path = ''
        if(!uploadFiles || uploadFiles.length==0){
          path = ''
        }
        let arr = [];
        if(!this.isMultiple){
          arr.push(uploadFiles[uploadFiles.length-1].response.message)
        }else{
          for(let a=0;a<uploadFiles.length;a++){
            // update-begin-author:taoyan date:20200819 for:【开源问题z】上传图片组件 LOWCOD-783
            if(uploadFiles[a].status === 'done' ) {
              arr.push(uploadFiles[a].url)
            }else{
              return;
            }
            // update-end-author:taoyan date:20200819 for:【开源问题z】上传图片组件 LOWCOD-783
          }
        }
        if(arr.length>0){
          path = arr.join(",")
        }
        this.$emit('change', path);
      },
      handleDelete(file){
        //如有需要新增 删除逻辑
        console.log(file)
      },
      handleCancel() {
        const video = this.$refs.previewVideo;
        if (video) {
          video.pause();
        }
        this.previewVisible = false;
      },
    },
    model: {
      prop: 'value',
      event: 'change'
    }
  }
</script>

<style scoped>
  /* update--begin--autor:lvdandan-----date:20201016------for：j-image-upload图片组件单张图片详情回显空白
  * https://github.com/zhangdaiscott/jeecg-boot/issues/1810
  * https://github.com/zhangdaiscott/jeecg-boot/issues/1779
  */
  ::v-deep .imgupload .ant-upload-select{display:block}
  ::v-deep .imgupload .ant-upload.ant-upload-select-picture-card{ width:120px;height: 120px;}
  ::v-deep .imgupload .iconp{padding:32px;}
  /* update--end--autor:lvdandan-----date:20201016------for：j-image-upload图片组件单张图片详情回显空白*/
  .modal-content {
    width: 100%;
    max-height: 480px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  }
  .responsive-image {
    max-width: 100%;
    max-height: 480px;
    width: auto;
    height: auto;
  }
  ::v-deep .ant-modal-close-x {
    width: 36px;
    height: 36px;
    line-height: 36px;
  }
</style>
