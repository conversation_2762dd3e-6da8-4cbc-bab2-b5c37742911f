<template>
  <j-modal
    :width="modalWidth"
    :visible="visible"
    :title="title"
    switchFullscreen
    wrapClassName="j-user-select-modal"
    @ok="handleSubmit"
    @cancel="close"
    style="top:50px"
    cancelText="关闭"
  >
    <a-row :gutter="10" style="background-color: #ececec; padding: 10px; margin: -10px">
      <a-col :md="6" :sm="24">
        <a-card :bordered="false">
          <!--组织机构-->
          <a-directory-tree
            selectable
            :selectedKeys="selectedDepIds"
            :checkStrictly="true"
            :dropdownStyle="{maxHeight:'200px',overflow:'auto'}"
            :treeData="departTree"
            :replaceFields="{children:'children', title:'name', key:'id'}"
            :expandAction="false"
            :expandedKeys.sync="expandedKeys"
            @select="onDepSelect"
          />
        </a-card>
      </a-col>
      <a-col :md="18" :sm="24">
        <a-card :bordered="false">
          姓名:
          <a-input-search
            :style="{width:'150px',marginBottom:'15px'}"
            placeholder="请输入姓名"
            v-model="queryParam.name"
            @search="onSearch"
          ></a-input-search>
          <a-button @click="searchReset(1)" style="margin-left: 20px" icon="redo">重置</a-button>
          <!--用户列表-->
          <a-table
            ref="table"
            :scroll="scrollTrigger"
            size="middle"
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange,type: getType}"
            :loading="loading"
            @change="handleTableChange">
          </a-table>
        </a-card>
      </a-col>
    </a-row>
    <a-row :gutter="10" style="background-color: #ececec; padding: 10px; margin: -10px">
      <a-col :md="6" :sm="24"></a-col>
      <a-col :md="18" :sm="24">
        <span style="margin-right: 15px">已选({{selectedDataRows.length}})</span><a-tag style="margin-bottom: 3px" color="#108ee9" v-for="item in selectedDataRows" closable @close="handleClose(item)">{{item.name+' '+item.phone}}</a-tag>
      </a-col>
    </a-row>
  </j-modal>
</template>

<script>
  import {filterObj} from '@/utils/util'
  import { getUnitList, getPersonList } from '@/api/api'

  export default {
    name: 'JSelectUserByDepModal',
    components: {},
    props: ['modalWidth', 'multi', 'userIds'],
    data() {
      return {
        queryParam: {
          name: "",
        },
        columns: [
          {
            title: '姓名',
            dataIndex: 'name',
            align: 'center'
          },
          {
            title: '性别',
            align: 'center',
            dataIndex: 'sex_dictText'
          },
          {
            title: '手机号码',
            align: 'center',
            dataIndex: 'phone'
          },
          {
            title: '所属单位',
            align: 'center',
            dataIndex: 'department_dictText'
          }
        ],
        scrollTrigger: {},
        dataSource: [],
        allDataSource: [],
        selectedRowKeys: [],
        selectionRows: [],
        selectUserRows: [],
        selectUserIds: [],
        title: '单位人员选择',
        ipagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条'
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0
        },
        isorter: {
          column: 'createTime',
          order: 'desc'
        },
        selectedDepIds: [],
        departTree: [],
        visible: false,
        form: this.$form.createForm(this),
        loading: false,
        expandedKeys: [],
      }
    },
    computed: {
      // 计算属性的 getter
      getType: function () {
        return this.multi == true ? 'checkbox' : 'radio';
      },
      selectedDataRows: function() {
        return this.allDataSource.filter(x => this.selectedRowKeys.includes(x.id))
      }
    },
    watch: {
      userIds: {
        immediate: true,
        handler() {
          this.initUserNames()
        }
      },
    },
    created() {
      // 该方法触发屏幕自适应
      this.resetScreenSize();
      this.loadData()
    },
    methods: {
      handleClose(item){
        this.selectedRowKeys = this.selectedRowKeys.filter(x=>x!==item.id);
        this.selectionRows = this.selectionRows.filter(x=>x.id!==item.id);
      },
      initUserNames() {
        console.log(this.userIds)
        if (this.userIds) {
          // 这里最后加一个 , 的原因是因为无论如何都要使用 in 查询，防止后台进行了模糊匹配，导致查询结果不准确
          let values = this.multi?JSON.parse(this.userIds):[JSON.parse(this.userIds)];

          let selectedRowKeys = []
          let realNames = ''
          values.forEach(user => {
            realNames += ' , ' + user['name'] + ' ' + user['phone']
            selectedRowKeys.push(user['id'])
          })
          realNames = realNames.substring(3)
          this.selectedRowKeys = selectedRowKeys
          this.$emit('initComp', realNames)
        } else {
          // JSelectUserByDep组件bug issues/I16634
          this.$emit('initComp', '')
        }
      },
      async loadData(arg) {
        if (arg === 1) {
          this.ipagination.current = 1;
        }
        console.log(this.selectedDepIds)
        if (this.selectedDepIds && this.selectedDepIds.length > 0) {
          await this.initQueryUserByDepId(this.selectedDepIds)
        } else {
          this.loading = true
          let params = this.getQueryParams()//查询条件
          await getPersonList(params).then((res) => {
            if (res.success) {
              const all = [...this.allDataSource, ...res.result.records]
              this.allDataSource = all.filter((item, index, self) => {
                return self.findIndex(x=>x.id===item.id) === index;
              });
              this.dataSource = res.result.records
              this.ipagination.total = res.result.total
            }
          }).finally(() => {
            this.loading = false
          })
        }
      },
      // 触发屏幕自适应
      resetScreenSize() {
        let screenWidth = document.body.clientWidth;
        if (screenWidth < 500) {
          this.scrollTrigger = {x: 800};
        } else {
          this.scrollTrigger = {};
        }
      },
      showModal() {
        this.visible = true;
        this.queryDepartTree();
        this.initUserNames()
        this.loadData();
        this.form.resetFields();
      },
      getQueryParams() {
        let param = Object.assign({}, this.queryParam, this.isorter);
        param.field = this.getQueryField();
        param.pageNo = this.ipagination.current;
        param.pageSize = this.ipagination.pageSize;
        return filterObj(param);
      },
      getQueryField() {
        let str = 'id,';
        for (let a = 0; a < this.columns.length; a++) {
          str += ',' + this.columns[a].dataIndex;
        }
        return str;
      },
      searchReset(num) {
        let that = this;
        that.selectedRowKeys = [];
        that.selectUserIds = [];
        that.selectedDepIds = [];
        if (num !== 0) {
          that.queryParam = {};
          that.loadData(1);
        }
      },
      close() {
        this.searchReset(0);
        this.visible = false;
      },
      handleTableChange(pagination, filters, sorter) {
        //TODO 筛选
        if (Object.keys(sorter).length > 0) {
          this.isorter.column = sorter.field;
          this.isorter.order = 'ascend' === sorter.order ? 'asc' : 'desc';
        }
        this.ipagination = pagination;
        this.loadData();
      },
      handleSubmit() {
        let that = this;
        this.getSelectUserRows();
        that.$emit('ok', that.selectedDataRows, that.selectedRowKeys);
        that.searchReset(0)
        that.close();
      },
      //获取选择用户信息
      getSelectUserRows(rowId) {
        let dataSource = this.dataSource;
        let userIds = "";
        this.selectUserRows = [];
        for (let i = 0, len = dataSource.length; i < len; i++) {
          if (this.selectedRowKeys.includes(dataSource[i].id)) {
            this.selectUserRows.push(dataSource[i]);
            userIds = userIds + "," + dataSource[i].username
          }
        }
        this.selectUserIds = userIds.substring(1);
      },
      // 点击树节点,筛选出对应的用户
      onDepSelect(selectedDepIds) {
        if (selectedDepIds[0] != null) {
          this.ipagination.current = 1
          this.initQueryUserByDepId(selectedDepIds); // 调用方法根据选选择的id查询用户信息
          if (this.selectedDepIds[0] !== selectedDepIds[0]) {
            this.selectedDepIds = [selectedDepIds[0]];
          }
        }
      },
      onSelectChange(selectedRowKeys, selectionRows) {
        this.selectedRowKeys = selectedRowKeys;
        this.selectionRows = this.allDataSource.filter(x=>selectedRowKeys.includes(x.id));
      },
      onSearch() {
        this.loadData(1);
      },
      // 根据选择的id来查询用户信息
      initQueryUserByDepId(selectedDepIds) {
        this.loading = true
        return getPersonList({department: selectedDepIds.toString()==='-1'?'':selectedDepIds.toString(),
          name : this.queryParam.name,
          pageNo : this.ipagination.current,
          pageSize : this.ipagination.pageSize}).then((res) => {
          if (res.success) {
            const all = [...this.allDataSource, ...res.result.records]
            this.allDataSource = all.filter((item, index, self) => {
              return self.findIndex(x=>x.id===item.id) === index;
            });
            this.dataSource = res.result.records;
            this.ipagination.total = res.result.total;
          }
        }).finally(() => {
          this.loading = false
        })
      },
      queryDepartTree() {
        getUnitList().then((res) => {
          if (res.success) {
            this.departTree = [{
              id:'-1',
              name:'单位名称',
              children:res.result.records}
          ]
            // this.departTree = res.result.records;
            // 默认展开父节点
            this.expandedKeys = this.departTree.map(item => item.id)
          }
        })
      },
      modalFormOk() {
        this.loadData();
      }
    }
  }
</script>

<style scoped>
  .ant-table-tbody .ant-table-row td {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  #components-layout-demo-custom-trigger .trigger {
    font-size: 18px;
    line-height: 64px;
    padding: 0 24px;
    cursor: pointer;
    transition: color .3s;
  }

  /* 树组件文字溢出处理 */
  :deep(.ant-tree-node-content-wrapper) {
    display: inline-block;
    width: calc(100% - 24px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  :deep(.ant-tree-title) {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 树节点悬停时显示完整文字 */
  :deep(.ant-tree-node-content-wrapper:hover .ant-tree-title) {
    overflow: visible;
    white-space: normal;
    word-break: break-all;
    background: #f5f5f5;
    padding: 2px 4px;
    border-radius: 2px;
    position: relative;
    z-index: 999;
  }
</style>