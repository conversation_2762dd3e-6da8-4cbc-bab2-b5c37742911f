.hevue-imgpreview-wrap {
  color: rgba(52, 52, 52, 0.6);
}

/* 遮罩层 */
.hevue-imgpreview-wrap .he-img-wrap {
  background: rgba(70, 70, 70, 0.3);
  backdrop-filter: blur(8px);
}

/* 控制条 */
.hevue-imgpreview-wrap .he-control-bar {
  background: rgba(255, 255, 255, .8);
  backdrop-filter: blur(5px);
  /* box-shadow: 0 0 5px 1px rgb(0 0 0 / 10%); */
  /* border: 1px solid rgba(160, 160, 160, .15); */
}

/* 左右箭头 */
.hevue-imgpreview-wrap .arrow {
  background: rgba(255, 255, 255, .8);
  backdrop-filter: blur(5px);
  /* box-shadow: 0 0 5px 1px rgb(0 0 0 / 10%); */
  /* border: 1px solid rgba(160, 160, 160, .15); */
}

/* 关闭按钮 */
.hevue-imgpreview-wrap .he-close-icon {
  background: rgba(255, 255, 255, .8);
  backdrop-filter: blur(5px);
  /* box-shadow: 0 0 5px 1px rgb(0 0 0 / 10%); */
  /* border: 1px solid rgba(160, 160, 160, .15); */
}

/* 页码指示器 */
.hevue-imgpreview-wrap .he-control-num {
  background: rgba(255, 255, 255, .8);
  backdrop-filter: blur(5px);
  /* box-shadow: 0 0 5px 1px rgb(0 0 0 / 10%); */
  /* border: 1px solid rgba(160, 160, 160, .15); */
}
