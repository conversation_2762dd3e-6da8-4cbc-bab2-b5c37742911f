.hevue-imgpreview-wrap {
  /* position: fixed; */
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  z-index: 9999;
  color: rgba(255,255,255,.6);
}
.hevue-imgpreview-wrap .he-img-wrap {
  width: 100%;
  height: 500px;
  text-align: center;
  vertical-align: middle;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, .3);
}
.hevue-imgpreview-wrap .he-img-view {
  /* transition: transform 0.3s; */
}
.hevue-imgpreview-wrap .arrow {
  width: 42px;
  height: 42px;
  text-align: center;
  line-height: 42px;
  position: absolute;
  top: 50%;
  border-radius: 50%;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  font-size: 24px;
  cursor: pointer;
  transition: all 0.2s;
  background: rgba(0,0,0,.3);
}
.hevue-imgpreview-wrap .arrow:hover {
  opacity: 0.8;
  transform: translateY(-50%) scale(1.2);
}
.hevue-imgpreview-wrap .arrow-left {
  left: 50px;
}
.hevue-imgpreview-wrap .arrow-right {
  right: 50px;
}
/* 关闭按钮 */
.hevue-imgpreview-wrap .he-close-icon {
  position: absolute;
  right: 50px;
  top: 50px;
  width: 36px;
  height: 36px;
  font-size: 22px;
  line-height: 36px;
  text-align: center;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
  background: rgba(0,0,0,.3);
}
.hevue-imgpreview-wrap .he-close-icon:hover {
  transform: rotate(90deg);
}
.hevue-imgpreview-wrap .he-control-bar-wrap {
  display: flex;
  position: absolute;
  width: 100%;
  bottom: 10%;
  left: 0;
}
.hevue-imgpreview-wrap .he-control-bar {
  height: 44px;
  bottom: 10%;
  padding: 0 22px;
  display: flex;
  border-radius: 22px;
  margin: 0 auto;
  background: rgba(0,0,0,.3);
}
.hevue-imgpreview-wrap .he-control-num {
  position: absolute;
  bottom: 5%;
  left: 50%;
  transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  padding: 0 22px;
  font-size: 16px;
  border-radius: 15px;
  background: rgba(0,0,0,.3);
}
.hevue-imgpreview-wrap .he-control-btn {
  line-height: 44px;
  font-size: 24px;
  cursor: pointer;
  padding: 0 9px;
  /* display: inline-block; */
  transition: all 0.2s;
}
.hevue-imgpreview-wrap .he-control-btn:hover {
  transform: scale(1.2);
}

.hevue-imgpreview-wrap .fade-enter-active,
.hevue-imgpreview-wrap .fade-leave-active {
  transition: opacity 0.3s;
}
.hevue-imgpreview-wrap .fade-enter, .hevue-imgpreview-wrap .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}
.hevue-imgpreview-wrap .hevue-img-status-icon {
  font-size: 56px;
}

.hevue-imgpreview-wrap .rotate-animation {
  animation: rotate 1.5s linear infinite;
}
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
