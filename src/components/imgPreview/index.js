import VueToast from "./index.vue";

let imgApp
let ImgPreviewConfig
let instance
let vueVersion

const ImgPreview = (options = {}) => {
  // console.log('ImgPreview', VueToast, options)
  // 如果options是字符串，则将其转换为对象
  if (typeof options === 'string') {
    options = {
      url: options
    }
  }

  // 优先采取局部配置，其次采取全局配置
  Object.keys(ImgPreviewConfig).map(name => {
    if (options[name] == undefined) {
      options[name] = ImgPreviewConfig[name]
    }
  })

  // 如果vueVersion大于2，则使用vue2的写法
  if (vueVersion > 2) {
    // 如果imgApp.ImgPreviewInstalled为false，则创建一个新的div元素，并将imgApp挂载到该元素上
    if (!imgApp.ImgPreviewInstalled) {
      const parent = document.createElement('div')
      instance = imgApp.mount(parent)
      imgApp.ImgPreviewInstalled = true
      let dom = instance.$el
      // console.log(dom)
      // 这里我想注入到指定的元素里面
      const targetElement = document.querySelector(options.targetElement);
      // console.log('插入元素', targetElement)
      if (targetElement) {
        targetElement.appendChild(dom);
      } else {
        document.body.appendChild(dom)
      }
    }

    // 将options中的属性赋值给instance
    Object.keys(options).map(name => {
      instance[name] = options[name]
    })
  } else {
    // 使用vue1的写法
    instance = new imgApp({
      data: options
    })
    instance.$mount()
    let dom = instance.$el
    // 这里我想注入到指定的元素里面
    const targetElement = document.querySelector(options.targetElement);
    // console.log('插入元素', targetElement)
    if (targetElement) {
      targetElement.appendChild(dom);
    } else {
      document.body.appendChild(dom)
    }
  }
  // 显示图片预览
  instance.show()
  return instance
}
const closeImagePreview = (options = {}) => {
  // console.log('closeImagePreview', options)
  // 调用 VueToast  里面的关闭方法  关闭组件预览
  instance.close(options)
}
// closeImagePreview 注册到全局
if (typeof window !== "undefined" && window.Vue) {
  // 挂在到  prototype
  window.Vue.prototype.$closeImagePreview = closeImagePreview;
}
// 安装函数，用于安装ImgPreview插件
const install = async (app, opts = {}) => {

  // 将opts赋值给ImgPreviewConfig
  ImgPreviewConfig = opts

  // 获取vue的版本，并将其拆分成数字
  vueVersion = app.version.split(".")[0]
  // 如果vue版本大于2，则使用import方法导入vue
  if (vueVersion > 2) {
    let { createApp } = await import("vue");
    // 使用createApp方法创建一个vue实例，并将VueToast作为参数传入
    imgApp = createApp(VueToast)
    // 将ImgPreview添加到app的config.globalProperties中
    app.config.globalProperties.$ImgPreview = ImgPreview;
    app.config.globalProperties.$closeImagePreview = closeImagePreview;
  } else {
    // 如果vue版本小于等于2，则使用import方法导入vue，并将其赋值给_vue
    let _vue = await (await import("vue")).default;
    // 使用_vue.extend方法创建一个vue实例，并将VueToast作为参数传入
    imgApp = _vue.extend(VueToast)
    // 将ImgPreview添加到_vue的prototype中
    _vue.prototype.$ImgPreview = ImgPreview;
    _vue.prototype.$closeImagePreview = closeImagePreview;
  }

};

if (typeof window !== "undefined" && window.Vue) {
  install(window.Vue)
}

export default install;