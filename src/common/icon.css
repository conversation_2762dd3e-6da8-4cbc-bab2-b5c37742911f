/*  图标库 2.0 lv 切记web H5 前缀不需要加https  app  必须加   亲测*/
/* CDN 服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
@font-face {
  font-family: 'iconfont';  /* Project id 3579334 */
  src: url('//at.alicdn.com/t/c/font_3579334_do8ycgthtk.woff2?t=1665718126438') format('woff2'),
       url('//at.alicdn.com/t/c/font_3579334_do8ycgthtk.woff?t=1665718126438') format('woff'),
       url('//at.alicdn.com/t/c/font_3579334_do8ycgthtk.ttf?t=1665718126438') format('truetype');
}
.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
}

/* 定位2 */
.icon-position2:before {
  content: '\e603';
}

/* 工人 */
.icon-worker:before {
  content: '\e609';
}
/* 男性 */
.icon-men:before {
  content: '\e6a6';
}
/* 女性 */
.icon-women:before {
  content: '\e6a7';
}

/* 左切换 */
.icon-left:before {
  content: '\e602';
}
/* 右切换 */
.icon-right:before {
  content: '\e601';
}

/* 编辑 */
.icon-edit:before {
  content: '\e6d5';
}

/* 删除 */
.icon-delet:before {
  content: '\e74b';
}

/* 圆圈 */
.icon-circle:before {
  content: '\ea97';
  margin-right: 6px;
}

/* 多边形 */
.icon-polygon:before {
  content: '\ed98';
  margin-right: 6px;
}

/* 正方形 */
.icon-squares:before {
  content: '\ea98';
  margin-right: 6px;
}

/* 播放 */
.icon-play:before {
  content: '\ea82';
}

/* 暂停 */
.icon-stop:before {
  content: '\e662';
}

/* 定位 */
.icon-position:before {
  content: '\e62f';
}
/* 轨迹 */
.icon-path:before {
  content: '\ea05';
}
