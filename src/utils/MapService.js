// 初始化地图
import { message } from 'ant-design-vue'
import { getAction } from '@/api/manage'
import { getFileAccessHttpUrl } from '@/api/manage'
import { getDictItems } from '@/components/dict/JDictSelectUtil'

// 地图对象
// let map = ''
// 数据源（风机，海缆）
let dataSource = {}
// 船舶数据
export default {
  options: {}, //配置项
  type: '', //1风场总览2形象进度
  zoom: null,
  defaultZoom: null,
  scale: 1,
  map: null,
  fanList: [],
  async initMap(options, callback) {
    console.log("🚀 ~ initMap ~ options:", options)
    if (!options || !options.mapContainer) {
      message.error('地图容器未找到！')
      return Promise.reject('地图容器未找到')
    }
    const { mapContainer, lng = 108.76236, lat = 21.44643, zoom = 14, porjectName } = options
    this.options = options
    this.type = options.type
    this.defaultZoom = zoom
    this.zoom = zoom
    try {
      if (window.T) {
        // console.log('地图脚本初始化成功...')
        this.map = new T.Map(mapContainer, {
          projection: 'EPSG:4326', // 使用 WGS 84 坐标系
        })
        // 设置地图中心点和缩放级别
        this.map.centerAndZoom(new T.LngLat(lng, lat), zoom)
        //添加比例尺控件
        const scale = new T.Control.Scale()
        this.map.addControl(scale)
        // 添加地图的点击事件监听器
        this.map.addEventListener('click', (event) => {
          if (callback) {
            callback(event, 'map')
          }
        })
        // 图层改变
        this.map.addEventListener('zoomend', (e) => {
          this.zoomChange(e.target.RR)
        })
        // 设置项目图标
        this.setProject(lng, lat, porjectName)
        // 获取数据
        await this.getData(options, (marker, markerType) => callback(marker, markerType))
        // 是否默认加载 船讯网船舶
        let shipData = []
        // if (options.defaultShip) {
        //   shipData = await this.setCXWShip(callback)
        // }
        this.setWindFarm(options.siteRange, callback)
        // 获取字典
        getDictItems('B10').then((resp) => {
          resp.forEach((data) => {})
        })
        getDictItems('data_fan_info,fan_type,id').then((resp) => {
          this.fanList = resp
        })
        return {
          map: this.map,
          shipData,
        }
      } else {
        throw new Error('地图脚本未加载')
      }
    } catch (error) {
      throw error
    }
  },
  /**
   * @description 图层改变函数
   * @param {*} zoom 层级 （1-8项目图标，大于8风场围栏，大于9升压站，大于11海缆，大于12风机，大于13风机编号船舶名称，10-12船舶点点图标大于12船舶图标）
   */
  zoomChange(zoom) {
    var overlays = this.map.getOverlays()
    this.zoom = zoom
    if (zoom < 9) {
      overlays
        .filter((x) => x.markerType == 'project' || x.markerType == 'projectName')
        .forEach((x) => {
          x.show()
        })
    } else {
      overlays
        .filter((x) => x.markerType == 'project' || x.markerType == 'projectName')
        .forEach((x) => {
          x.hide()
        })
    }
    if (zoom > 8) {
      overlays
        .filter((x) => x.markerType == 'fengchang' || x.markerType == 'weilan')
        .forEach((x) => {
          x.show()
        })
    } else {
      overlays
        .filter((x) => x.markerType == 'fengchang' || x.markerType == 'weilan')
        .forEach((x) => {
          x.hide()
        })
    }
    if (zoom > 9) {
      overlays
        .filter((x) => x.markerType == 'shengyazhan')
        .forEach((x) => {
          x.show()
        })
    } else {
      overlays
        .filter((x) => x.markerType == 'shengyazhan')
        .forEach((x) => {
          x.hide()
        })
    }
    if (zoom > 11) {
      overlays
        .filter((x) => x.markerType == 'hailan')
        .forEach((x) => {
          x.show()
        })
    } else {
      overlays
        .filter((x) => x.markerType == 'hailan')
        .forEach((x) => {
          x.hide()
        })
    }
    if (zoom > 12) {
      overlays
        .filter((x) => x.markerType == 'fengji')
        .forEach((x) => {
          x.show()
        })
    } else {
      overlays
        .filter((x) => x.markerType == 'fengji')
        .forEach((x) => {
          x.hide()
        })
    }
    if (zoom > 13) {
      overlays
        .filter((x) => x.markerType == 'fengjiLabel')
        .forEach((x) => {
          x.show()
        })
    } else {
      overlays
        .filter((x) => x.markerType == 'fengjiLabel')
        .forEach((x) => {
          x.hide()
        })
    }

    // 图层改变图标变化
    let { scale } = this
    if (zoom < 13) {
      scale = 1
    } else {
      scale = (1.6 ** Math.abs(zoom - 13)).toFixed(1)
    }
    this.scale = scale
    // 船舶------------------- 船舶不变化
    // if (zoom < 10) {
    //   // overlays
    //   //   .filter(x => x.markerType == 'ship')
    //   //   .forEach(x => {
    //   //     x.hide()
    //   //   })
    // } else if (zoom > 9 && zoom < 13) {
    //   overlays
    //     .filter(x => x.markerType == 'shipLabel')
    //     .forEach(x => {
    //       x.hide()
    //     })
    //   overlays
    //     .filter(x => x.markerType == 'ship')
    //     .forEach(x => {
    //       x.show()
    //       x.setIcon(
    //         new T.Icon({
    //           iconUrl: require('@/assets/map/img/shipGreen.png'),
    //           iconSize: new T.Point(16, 16),
    //           iconAnchor: new T.Point(8, 8)
    //         })
    //       )
    //     })
    // } else {
    //   overlays
    //     .filter(x => x.markerType == 'shipLabel')
    //     .forEach(x => {
    //       x.show()
    //     })
    //   overlays
    //     .filter(x => x.markerType == 'ship')
    //     .forEach(x => {
    //       x.setIcon(
    //         new T.Icon({
    //           iconUrl: require(`@/assets/map/img/legend/${x.shipType}.png`),
    //           iconSize: new T.Point(42, 32),
    //           iconAnchor: new T.Point(21, 16)
    //         })
    //       )
    //     })
    // }
    // 升压站图标自适应
    overlays
      .filter((x) => x.markerType == 'shengyazhan')
      .forEach((x) => {
        let { iconUrl } = x.getIcon().options
        x.setIcon(
          new T.Icon({
            iconUrl,
            iconSize: new T.Point(32 * scale, 32 * scale),
            iconAnchor: new T.Point(16 * scale, 16 * scale),
          })
        )
      })
    // 风机图标自适应
    overlays
      .filter((x) => x.markerType == 'fengji')
      .forEach((x) => {
        let { iconUrl } = x.getIcon().options
        x.setIcon(
          new T.Icon({
            iconUrl,
            iconSize: new T.Point(50 * scale, 50 * scale),
            iconAnchor: new T.Point(26 * scale, 45 * scale),
          })
        )
      })
    // 设置风机船舶label
    overlays
      .filter((x) => x.markerType == 'fengjiLabel' || x.markerType == 'shipLabel')
      .forEach((x) => {
        let fontSizeScale = zoom > 14 ? 1.6 ** (zoom - 14) : 1
        x.setFontSize(12 * fontSizeScale)
        if (x.markerType == 'fengjiLabel') x.setOffset(new T.Point(-37, 30 * fontSizeScale))
        else x.setOffset(new T.Point(-50, 40 * fontSizeScale))
      })
    // 海缆线自适应
    overlays
      .filter((x) => x.markerType == 'hailan')
      .forEach((x) => {
        x.setWeight(zoom - 10)
      })
  },
  /**
   * @description 设置项目图标
   * @param {*} lng
   * @param {*} lat
   * @param {*} porjectName
   */
  setProject(lng, lat, porjectName) {
    const markerLayer = new T.Marker(new T.LngLat(lng, lat), {
      icon: new T.Icon({
        iconUrl: require('@/assets/map/img/projectIcon2.png'),
        iconSize: new T.Point(100, 100),
        iconAnchor: new T.Point(50, 60),
      }),
    })
    markerLayer.markerType = 'project'
    // 创建标记的文本标签
    const label = new T.Label({
      offset: new T.Point(-100, 50), // 偏移量，使标签在标记上方显示
      text: porjectName, // 显示标记的名称
      position: new T.LngLat(lng, lat), // 标签的位置
    })

    label.markerType = 'projectName'
    label.setBorderColor('blue')
    label.setBorderLine(1)
    label.setFontColor('blue')
    label.setBackgroundColor('transparent')
    // 将文本标签添加到地图上
    this.map.addOverLay(label)
    this.map.addOverLay(markerLayer)
    if (this.defaultZoom > 8) {
      markerLayer.hide()
      label.hide()
    }
  },
  /**
   * @description 获取数据(风机 海缆 升压站)
   */
  getData({ type, defaultCable, defaultFan, defaultBoosterStation }, callback) {
    getAction('/project/projectPbs/getBaseMapData').then((res) => {
      dataSource = res.result || {}
      if (defaultCable) this.submarineCable(type, (marker, markerType) => callback(marker, markerType)) //海缆
      if (defaultFan) this.setFanMarks(type, (marker, markerType) => callback(marker, markerType)) //风机
      if (defaultBoosterStation) this.setBoosterStation((marker, markerType) => callback(marker, markerType))
    })
  },
  /**
   * @description （1设计施工 2实际施工）
   */
  changeType(value) {
    const overlays = this.map.getOverlays()
    this.type = value
    overlays.forEach((x) => {
      if (x.markerType === 'fengji') {
        const url = value == '1' ? x.installedIcon : x.lastProcessIcon
        const icon = new T.Icon({
          iconUrl: url ? getFileAccessHttpUrl(url) : require(`@/assets/map/img/Fan-${x.specificationModel}.png`),
          iconSize: new T.Point(50 * this.scale, 50 * this.scale),
          iconAnchor: new T.Point(26 * this.scale, 45 * this.scale),
        })
        x.setIcon(icon)
      } else if (x.markerType == 'hailan') {
        x.setLineStyle(value == 2 && !x.isCompleted ? 'dashed' : 'solid')
      }
    })
  },

  /**
   * @description 风机标点
   * @param {*} markers
   * @param {*} callback
   */
  setFanMarks(type, callback) {
    if (!dataSource.fanList) return
    dataSource.fanList.forEach((marker) => {
      // console.log('🚀 ~ setFanMarks ~ marker:', marker)
      let point = JSON.parse(marker.point)
      if (Array.isArray(point) && point.length == 2) {
        const url = type == '1' ? marker.installedIcon : marker.lastProcessIcon
        const markerLayer = new T.Marker(new T.LngLat(point[0], point[1]), {
          icon: new T.Icon({
            iconUrl: url ? getFileAccessHttpUrl(url) : require(`@/assets/map/img/Fan-${marker.specificationModel}.png`),
            iconSize: new T.Point(50, 50),
            iconAnchor: new T.Point(26, 45),
            iconId: marker.id,
          }),
        })

        // markerLayer.setZIndexOffset(999)
        markerLayer.markerType = 'fengji'
        markerLayer.lastProcessIcon = marker.lastProcessIcon
        markerLayer.specificationModel = marker.specificationModel
        markerLayer.installedIcon = marker.dataFanInfo.installedIcon
        markerLayer.addEventListener('click', () => {
          // 调用回调函数，并传递 marker 对象作为参数
          if (callback) {
            callback(marker, 'fengji')
          }
        })
        markerLayer.addEventListener('mouseover', (e) => {
          let { iconAnchor, iconId, iconSize, iconUrl } = e.target.getIcon().options
          e.target.setIcon(
            new T.Icon({
              iconId,
              iconUrl,
              iconSize: new T.Point(iconSize.x * 2, iconSize.y * 2),
              iconAnchor: new T.Point(iconAnchor.x * 2, iconAnchor.y * 2),
            })
          )
        })
        markerLayer.addEventListener('mouseout', (e) => {
          let { iconAnchor, iconId, iconSize, iconUrl } = e.target.getIcon().options
          e.target.setIcon(
            new T.Icon({
              iconId,
              iconUrl,
              iconSize: new T.Point(iconSize.x / 2, iconSize.y / 2),
              iconAnchor: new T.Point(iconAnchor.x / 2, iconAnchor.y / 2),
            })
          )
        })
        // 创建标记的文本标签
        const label = new T.Label({
          offset: new T.Point(-37, 30), // 偏移量，使标签在标记上方显示
          text: marker.name, // 显示标记的名称
          position: new T.LngLat(point[0], point[1]), // 标签的位置
        })

        label.markerType = 'fengjiLabel'
        label.setBorderColor('transparent')
        label.setFontColor('#fff')
        label.setFontSize(12)
        label.setBackgroundColor('transparent')
        // 将文本标签添加到地图上
        this.map.addOverLay(label)
        this.map.addOverLay(markerLayer)
        if (this.zoom < 14) label.hide()
      }
    })
  },
  /**
   * @description 画海缆线
   * @param {*} callback
   */
  submarineCable(type, callback) {
    if (!dataSource.cableList) return
    dataSource.cableList.forEach((item) => {
      if (item.point) {
        let json = JSON.parse(item.point)
        let points = []
        if (Array.isArray(json) && json.length >= 2) {
          json.forEach((x) => {
            points.push(new T.LngLat(x[0], x[1]))
          })
          // 开始画线
          const polyline = new T.Polyline(points, {
            color: item.dataCableInfo.color || '#F3C978',
            weight: 3,
            opacity: 1,
            lineStyle: type == 2 && !item.isCompleted ? 'dashed' : 'solid',
          })
          polyline.markerType = 'hailan'
          polyline.isCompleted = item.isCompleted
          polyline.addEventListener('click', () => {
            if (callback) {
              callback(item, 'hailan')
            }
          })
          polyline.addEventListener('mouseover', (e) => {
            e.target.setWeight(e.target.getWeight() * 2)
          })
          polyline.addEventListener('mouseout', (e) => {
            e.target.setWeight(e.target.getWeight() / 2)
          })
          this.map.addOverLay(polyline)
        }
      }
    })
  },
  /**
   * @description 电子围栏
   * @param {*} fence
   * @param {*} callback
   */
  setFence(container = this.map, fence, callback) {
    let points = []
    fence.forEach((x) => {
      points.push(new T.LngLat(x[0], x[1]))
    })
    // 开始画线
    const polyline = new T.Polyline(points, {
      color: '#FF7D00',
      weight: 4,
      opacity: 1,
      lineStyle: 'dashed',
    })

    polyline.markerType = 'weilan'
    container.addOverLay(polyline)
  },
  /**
   * @description 绘制风场
   * @param {*} windFarm
   * @param {*} callback
   */
  setWindFarm(windFarm, callback) {
    const polygonConfig = {
      color: '#7599F4',
      weight: 1,
      opacity: 0.8,
      fillColor: '#7599F4',
      fillOpacity: 0.8,
    }
    if (Array.isArray(windFarm)) {
      let points = []
      windFarm.forEach((x) => {
        points.push(new T.LngLat(x[0], x[1]))
      })
      const Polygon = new T.Polygon(points, polygonConfig)
      Polygon.markerType = 'fengchang'
      // Polygon.addEventListener('click', e => {
      //   if (callback) {
      //     callback(e, 'fengchang')
      //   }
      // })
      this.map.addOverLay(Polygon)
    }
  },
  /**
   * @description 升压站
   * @param {*} callback
   */
  setBoosterStation(callback) {
    dataSource.syzPbsList.forEach((marker) => {
      let point = JSON.parse(marker.point)
      if (Array.isArray(point) && point.length == 2) {
        const markerLayer = new T.Marker(new T.LngLat(point[0], point[1]), {
          zIndexOffset: 0,
          icon: new T.Icon({
            iconUrl: require('@/assets/map/img/legend/BoosterStation.png'),
            iconSize: new T.Point(32, 32),
            iconAnchor: new T.Point(16, 16),
            iconId: marker.id,
          }),
        })
        markerLayer.markerType = 'shengyazhan'
        markerLayer.addEventListener('click', () => {
          // 调用回调函数，并传递 marker 对象作为参数
          if (callback) {
            callback(marker, 'shengyazhan')
          }
        })
        markerLayer.addEventListener('mouseover', (e) => {
          console.log('进-----升压站')
          let { iconAnchor, iconId, iconSize, iconUrl } = e.target.getIcon().options
          e.target.setIcon(
            new T.Icon({
              iconId,
              iconUrl,
              iconSize: new T.Point(iconSize.x * 2, iconSize.y * 2),
              iconAnchor: new T.Point(iconAnchor.x * 2, iconAnchor.y * 2),
            })
          )
        })
        markerLayer.addEventListener('mouseout', (e) => {
          console.log('出-----升压站')
          let { iconAnchor, iconId, iconSize, iconUrl } = e.target.getIcon().options
          e.target.setIcon(
            new T.Icon({
              iconId,
              iconUrl,
              iconSize: new T.Point(iconSize.x / 2, iconSize.y / 2),
              iconAnchor: new T.Point(iconAnchor.x / 2, iconAnchor.y / 2),
            })
          )
        })
        this.map.addOverLay(markerLayer)
      }
    })
  },
  /**
   * @description 船讯网船舶
   * @param {*} callback
   * @returns
   */
  async setCXWShip(callback) {
    try {
      let url = '/ship/shipAlarm/queryAlarm'
      const res = await getAction(url)
      const { success, result } = res
      if (!success) {
        throw new Error('获取船舶数据失败')
      }
      if (result) {
        // callback(result) // 如果需要在请求成功后执行额外的回调函数，可以在此处调用
        this.setShipMarks(this.map, res.result.outShip || [], 'ExternalShip', callback)
        this.setShipMarks(this.map, res.result.inShip || [], 'InternalShip', callback)
        this.setShipMarks(this.map, res.result.alarmShip || [], 'WarningShip', callback)
        this.setFence(this.map, JSON.parse(res.result.fenceRadius))
        return result
      } else {
        return []
      }
    } catch (error) {
      console.err(error)
      return []
    }
  },
  /**
   * @description 船舶标点
   * @param {*} markers
   * @param {*} callback
   */
  setShipMarks(map = this.map, markers, type, callback) {
    markers.forEach((marker) => {
      let lng = marker.lon * Math.pow(10, -6)
      let lat = marker.lat * Math.pow(10, -6)
      const markerLayer = new T.Marker(new T.LngLat(lng, lat), {
        icon: new T.Icon({
          iconUrl: require(`@/assets/map/img/legend/${type}.png`),
          iconSize: new T.Point(42, 32), // 图标大小
          iconAnchor: new T.Point(21, 16), // 图标锚点，通常是图标中心
        }),
      })
      markerLayer.addEventListener('click', () => {
        // 鼠标点击事件
        if (callback) {
          callback({ data: marker, type, item: markerLayer })
        }
      })
      markerLayer.markerType = 'ship'
      markerLayer.shipType = type
      // 创建标记的文本标签
      const label = new T.Label({
        offset: new T.Point(-50, 40), // 偏移量，使标签在标记上方显示
        text: marker.name, // 显示标记的名称
        position: new T.LngLat(lng, lat), // 标签的位置
      })
      label.markerType = 'shipLabel'
      label.setBorderColor('transparent')
      label.setFontColor('#fff')
      label.setBackgroundColor('transparent')
      // 将文本标签添加到地图上
      map.addOverLay(label)
      map.addOverLay(markerLayer)
    })
  },

  /**
   * @description 展示信息弹窗
   * @param {*} markers
   * @param {*} callback
   */
  showTip(markerLayer, content) {
    const infoWindow = new T.InfoWindow(content, {
      minWidth: 120,
      maxWidth: 200,
      maxHeight: 300,
    })
    markerLayer.openInfoWindow(infoWindow)
    return infoWindow
  },
  /**
   * @description 关闭信息弹窗
   * @param {*} markers
   * @param {*} callback
   */
  clearTip(map = this.map, infoWindow) {
    map.removeOverLay(infoWindow)
  },
  /**
   * @description 清除标点
   */
  clearMarkers() {
    let overlays = this.map.getOverlays()
    // 遍历覆盖物数组，逐个移除标点
    // for (var i = 0; i < overlays.length; i++) {
    //   if (overlays[i] instanceof T.Marker) {
    //     map.removeOverLay(overlays[i])
    //   }
    // }
  },
  /**
   * @description 只渲染点位  数据 在业务上处理好  禁止在这里处理 数据
   * @param {*} callback
   * @returns
   */
  setPoits(e) {
    const [dolphin, callback, icon] = e
    console.log('setPoits', dolphin)
    dolphin.forEach((item) => {
      const marker = new T.Marker(new T.LngLat(item.lng, item.lat), {
        icon: icon,
      })
      marker.markerType = item.type
      marker.addEventListener('click', () => {
        // 鼠标点击事件
        if (callback) {
          callback(item, marker)
        }
      })
      this.map.addOverLay(marker)
    })
  },
  /**
   * @description 只渲染点位  数据 在业务上处理好  禁止在这里处理 数据
   * @param {*} callback
   * @returns
   */
  clearPoits(e) {
    const [type, callback] = e
    let overlays = this.map.getOverlays()
    if (type) {
      overlays.forEach((item) => {
        if (item.markerType == type) {
          this.map.removeOverLay(item)
        }
      })
    } else {
      overlays.forEach((item) => {
        this.map.removeOverLay(item)
      })
    }
  },
}
