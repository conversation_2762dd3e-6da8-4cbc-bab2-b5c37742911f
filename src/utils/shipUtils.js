let CanvasShipUtils = {}
CanvasShipUtils.dataToShip = function(sourceData, extendedData, isExtended) {
  let shipData = JSON.parse(JSON.stringify(sourceData)) // 拷贝一份避免改动原始数据
  if (shipData.ShipID) shipData.shipid = shipData.ShipID
  if (shipData.navistat) shipData.navistatus = shipData.navistat
  if (shipData.lasttime) shipData.lastdyn = shipData.lasttime
  if (shipData.From) shipData.source = shipData.From

  delete shipData.ShipID
  delete shipData.navistat
  delete shipData.lasttime
  delete shipData.From

  if (!shipData.shipid) shipData.shipid = shipData.mmsi
  if (!shipData.lng) {
    shipData.lng = shipData.lon
    delete shipData.lon
  }
  if (shipData.shiptype) shipData.type = shipData.shiptype
  if (shipData.rot > 0x4b0 || shipData.rot < -0x4b0) shipData.rot = undefined

  if (isExtended) {
    shipData.lng /= 0xf4240
    shipData.lat /= 0xf4240
    shipData.hdg = CanvasShipUtils.xTo360(CommUtils.getNumberLEValue(shipData.hdg, 0x8c3c, 0x64, 0x1))
    shipData.cog = CanvasShipUtils.xTo360(CommUtils.getNumberLEValue(shipData.cog, 0x8c96, 0x64, 0x1))
    shipData.sog = CommUtils.getNumberLEValue(shipData.sog, 0xcd60, 0x202, 0x1)
    shipData.rot = CommUtils.getNumber(shipData.rot / 0x64, 0x2)
    shipData.draught /= 0x3e8
    shipData.length /= 0xa
    shipData.width /= 0xa
    shipData.left /= 0xa
    shipData.trail /= 0xa
  } else {
    if (shipData.lng > 0x3e8) shipData.lng /= 0xf4240
    if (Math.abs(shipData.lat) > 0x3e8) shipData.lat /= 0xf4240
    if (Math.abs(shipData.hdg) > 0x3e8) {
      shipData.hdg = CanvasShipUtils.xTo360(CommUtils.getNumberLEValue(shipData.hdg, 0x8c3c, 0x64, 0x1))
    }
    if (shipData.cog > 0x3e8) {
      shipData.cog = CanvasShipUtils.xTo360(CommUtils.getNumberLEValue(shipData.cog, 0x8c96, 0x64, 0x1))
    }
    if (shipData.sog > 0x202) shipData.sog = CommUtils.getNumberLEValue(shipData.sog, 0xcd60, 0x202, 0x1)
    if (shipData.rot > 0x3e8) shipData.rot = CommUtils.getNumber(shipData.rot / 0x64, 0x2)
    if (shipData.draught > 0x3e8) shipData.draught = CommUtils.getNumber(shipData.draught / 0x3e8, 0x1)
  }

  if (extendedData && extendedData.color) shipData.color = extendedData.color
  if (extendedData && extendedData.istop !== undefined) shipData.istop = !extendedData.istop || extendedData.istop

  shipData.datapower = (shipData.mmsi + '').indexOf('*') > -1 ? 1 : 0

  shipData.gps_lat = shipData.lat
  shipData.gps_lng = shipData.lng
  shipData.gps_lastdyn = shipData.lastdyn

  return shipData
}

/**
 * 将角度转换为360度
 * @param {number} degrees
 * @returns {number}
 */
CanvasShipUtils.xTo360 = function(degrees) {
  if (degrees === undefined || degrees === null) {
    return degrees
  }
  degrees = Number(degrees)
  while (degrees > 360) {
    degrees -= 360
  }
  while (degrees < 0) {
    degrees += 360
  }
  return degrees
}

let CommUtils = {}
CommUtils.getNumberLEValue = function(value, maxValue, divisor, roundingMethod, defaultValue) {
  var _0x56b676 = void 0x0
  return CommUtils['isEmpty'](value) ? _0x56b676 : (CommUtils['isEmpty'](divisor) && (divisor = 0x1), CommUtils['isEmpty'](maxValue) && (maxValue = Number['MAX_VALUE']), (_0x56b676 = (_0x56b676 = Number(value)) <= maxValue ? CommUtils['getNumber'](_0x56b676 / divisor, roundingMethod, defaultValue) : void 0x0))
}
CommUtils.getNumber = function(value, defaultvalue, defaultMethod) {
  var result = 0x0
  if (CommUtils['isEmpty'](value)) return 0x0
  CommUtils['isEmpty'](defaultvalue) && (defaultvalue = 0x0), CommUtils['isEmpty'](defaultMethod) && (defaultMethod = 'floor')
  var _0x473d35 = Math['pow'](0xa, defaultvalue)
  switch ((_0x473d35 <= 0x0 && (_0x473d35 = 0x1), defaultMethod)) {
    case 'round':
      result = Math['round'](value * _0x473d35) / _0x473d35
      break
    case 'floor':
    default:
      result = Math['floor'](value * _0x473d35) / _0x473d35
      break
    case 'ceil':
      result = Math['ceil'](value * _0x473d35) / _0x473d35
  }
  return result
}
CommUtils.isEmpty = function(value) {
  return value === null || value === undefined || value.length === 0
}

function decryptData(rawData) {
  const processedData = JSON.parse(JSON.stringify(rawData))
  return CanvasShipUtils.dataToShip(processedData)
}

let shipTypeInfo = {
  20: '地效应船',
  21: '地效应船',
  22: '地效应船',
  23: '地效应船',
  24: '地效应船',
  25: '地效应船',
  26: '地效应船',
  27: '地效应船',
  28: '地效应船',
  29: '地效应船',
  30: '捕捞',
  31: '拖引',
  32: '拖引并且船长>200m或船宽>25m',
  33: '疏浚或水下作业',
  34: '潜水作业',
  35: '参与军事行动',
  36: '帆船航行',
  37: '游艇',
  40: '高速船',
  41: '高速船',
  42: '高速船',
  43: '高速船',
  44: '高速船',
  45: '高速船',
  46: '高速船',
  47: '高速船',
  48: '高速船',
  49: '高速船',
  50: '引航船',
  51: '搜救船',
  52: '拖轮',
  53: '港口供应船',
  54: '装有防污装置和设备的船舶',
  55: '执法艇',
  56: '备用-用于当地船舶的任务分配',
  57: '备用-用于当地船舶的任务分配',
  58: '医疗船',
  59: '符合18号决议(Mob-83)的船舶',
  60: '客船',
  61: '客船',
  62: '客船',
  63: '客船',
  64: '客船',
  65: '客船',
  66: '客船',
  67: '客船',
  68: '客船',
  69: '客船',
  70: '货船',
  71: '货船',
  72: '货船',
  73: '货船',
  74: '货船',
  75: '货船',
  76: '货船',
  77: '货船',
  78: '货船',
  79: '货船',
  80: '油轮',
  81: '油轮',
  82: '油轮',
  83: '油轮',
  84: '油轮',
  85: '油轮',
  86: '油轮',
  87: '油轮',
  88: '油轮',
  89: '油轮',
  90: '其他类型的船舶',
  91: '其他类型的船舶',
  92: '其他类型的船舶',
  93: '其他类型的船舶',
  94: '其他类型的船舶',
  95: '其他类型的船舶',
  96: '其他类型的船舶',
  97: '其他类型的船舶',
  98: '其他类型的船舶',
  99: '其他类型的船舶',
  100: '集装箱船'
}

function getShipType(shipType) {
  return shipTypeInfo.hasOwnProperty(Number(shipType)) ? shipTypeInfo[Number(shipType)] : '其他'
}

function getNaviStatus(t) {
  var array = ['在航(主机推动)', '锚泊', '失控', '操作受限', '吃水受限', '靠泊', '搁浅', '捕捞作业', '靠船帆提供动力']
  return t >= 0 && t <= 8 ? array[t] : ''
}

let config = {}
config.Flags = {
  201: 'ALB',
  202: 'AND',
  203: 'AUT',
  204: 'AZS',
  205: 'BEL',
  206: 'BLR',
  207: 'BGR',
  208: 'VAT',
  209: 'CYP',
  210: 'CYP',
  211: 'DEU',
  212: 'CYP',
  213: 'GEO',
  214: 'MDA',
  215: 'MLT',
  216: 'ARM',
  218: 'DEU',
  219: 'DNK',
  220: 'DNK',
  224: 'ESP',
  225: 'ESP',
  226: 'FRA',
  227: 'FRA',
  228: 'FRA',
  229: 'MLT',
  230: 'FIN',
  231: 'FRO',
  232: 'GBR',
  233: 'GBR',
  234: 'GBR',
  235: 'GBR',
  236: 'GIB',
  237: 'GRC',
  238: 'HRV',
  239: 'GRC',
  240: 'GRC',
  241: 'GRC',
  242: 'MAR',
  243: 'HUN',
  244: 'NLD',
  245: 'NLD',
  246: 'NLD',
  247: 'ITA',
  248: 'MLT',
  249: 'MLT',
  250: 'IRL',
  251: 'ISL',
  252: 'LIE',
  253: 'LUX',
  254: 'MCO',
  255: 'MDR',
  256: 'MLT',
  257: 'NOR',
  258: 'NOR',
  259: 'NOR',
  261: 'POL',
  262: 'MNE',
  263: 'PRT',
  264: 'ROU',
  265: 'SWE',
  266: 'SWE',
  267: 'SVK',
  268: 'SMR',
  269: 'SWZ',
  270: 'CZE',
  271: 'TUR',
  272: 'UKR',
  273: 'RUS',
  274: 'MKD',
  275: 'LVA',
  276: 'EST',
  277: 'LTU',
  278: 'SVN',
  279: 'SRB',
  301: 'AIA',
  303: 'USA',
  304: 'ATG',
  305: 'ATG',
  306: 'ANT',
  307: 'ABW',
  308: 'BHS',
  309: 'BHS',
  310: 'BMU',
  311: 'BHS',
  312: 'BLZ',
  314: 'BRB',
  316: 'CAN',
  319: 'CYM',
  321: 'CRI',
  323: 'CUB',
  325: 'DOM',
  327: 'DOM',
  329: 'GLP',
  330: 'GRD',
  331: 'GRL',
  332: 'GTM',
  334: 'HND',
  336: 'HTI',
  338: 'USA',
  339: 'JAM',
  341: 'KNA',
  343: 'LCA',
  345: 'MEX',
  347: 'MTQ',
  348: 'MSR',
  350: 'NIC',
  351: 'PAN',
  352: 'PAN',
  353: 'PAN',
  354: 'PAN',
  355: 'PAN',
  356: 'PAN',
  357: 'PAN',
  358: 'PRI',
  359: 'SLV',
  361: 'SPM',
  362: 'TTO',
  364: 'TCA',
  366: 'USA',
  367: 'USA',
  368: 'USA',
  369: 'USA',
  370: 'PAN',
  371: 'PAN',
  372: 'PAN',
  373: 'PAN',
  374: 'PAN',
  375: 'VCT',
  376: 'VCT',
  377: 'VCT',
  378: 'VGB',
  379: 'VGB',
  401: 'AFG',
  403: 'SAU',
  405: 'BGD',
  408: 'BHR',
  410: 'BTN',
  412: 'CHN',
  413: 'CHN',
  414: 'CHN',
  416: 'TWN',
  417: 'LKA',
  419: 'IND',
  422: 'IRN',
  423: 'AZE',
  425: 'IRQ',
  428: 'ISR',
  431: 'JPN',
  432: 'JPN',
  434: 'TKM',
  436: 'KAZ',
  437: 'UZB',
  438: 'JOR',
  440: 'KOR',
  441: 'KOR',
  443: 'PSE',
  445: 'PRK',
  447: 'KWT',
  450: 'LBN',
  451: 'KGZ',
  453: 'MAC',
  455: 'MDV',
  457: 'MNG',
  459: 'NPL',
  461: 'OMN',
  463: 'PAK',
  466: 'QAT',
  468: 'SYR',
  470: 'ARE',
  471: 'ARE',
  473: 'YEM',
  475: 'YEM',
  477: 'HKG',
  478: 'BIH',
  501: 'ADL',
  503: 'AUS',
  506: 'MMR',
  508: 'BRN',
  510: 'FSM',
  511: 'PLW',
  512: 'NZL',
  514: 'KHM',
  515: 'KHM',
  516: 'CXR',
  518: 'COK',
  520: 'FJI',
  523: 'CCK',
  525: 'IDN',
  529: 'KIR',
  531: 'LAO',
  533: 'MYS',
  536: 'MNP',
  538: 'MHL',
  540: 'NCL',
  542: 'NIU',
  544: 'NRU',
  546: 'PYF',
  548: 'PHL',
  550: 'TLS',
  553: 'PNG',
  555: 'PCN',
  557: 'SLB',
  559: 'ASM',
  561: 'WSM',
  563: 'SGP',
  564: 'SGP',
  565: 'SGP',
  566: 'SGP',
  567: 'THA',
  570: 'TON',
  572: 'TUV',
  573: 'VNM',
  574: 'VNM',
  576: 'VUT',
  577: 'VUT',
  578: 'WLF',
  601: 'ZAF',
  603: 'AGO',
  605: 'DZA',
  607: 'ATF',
  608: 'ASL',
  609: 'BDI',
  610: 'BEN',
  611: 'BWA',
  612: 'CAF',
  613: 'CMR',
  615: 'COG',
  616: 'COM',
  617: 'CPV',
  618: 'ATF',
  619: 'CIV',
  621: 'DJI',
  622: 'EGY',
  624: 'ETH',
  625: 'ERI',
  626: 'GAB',
  627: 'GHA',
  629: 'GMB',
  630: 'GNB',
  631: 'GNQ',
  632: 'GIN',
  633: 'BFA',
  634: 'KEN',
  635: 'ATF',
  636: 'LBR',
  637: 'LBR',
  642: 'LBY',
  644: 'LSO',
  645: 'MUS',
  647: 'MDG',
  649: 'MLI',
  650: 'MOZ',
  654: 'MRT',
  655: 'MWI',
  656: 'NER',
  657: 'NGA',
  659: 'NAM',
  660: 'REU',
  661: 'RWA',
  662: 'SDN',
  663: 'SEN',
  664: 'SYC',
  665: 'SHN',
  666: 'SOM',
  667: 'SLE',
  668: 'STP',
  669: 'SWZ',
  670: 'TCD',
  671: 'TGO',
  672: 'TUN',
  674: 'TZA',
  675: 'UGA',
  676: 'COG',
  677: 'TZA',
  678: 'ZMB',
  679: 'ZWE',
  701: 'ARG',
  710: 'BRA',
  720: 'BOL',
  725: 'CHL',
  730: 'COL',
  735: 'ECU',
  740: 'FLK',
  745: 'GIN',
  750: 'GUY',
  755: 'PRY',
  760: 'PER',
  765: 'SUR',
  770: 'URY',
  775: 'VEN',
  472: 'TJK',
  620: 'COM',
  638: 'SSD'
}
config.falgCoutry = {
  MDR: '葡属马德拉群岛',
  ALA: '奥兰群岛',
  AFG: '阿富汗',
  ALB: '阿尔巴尼亚',
  DZA: '阿尔及利亚',
  ASM: '美属萨摩亚',
  AND: '安道尔',
  AGO: '安哥拉',
  AIA: '安圭拉',
  ATA: '南极洲',
  ATG: '安提瓜和巴布达',
  ARG: '阿根廷',
  ARM: '亚美尼亚',
  ABW: '阿鲁巴',
  AUS: '澳大利亚',
  AUT: '奥地利',
  AZE: '阿塞拜疆',
  BHS: '巴哈马',
  BHR: '巴林',
  BGD: '孟加拉',
  BRB: '巴巴多斯',
  BLR: '白俄罗斯',
  BEL: '比利时',
  BLZ: '伯利兹',
  BEN: '贝宁',
  BMU: '百慕大',
  BTN: '不丹',
  BOL: '玻利维亚',
  BIH: '波斯尼亚和黑塞哥维那',
  BWA: '博茨瓦纳',
  BVT: '布维岛',
  BRA: '巴西',
  IOT: '英属印度洋领地',
  BRN: '文莱达鲁萨兰国',
  BGR: '保加利亚',
  BFA: '布基纳法索',
  BDI: '布隆迪',
  KHM: '柬埔寨',
  CMR: '喀麦隆',
  CAN: '加拿大',
  CPV: '佛得角',
  CYM: '开曼群岛',
  CAF: '中非共和国',
  TCD: '乍得',
  CHL: '智利',
  CHN: '中国',
  CXR: '圣诞岛',
  CCK: '科科斯（基林）群岛',
  COL: '哥伦比亚',
  COM: '科摩罗',
  COD: '刚果民主共和国',
  COG: '刚果共和国',
  COK: '库克群岛',
  CRI: '哥斯达黎加',
  CIV: '科特迪瓦',
  HRV: '克罗地亚',
  CUB: '古巴',
  CYP: '塞浦路斯',
  CZE: '捷克共和国',
  DNK: '丹麦',
  DJI: '吉布提',
  DMA: '多米尼加联邦',
  DOM: '多米尼加共和国',
  ECU: '厄瓜多尔尔',
  EGY: '埃及',
  SLV: '萨尔瓦多',
  GNQ: '赤道几内亚',
  ERI: '厄立特里亚',
  EST: '爱沙尼亚',
  ETH: '埃塞俄比亚',
  FLK: '福克兰群岛（马尔维纳斯）',
  FRO: '法罗群岛',
  FJI: '斐济',
  FIN: '芬兰',
  FRA: '法国',
  GUF: '法属圭亚那',
  PYF: '法属波利尼西亚',
  ATF: '法属南部领土',
  GAB: '加蓬',
  GMB: '冈比亚',
  GEO: '格鲁吉亚',
  DEU: '德国',
  GHA: '加纳',
  GIB: '直布罗陀',
  GRC: '希腊',
  GRL: '格陵兰',
  GRD: '格林纳达',
  GLP: '瓜德罗普',
  GUM: '关岛',
  GTM: '危地马拉',
  GIN: '几内亚',
  GNB: '几内亚比绍',
  GUY: '圭亚那',
  HTI: '海地',
  HMD: '赫德岛和麦克唐纳岛',
  HND: '洪都拉斯',
  HKG: '中国香港',
  HUN: '匈牙利',
  ISL: '冰岛',
  IND: '印度',
  IDN: '印度尼西亚',
  IRN: '伊朗伊斯兰共和国',
  IRQ: '伊拉克',
  IRL: '爱尔兰',
  ISR: '以色列',
  ITA: '意大利',
  JAM: '牙买加',
  JPN: '日本',
  JOR: '约旦',
  KAZ: '哈萨克斯坦',
  KEN: '肯尼亚',
  KIR: '基里巴斯',
  PRK: '朝鲜民主主义人民共和国',
  KOR: '大韩民国',
  KWT: '科威特',
  KGZ: '吉尔吉斯斯坦',
  LAO: '老挝人民民主共和国',
  LVA: '拉脱维亚',
  LBN: '黎巴嫩',
  LSO: '莱索托',
  LBR: '利比里亚',
  LBY: '阿拉伯利比亚民众国',
  LIE: '列支敦士登',
  LTU: '立陶宛',
  LUX: '卢森堡',
  MAC: '中国澳门',
  MKD: '前南斯拉夫的马其顿共和国',
  MDG: '马达加斯加',
  MWI: '马拉维',
  MYS: '马来西亚',
  MDV: '马尔代夫',
  MLI: '马里',
  MLT: '马耳他',
  MHL: '马绍尔群岛',
  MTQ: '马提尼克',
  MRT: '毛里塔尼亚',
  MUS: '毛里求斯',
  MYT: '马约特',
  MEX: '墨西哥',
  FSM: '密克罗尼西亚联邦',
  MDA: '摩尔多瓦共和国',
  MCO: '摩纳哥',
  MNG: '蒙古',
  MSR: '蒙特塞拉特',
  MAR: '摩洛哥',
  MOZ: '莫桑比克',
  MMR: '缅甸',
  NAM: '纳米比亚',
  NRU: '瑙鲁',
  NPL: '尼泊尔',
  NLD: '荷兰',
  ANT: '荷属安的列斯',
  NCL: '新喀里多尼亚',
  NZL: '新西兰',
  NIC: '尼加拉瓜',
  NER: '尼日尔尔',
  NGA: '尼日尔利亚',
  NIU: '纽埃',
  NFK: '诺福克岛',
  MNP: '北马里亚纳群岛',
  NOR: '挪威',
  OMN: '阿曼',
  PAK: '巴基斯坦',
  PLW: '帕劳',
  PSE: '巴勒斯坦',
  PAN: '巴拿马',
  PNG: '巴布亚新几内亚',
  PRY: '巴拉圭',
  PER: '秘鲁',
  PHL: '菲律宾',
  PCN: '皮特凯恩群岛',
  POL: '波兰',
  PRT: '葡萄牙',
  PRI: '波多黎各',
  QAT: '卡塔尔',
  REU: '留尼汪',
  ROU: '罗马尼亚',
  RUS: '俄罗斯',
  RWA: '卢旺达',
  SHN: '圣赫勒拿',
  KNA: '圣基茨和尼维斯',
  LCA: '圣卢西亚',
  SPM: '圣皮埃尔和密克隆',
  VCT: '圣文森特和格林纳丁斯',
  WSM: '萨摩亚',
  SMR: '圣马力诺',
  STP: '圣多美和普林西比',
  SAU: '沙特阿拉伯',
  SEN: '塞内加尔',
  SCG: '塞尔维亚和黑山',
  SYC: '塞舌尔',
  SLE: '塞拉利昂',
  SGP: '新加坡',
  SVK: '斯洛伐克',
  SVN: '斯洛文尼亚',
  SLB: '所罗门群岛',
  SOM: '索马里',
  ZAF: '南非',
  SGS: '南乔治亚岛和南桑德韦奇岛',
  ESP: '西班牙',
  LKA: '斯里兰卡',
  SDN: '苏丹',
  SUR: '苏里南',
  SJM: '斯瓦尔巴岛和扬马延岛',
  SWZ: '斯威士兰',
  SWE: '瑞典',
  CHE: '瑞士',
  SYR: '阿拉伯叙利亚共和国',
  TWN: '中国台湾',
  TJK: '塔吉克斯坦共和国',
  TZA: '坦桑尼亚联合共和国',
  THA: '泰国',
  TGO: '多哥',
  TKL: '托克劳',
  TON: '汤加',
  TTO: '特立尼达和多巴哥',
  TUN: '突尼斯',
  TUR: '土耳其',
  TKM: '土库曼斯坦',
  TCA: '特克斯和凯科斯群岛',
  TUV: '图瓦卢',
  UGA: '乌干达',
  UKR: '乌克兰',
  ARE: '阿拉伯联合酋长国',
  GBR: '英国',
  USA: '美国',
  UMI: '美国边远小岛',
  URY: '乌拉圭',
  UZB: '乌兹别克斯坦',
  VUT: '瓦努阿图',
  VAT: '梵蒂冈（梵蒂冈城国）',
  VEN: '委内瑞拉',
  VNM: '越南',
  VGB: '英属维尔京群岛',
  VIR: '美属维尔京群岛',
  WLF: '瓦利斯和富图纳群岛',
  ESH: '西撒哈拉',
  YEM: '也门',
  ZMB: '赞比亚',
  ZWE: '津巴布韦',
  CUW: '库拉索',
  SSD: '南苏丹',
  TLS: '东帝汶民主共和国'
}

function Fixed(input, length, chart) {
  var ret = input.toString().slice(0, length)
  for (var i = 0; i < length - ret.length; i++) {
    ret = chart + ret
  }
  return ret
}

function latNe(lat) {
  if (Math.abs(lat) > 90) return ''

  var _latNe = String(lat)

  if (_latNe.lastIndexOf('.') == -1) {
    //如果数值没有小数点，则补充三个0
    _latNe = _latNe + '.000'
  }

  var tmpindex = _latNe.lastIndexOf('.')
  var preNum = Number(_latNe.substring(0, tmpindex))
  var lastNum = Number('0.' + _latNe.substring(tmpindex + 1, _latNe.length)) * 60
  if (Math.abs(preNum) > 90)
    //纬度大于 90
    return 'invalid  position'

  if (lat >= 0) {
    return preNum.toString() + '-' + Fixed(lastNum.toFixed(3), 6, '0') + 'N'
  } else {
    return Math.abs(preNum) + '-' + Fixed(lastNum.toFixed(3), 6, '0') + 'S'
  }
}

function lngNe(lng) {
  lng = lng > 180 ? lng - 360 : lng
  if (Math.abs(lng) > 180) return ''

  var _lngNe = String(lng)

  if (_lngNe.lastIndexOf('.') == -1) {
    //如果数值没有小数点，则补充三个0
    _lngNe = _lngNe + '.000'
  }

  var tmpindex = _lngNe.lastIndexOf('.')
  var preNum = Number(_lngNe.substring(0, tmpindex))
  var lastNum = Number('0.' + _lngNe.substring(tmpindex + 1, _lngNe.length)) * 60
  if (Math.abs(preNum) > 180)
    //纬度大于 180
    return 'invalid  position'
  if (lng >= 0) {
    return preNum.toString() + '-' + Fixed(lastNum.toFixed(3), 6, '0') + 'E '
  } else {
    return Math.abs(preNum) + '-' + Fixed(lastNum.toFixed(3), 6, '0') + 'W '
  }
}

var Fn = {}
Fn.getTime = function(s) {
  return Math.floor(new Date(s.replace(/-/g, '/')).getTime() / 1000)
}

Fn.formatTime = function(time, seconds, len) {
  if (!time) time = new Date().getTime() / 1000
  if (typeof time == 'string') time = new Date(time.replace(/-/g, '/')).getTime() / 1000
  var t = new Date((time + (seconds || 0)) * 1000), t = {
    y: t.getFullYear(), m: t.getMonth() + 1, d: t.getDate(), H: t.getHours(), M: t.getMinutes(), S: t.getSeconds()
  }
  for (var k in t) if (t[k] < 10) t[k] = '0' + t[k]
  t = [t.y, '-', t.m, '-', t.d, ' ', t.H, ':', t.M, ':', t.S].join('')
  if (len) t = t.substr(0, len)
  return t
}

function formatShip(d) {
  var data = d
  if (typeof data == 'string') data = json(data)
  if (!data) return

  data.shipName = decodeURIComponent(data.name)
  data.shipType = getShipType(data.type)
  data.shipStatus = getNaviStatus(data.navistatus)
  if (data.imo == '2147483647' || data.imo == '0' || (data.imo + '').length != 7) data.imo = '-' //无效的IMO
  data.mmsi = data.mmsi + ''
  if (data.mmsi == '-1' || data.mmsi == '2147483647' || data.mmsi.length > 12) data.mmsi = '' //无效的MMSI
  if (data.callsign == '0') data.callsign = '' //无效呼号
  data.mmsiFlag = ''
  data.flagImg = ''
  var head3MMSI = data.mmsi.substr(0, 3), flag = config.Flags[head3MMSI]
  if (flag == 'TWN' || data.mmsi.length != 9) flag = ''
  if (flag) {
    data.mmsiFlag = config.falgCoutry[flag] || ''
    data.flagImg = 'http://api.shipxy.com/apiresource/flags/' + flag + '.png'
  }
  data.boxtitle = data.name || data.callsign || data.mmsi || ''
  data.dest = data.dest.replace(/%/g, '')
  data.dest = decodeURIComponent(data.dest || '').trim()
  data.dest = data.dest.replace(/\</g, '&lt;').replace(/\>/g, '&gt;')
  data.dest = data.dest == '' ? '-' : data.dest
  data.eta = (data.eta || '').trim()
  if (!/(\d+)\-(\d+) (\d+):(\d+)/.test(data.eta)) data.eta = ''
  //eta标准化
  //tip: 优化 eta 取值逻辑 2022-07-22
  var eta = data.eta //3-15 02:00
  if (eta) {
    eta = eta.replace('.', '-') //3-15 02:00
    var year = new Date(data.laststa * 1e3).getFullYear()
    var preDateStr = year - 1 + '-' + eta
    var nowDateStr = year + '-' + eta
    var nextDateStr = year + 1 + '-' + eta
    var nUTC = Fn.getTime(Fn.formatTime(data.laststa))
    var aUTC = Math.abs(Fn.getTime(preDateStr) - nUTC)
    var bUTC = Math.abs(Fn.getTime(nowDateStr) - nUTC)
    var cUTC = Math.abs(Fn.getTime(nextDateStr) - nUTC)
    var dUTC
    var min
    if (aUTC >= bUTC) {
      min = Fn.formatTime(Fn.getTime(nowDateStr))
      dUTC = bUTC
    } else {
      min = Fn.formatTime(Fn.getTime(preDateStr))
      dUTC = aUTC
    }
    if (dUTC > cUTC) {
      min = Fn.formatTime(Fn.getTime(nextDateStr))
    }
    data._eta = min
  }
  data.heading = data.hdg
  data.course = data.cog
  if (!data.heading) data.heading = -1
  if (!data.course) data.course = -1

  data.heading_f = data.heading < 0 || data.heading > 360 ? '未知' : parseFloat(data.heading).toFixed(1) + '度'
  data.course_f = data.course < 0 || data.course > 360 ? '未知' : parseFloat(data.course).toFixed(1) + '度'
  data.lon = data.lng //新平台 lng ,老的lon
  data._lat = latNe(data.gps_lat || data.lat)
  data._lon = lngNe(data.gps_lng || data.lng)

  if (data.sog) {
    if (data.sog * 1 < 102.2) {
      data._sog = (data.sog * 1).toFixed(1) + '节'
    } else {
      data._sog = '-'
    }
  } else {
    data._sog = '0节'
  }

  if (data.length) data._length = data.length + '米'
  if (data.width) data._width = data.width + '米'
  if (data.draught) data._draught = data.draught / 1000 + '米'
  if (!data.mmsi) {
    data.heading_f = ''
    data.course_f = ''
  }
  var con1 = data.OBCTime > data.lastTime //卫星时间大于更新时间
  //var con2 = (data.OBCTime + (48 * 3600) > (new Date(ServerTime.replace(/-/g, '/')).getTime() / 1000)); //48小时内卫星AIS有更新
  //var show = !window.hasOBCRight && data.OBCTime && (con1 || con2); //是否显示卫星AIS提示
  //var show = data.buyOBC; //是否显示卫星AIS提示
  //var lasttime = data.lastdyn > data.laststa ? data.lastdyn : data.laststa//7.17
  var lasttime = data.lastdyn //7.17 改为只取动态数据
  var obctime = 0
  if (data.satelliteutc && lasttime != 0) {
    obctime = data.satelliteutc > lasttime ? data.satelliteutc : 0
  }
  data.lastTime = lasttime == 0 ? '' : Fn.formatTime(lasttime)
  data.OBCTime = obctime == 0 ? 0 : Fn.formatTime(obctime)
  //网位仪
  data.is_netsonde = false // Menu.hashWWY[data.mmsi] ? true : false;
  data.isShowLableTime = false // Menu.hashWWY[data.mmsi] ? false : data.isShowLableTime;
  return data
}

function createForm(mmsi) {
  var str = ''
  str += '			    <table id="shipAIS">                                                                                                                                                                                                   '
  str += '			    	<tr>                                                                                                                                                                                                  '
  str += '                        <th width="50" id="mmsiInfo" >MMSI：</th>'
  str += '                        <td><span id="si_mmsi" style="display:inline-block;line-height:24px;"></span><span id="si_mmsiFlag" style="display:none">-</span><img src="/Content/images/rzpass.svg"  title="该船舶已通过证书认证，MMSI与船名真实可靠" id="RZSign" style="display:none;width:20px;margin-left:5px;" /></td>                                                                                                                                                               '
  str += '                        <th width="60" >船首向：</th>                                                                                                                                                                     '
  str += '                        <td id="si_heading_f">-</td>                                                                                                                                                                                    '
  str += '			    	</tr>                                                                                                                                                                                                 '
  str += '			    	<tr>                                                                                                                                                                                                  '
  str += '			    		<th>呼号：</th>                                                                                                                                                                                   '
  str += '			    		<td id="si_callsign">-</td>                                                                                                                                                                                     '
  str += '			    		<th>航向：</th>                                                                                                                                                                                 '
  str += '			    		<td id="si_course_f">-</td>                                                                                                                                                                                  '
  str += '			    	</tr>                                                                                                                                                                                                 '
  str += '			    	<tr>                                                                                                                                                                                                  '
  str += '			    		<th>IMO：</th>                                                                                                                                                                                    '
  str += '			    		<td id="si_imo">-</td>                                                                                                                                                                                  '
  str += '			    		<th>航速：</th>                                                                                                                                                                                   '
  str += '			    		<td id="si__sog">-</td>                                                                                                                                                                                    '
  str += '			    	</tr>                                                                                                                                                                                                 '
  str += '			    	<tr>                                                                                                                                                                                                  '
  str += '			    		<th>类型：</th>                                                                                                                                                                                   '
  str += '			    		<td id="si_shipType">-</td>                                                                                                                                                                                   '
  str += '			    		<th>纬度：</th>                                                                                                                                                                                   '
  str += '			    		<td id="si__lat"></td>                                                                                                                                                                               '
  str += '			    	</tr>                                                                                                                                                                                                 '
  str += '			    	<tr>                                                                                                                                                                                                  '
  str += '			    		<th>状态：</th>                                                                                                                                                                                   '
  str += '			    		<td id="si_shipStatus">-</td>                                                                                                                                                                                     '
  str += '			    		<th>经度：</th>                                                                                                                                                                                   '
  str += '			    		<td id="si__lon">-</td>                                                                                                                                                                              '
  str += '			    	</tr>                                                                                                                                                                                                 '
  str += '			    	<tr>                                                                                                                                                                                                  '
  str += '			    		<th>船长：</th>                                                                                                                                                                                   '
  str += '			    		<td id="si__length">-</td>                                                                                                                                                                                     '
  str += '			    		<th>目的地：</th>                                                                                                                                                                                 '
  str += '			    		<td id="si_dest">-</td>                                                                                                                                                                                '
  str += '			    	</tr>                                                                                                                                                                                                 '
  str += '			    	<tr>                                                                                                                                                                                                  '
  str += '			    		<th>船宽：</th>                                                                                                                                                                                   '
  str += '			    		<td id="si__width">-</td>                                                                                                                                                                                     '
  str += '			    		<th>预到时间：</th>                                                                                                                                                                               '
  str += '			    		<td id="si__eta">-</td>                                                                                                                                                                      '
  str += '			    	</tr>                                                                                                                                                                                                 '
  str += '			    	<tr>                                                                                                                                                                                                  '
  str += '			    		<th>吃水：</th>                                                                                                                                                                                   '
  str += '			    		<td id="si__draught">-</td>                                                                                                                                                                                     '
  str += '			    		<th>更新时间：</th>                                                                                                                                                                               '
  str += '			    		<td id="si_lastTime">-</td>                                                                                                                                                                      '
  str += '			    	</tr>                                                                                                                                                                                                 '
  str += '			    </table> '
  var div = document.createElement('div')
  div.innerHTML = str
  document.body.appendChild(div)
  console.log('创建div', div)
}

/**
 *
 * @param ship 解码后的数据
 * @param _dd  //原始数据
 * @param _mmsi //船id
 * @returns {*}
 */
function aisDomInfo(ship, _dd, _mmsi) {
  ship['dest'] = _dd.dest
  ship['draught'] = _dd.draught
  ship['eta'] = _dd.eta
  ship['mmsi'] = _dd.mmsi
  ship['imo'] = _dd.imo
  ship['callsign'] = _dd.callsign
  ship['name'] = _dd.name.replace(/%/g, '')
  ship['lastdyn'] = _dd.lastdyn
  ship['laststa'] = _dd.laststa
  ship['source'] = _dd.source
  ship['type'] = _dd.type
  ship['length'] = (_dd.length === 10220 ? 0 : _dd.length) / 10
  ship['width'] = (_dd.width === 1260 ? 0 : _dd.width) / 10
  ship['satelliteutc'] = _dd.satelliteutc //20190307,未登录用户点击卫星船没有提示购买
  ship['lat'] = _dd.lat / 1000000 //
  ship['lng'] = _dd.lon / 1000000 //
  ship['lon'] = _dd.lon / 1000000 //20190806，应隐藏区域船新增
  ship['navistatus'] = _dd.navistatus //20190918，状态
  var data_f = formatShip(ship)
  // for (var k in data_f) {
  //   if (typeof k == 'string' && (c = document.getElementById('si_' + k))) {
  //     d = data_f[k]
  //     if (k === 'flagImg') {
  //       //国旗
  //       c.style.display = ''
  //       $('#si_' + k + '').attr('src', d)
  //       if (!c.getAttribute('src')) {
  //         c.style.display = 'none'
  //         $('.ship_title').css('margin-left', '10px')
  //       } else {
  //         $('.ship_title').css('margin-left', '45px')
  //       }
  //       continue
  //     }
  //     c.title = d
  //     c.innerHTML = d
  //   }
  // }
  return data_f
}

export function transShipDataType(record) {
  let ship = decryptData(record)
  var data_f = aisDomInfo(ship, record, record.mmsi)
  return data_f
}

/*
let record = {
  source: 0,
  mmsi: '413235611',
  shipid: 'CE66EC0AECA391BC',
  tradetype: 99,
  type: 70,
  imo: '0',
  name: 'GUANG XING 298',
  matchtype: 1,
  cnname: '广星298',
  callsign: '',
  length: 800,
  width: 180,
  left: 90,
  trail: 770,
  draught: 4000,
  dest: 'ZHANJIANG,CN',
  eta: '07-16 16:00',
  laststa: 1658000169,
  lon: 152534282,
  lat: -85841733,
  sog: 29683,
  cog: 40480,
  hdg: 12600,
  rot: 0,
  navistatus: 8,
  lastdyn: 1667840650,
  satelliteutc: 0
} //调用示例
let ship = decryptData(record)
console.log('解码后', ship)
// source = getShipType(100)
createForm(record.mmsi)
var data_f = aisDomInfo(ship, record, record.mmsi)
console.log(JSON.stringify(data_f))
*/
