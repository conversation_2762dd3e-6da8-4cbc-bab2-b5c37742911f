//Gis 封装公共方法

// 获取  geojson数据
export function getData() {
  // 如果存在就返回 数据 不存在就放一个空数组
  if (!localStorage.getItem('geojson')) {
    localStorage.setItem('geojson', '[]')
  }
  // localStorage 存储的是字符串
  return JSON.parse(localStorage.getItem('geojson'))
}

// 存储 geojson数据
export function saveData(data) {
  // console.log('存入数据', data);
  localStorage.setItem('geojson', JSON.stringify(data))
}
// export function saveData(data) {
// 	// 存下 {'rectangle': [[1,2,3,4]]}
// 	// 做个逻辑处理 就是之前有数据 就把之前的数据拿到  然后跟现在的数据合并 然后存入，
// 	let geodata = JSON.parse(localStorage.getItem('geojson'))
// 	if (geodata && Object.keys(data)[0] in geodata) {
// 		geodata[Object.keys(data)[0]].push(data[Object.keys(data)[0]][0])
// 		localStorage.setItem('geojson', JSON.stringify(geodata))
// 	} else {
// 		localStorage.setItem('geojson', JSON.stringify(data))
// 	}
// }
