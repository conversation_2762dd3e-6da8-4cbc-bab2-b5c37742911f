import { UserLayout, TabLayout, RouteView, BlankLayout, PageView } from '@/components/layouts'

/**
 * 走菜单，走权限控制
 * @type {[null,null]}
 */
export const asyncRouterMap = [
  {
    path: '/',
    name: 'dashboard',
    component: TabLayout,
    meta: { title: '首页' },
    redirect: '/dashboard/analysis',
    children: []
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
]

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Login')
      },
      {
        path: 'register',
        name: 'register',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/register/Register')
      },
      {
        path: 'register-result',
        name: 'registerResult',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/register/RegisterResult')
      },
      {
        path: 'alteration',
        name: 'alteration',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/alteration/Alteration')
      }
    ]
  },
  {
    path: '/test',
    component: BlankLayout,
    redirect: '/test/test6',
    children: [
      // {
      //   path: 'home',
      //   name: 'TestHome',
      //   component: () => import('@/views/Home')
      // },
      // {
      //   path: 'test5',
      //   name: 'TestTest5',
      //   component: () => import('@/views/basic/test/test5')
      // },
      // {
      //   path: 'test6',
      //   name: 'TestTest6',
      //   component: () => import('@/views/basic/ProjectPbsList.vue')
      // },
      // {
      //   path: 'test7',
      //   name: 'TestTest7',
      //   component: () => import('@/views/modules/project/projectCamera/Index.vue')
      // }
    ]
  },
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404')
  }
]
