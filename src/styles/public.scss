// 以下是遍历
/* ============================ 圆角 ============================ */
@for $i from 0 through 100 {
  .rounded-#{$i} {
    border-radius: $i + px;
  }
}

// 定位 遍历1-200
@for $i from 0 through 500 {
  .top-#{$i} {
    top: $i + px;
  }
  .bottom-#{$i} {
    bottom: $i + px;
  }
  .left-#{$i} {
    left: $i + px;
  }
  .right-#{$i} {
    right: $i + px;
  }
}

/* ============================ 内外边距 遍历1-500 ============================ */
@for $i from 0 through 200 {
  .m-#{$i} {
    margin: $i + px;
  }
  .mt-#{$i} {
    margin-top: $i + px;
  }
  .mr-#{$i} {
    margin-right: $i + px;
  }
  .mb-#{$i} {
    margin-bottom: $i + px;
  }
  .ml-#{$i} {
    margin-left: $i + px;
  }
  .mx-#{$i} {
    margin-left: $i + px;
    margin-right: $i + px;
  }
  .my-#{$i} {
    margin-top: $i + px;
    margin-bottom: $i + px;
  }
  .p-#{$i} {
    padding: $i + px;
  }
  .pt-#{$i} {
    padding-top: $i + px;
  }
  .pr-#{$i} {
    padding-right: $i + px;
  }
  .pb-#{$i} {
    padding-bottom: $i + px;
  }
  .pl-#{$i} {
    padding-left: $i + px;
  }
  .px-#{$i} {
    padding-left: $i + px;
    padding-right: $i + px;
  }
  .py-#{$i} {
    padding-top: $i + px;
    padding-bottom: $i + px;
  }
}
/* ============================ 宽高 ============================ */
// 宽度/最大宽度 遍历1-3000
@for $i from 0 through 3000 {
  .w-#{$i} {
    width: $i + px;
  }
  .maxw-#{$i} {
    max-width: $i + px;
  }
  .minw-#{$i} {
    min-width: $i + px;
  }
}

// 高度/最大高度 遍历1-3000
@for $i from 0 through 3000 {
  .h-#{$i} {
    height: $i + px;
  }
  .maxh-#{$i} {
    max-height: $i + px;
  }
  .minh-#{$i} {
    min-height: $i + px;
  }
}

// VW -- 高度/最大高度 遍历1-100
@for $i from 0 through 100 {
  .vw-#{$i} {
    width: $i + vw;
  }
  .mvw-#{$i} {
    max-width: $i + vw;
  }
}
// VH -- 高度/最大高度 遍历1-100
@for $i from 0 through 100 {
  .vh-#{$i} {
    height: $i + vh;
  }
  .mvh-#{$i} {
    max-height: $i + vh;
  }
}

/* ============================ 文本 ============================*/
@for $i from 10 through 64 {
  .font-#{$i} {
    font-size: $i + px;
  }
  .line-#{$i} {
    line-height: $i + px;
  }
}

@for $i from 0 through 9999 {
  .index-#{$i} {
    z-index: $i;
  }
}
@for $i from 1 through 10 {
  .flex-#{$i} {
    flex: $i;
  }
}
