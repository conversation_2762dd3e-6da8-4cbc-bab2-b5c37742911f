<svg width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 4">
<rect id="Rectangle 13" width="8" height="8" rx="1" fill="#97B4FF"/>
<g id="Rectangle 14" filter="url(#filter0_d_107_711)">
<rect x="4" y="4" width="10" height="10" rx="1" fill="#3254FF"/>
</g>
</g>
<defs>
<filter id="filter0_d_107_711" x="2" y="4" width="14" height="14" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.196078 0 0 0 0 0.329412 0 0 0 0 1 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_107_711"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_107_711" result="shape"/>
</filter>
</defs>
</svg>
