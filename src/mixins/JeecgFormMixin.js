/**
 * 新增修改完成调用 modalFormOk方法 编辑弹框组件ref定义为modalForm
 * 高级查询按钮调用 superQuery方法  高级查询组件ref定义为superQueryModal
 * data中url定义 list为查询列表  delete为删除单条记录  deleteBatch为批量删除
 */
import Vue from 'vue'
import { ACCESS_TOKEN, TENANT_ID } from '@/store/mutation-types'
import store from '@/store'
import { Modal } from 'ant-design-vue'

export const JeecgTreeListMixin = {
  mixins: [],
  data() {
    return {}
  },
  created() {},
  computed: {},
  methods: {
  }
}
