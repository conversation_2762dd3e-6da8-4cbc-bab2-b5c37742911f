/**
 * 新增修改完成调用 modalFormOk方法 编辑弹框组件ref定义为modalForm
 * 高级查询按钮调用 superQuery方法  高级查询组件ref定义为superQueryModal
 * data中url定义 list为查询列表  delete为删除单条记录  deleteBatch为批量删除
 */
import { filterObj } from '@/utils/util'
import { deleteAction, getAction, downFile, getFileAccessHttpUrl } from '@/api/manage'
import Vue from 'vue'
import { ACCESS_TOKEN, TENANT_ID } from '@/store/mutation-types'
import store from '@/store'
import { Modal } from 'ant-design-vue'
import TableLayout from '@/components/tableLayout/TableLayout.vue'

export const JeecgListMixin = {
  components: { TableLayout },
  data() {
    return {
      /* 查询条件-请不要在queryParam中声明非字符串值的属性 */
      queryParam: {},
      /* 数据源 */
      dataSource: [],
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '50', '100'],
        showTotal: (total) => {
          return ' 共' + total + '条'
        },
        showSizeChanger: true,
        total: 0,
      },
      /* 排序参数 */
      isorter: {
        column: 'createTime',
        order: 'desc',
      },
      /* 筛选参数 */
      filters: {},
      /* table加载状态 */
      loading: false,
      /* table选中keys */
      selectedRowKeys: [],
      /* table选中records */
      selectionRows: [],
      /* 查询折叠 */
      toggleSearchStatus: false,
      /* 高级查询条件生效状态 */
      superQueryFlag: false,
      /* 高级查询条件 */
      superQueryParams: '',
      /** 高级查询拼接方式 */
      superQueryMatchType: 'and',
    }
  },
  created() {
    this.onInit() // 初始化前，如果需要做其他处理在扩展方法中覆盖此方法
    if (!this.disableMixinCreated) {
      console.log('mixin-created')
      this.loadData()
      // 初始化字典配置 在自己页面定义
      this.initDictConfig()
    }
    this.onInited() // 初始化前，如果需要做其他处理在扩展方法中覆盖此方法
  },
  computed: {
    // token header
    tokenHeader() {
      let head = { 'X-Access-Token': Vue.ls.get(ACCESS_TOKEN) }
      let tenantid = Vue.ls.get(TENANT_ID)
      if (tenantid) {
        head['tenant_id'] = tenantid
      }
      return head
    },
    // 列表列配置
    tableColumns() {
      return this.columns || this.tableProps.columns
    },
  },
  methods: {
    onInit() {
      console.log('mixin-onInit===>Create执行前')
    },
    onInited() {
      console.log('mixin-onInited===>Create执行后')
    },
    async loadData(arg) {
      console.log('mixin-loadData', arg)
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      if (this.tableProps) {
        this.tableProps.loading = true
      } else {
        this.loading = true
      }
      // 处理查询条件
      let params = this.getQueryParams(arg)
      console.log('处理查询条件', params)
      if (this.loadDataBefore && typeof this.loadDataBefore === 'function') {
        let res = await this.loadDataBefore(params)
        if (!res) {
          if (this.tableProps) {
            this.tableProps.loading = false
          } else {
            this.loading = false
          }
          return res
        }
      }
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          const dataSource = res.result.records || res.result || []
          // 数据请求后方法
          if (this.loadDataAfter && typeof this.loadDataAfter === 'function') {
            this.loadDataAfter(res)
          } else {
            if (this.tableProps) {
              this.tableProps.dataSource = dataSource
              this.tableProps.ipagination && (this.tableProps.ipagination.total = res.result.total || 0)
            } else {
              this.dataSource = dataSource
              this.ipagination && (this.ipagination.total = res.result.total || 0)
            }
          }
        }
        if (this.tableProps) {
          this.tableProps.loading = false
        } else {
          this.loading = false
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
      })
    },
    initDictConfig() {
      console.log('mixin-initDictConfig===>这是一个假的方法!')
    },
    handleSuperQuery(params, matchType) {
      // 高级查询方法
      if (!params) {
        this.superQueryParams = ''
        this.superQueryFlag = false
      } else {
        this.superQueryFlag = true
        this.superQueryParams = JSON.stringify(params)
        this.superQueryMatchType = matchType
      }
      this.loadData(1)
    },
    getQueryParams(arg) {
      // 获取查询条件
      let sqp = {}
      if (this.superQueryParams) {
        sqp['superQueryParams'] = encodeURI(this.superQueryParams)
        sqp['superQueryMatchType'] = this.superQueryMatchType
      }
      // 加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        if (this.tableProps) {
          this.tableProps.ipagination && (this.tableProps.ipagination.current = 1)
        } else {
          this.ipagination && (this.ipagination.current = 1)
        }
      }
      let param = Object.assign(sqp, this.isorter, this.filters, this.queryParam, arg)
      param.field = this.getQueryField()
      // 新布局组件分页
      if (this.tableProps) {
        this.tableProps.ipagination && (param.pageNo = this.tableProps.ipagination.current)
        this.tableProps.ipagination && (param.pageSize = this.tableProps.ipagination.pageSize)
      } else {
        this.ipagination && (param.pageNo = this.ipagination.current)
        this.ipagination && (param.pageSize = this.ipagination.pageSize)
      }
      return filterObj(param)
    },
    getQueryField() {
      // TODO 字段权限控制
      var str = 'id,'
      this.tableColumns.forEach((value) => {
        str += ',' + value.dataIndex
      })
      return str
    },

    onSelectChange(selectedRowKeys, selectionRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = selectionRows
    },
    onClearSelected() {
      this.selectedRowKeys = []
      this.selectionRows = []
    },
    searchQuery() {
      this.loadData(1)
    },
    superQuery() {
      this.$refs.superQueryModal.show()
    },
    searchReset() {
      this.queryParam = {}
      this.loadData(1)
    },
    handleTableChange(pagination, filters, sorter) {
      // 分页、排序、筛选变化时触发
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = sorter.order == 'ascend' ? 'asc' : 'desc'
      }
      if (this.tableProps) {
        this.tableProps.ipagination && (this.tableProps.ipagination = pagination)
      } else {
        this.ipagination && (this.ipagination = pagination)
      }
      this.loadData()
    },
    batchDel: function () {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.deleteBatch属性!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        var that = this
        this.$confirm({
          title: '确认删除',
          content: '是否删除选中数据?',
          onOk: function () {
            that.tableProps && (that.tableProps.loading = true)
            that.loading = true
            deleteAction(that.url.deleteBatch, { ids: ids })
              .then((res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  // 判断当前页是否还有数据
                  if (that.dataSource.length <= that.selectedRowKeys.length) {
                    that.loadData(1)
                  } else {
                    that.loadData()
                  }
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.tableProps && (that.tableProps.loading = false)
                that.loading = false
              })
          },
        })
      }
    },
    handleDelete: function (id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      if (typeof id === 'object') {
        id = id.id
      }
      let that = this
      this.$confirm({
        title: '提醒',
        content: '确认要删除吗?',
        okText: '确认',
        cancelText: '取消',
        onOk() {
          deleteAction(that.url.delete, { id }).then((res) => {
            if (res.success) {
              that.$message.success('删除成功！')
              // 判断当前页是否还有数据
              if (that.dataSource.length === 1) {
                that.loadData(1)
              } else {
                that.loadData()
              }
            } else {
              that.$message.warning(res.message)
            }
          })
        },
        onCancel() {},
      })
    },
    handleDetail: async function (record) {
      if (this.handleDetailBefore && typeof this.handleDetailBefore === 'function') {
        await this.handleDetailBefore(record)
      }
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '详情'
      this.$refs.modalForm.disableSubmit = true
    },
    handleAdd: async function () {
      if (this.handleAddBefore && typeof this.handleAddBefore === 'function') {
        await this.handleAddBefore()
      }
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.disableSubmit = false
    },
    handleEdit: async function (record) {
      if (this.handleEditBefore && typeof this.handleEditBefore === 'function') {
        await this.handleEditBefore(record)
      }
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.disableSubmit = false
    },
    modalFormOk() {
      // 新增/修改 成功时，重载列表
      this.loadData()
      // 清空列表选中
      this.onClearSelected()
    },
    handleToggleSearch() {
      this.toggleSearchStatus = !this.toggleSearchStatus
    },
    // 给popup查询使用(查询区域不支持回填多个字段，限制只返回一个字段)
    getPopupField(fields) {
      return fields.split(',')[0]
    },
    /* 导出 */
    handleExportXls2() {
      let paramsStr = encodeURI(JSON.stringify(this.getQueryParams()))
      let url = `${window._CONFIG['domianURL']}/${this.url.exportXlsUrl}?paramsStr=${paramsStr}`
      window.location.href = url
    },
    handleExportXls(fileName) {
      if (!fileName || typeof fileName !== 'string') {
        fileName = '导出文件'
      }
      let param = this.getQueryParams()
      if (this.selectedRowKeys && this.selectedRowKeys.length > 0) {
        param['selections'] = this.selectedRowKeys.join(',')
      }
      console.log('导出参数', param)
      downFile(this.url.exportXlsUrl, param).then((data) => {
        if (!data) {
          this.$message.warning('文件下载失败')
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data], { type: 'application/vnd.ms-excel' }), fileName + '.xls')
        } else {
          let url = window.URL.createObjectURL(new Blob([data], { type: 'application/vnd.ms-excel' }))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName + '.xls')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) // 下载完成移除元素
          window.URL.revokeObjectURL(url) // 释放掉blob对象
        }
      })
    },
    /* 导入 */
    handleImportExcel(info) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList)
      }
      if (info.file.status === 'done') {
        if (info.file.response.success) {
          // this.$message.success(`${info.file.name} 文件上传成功`);
          if (info.file.response.code === 201) {
            let {
              message,
              result: { msg, fileUrl, fileName },
            } = info.file.response
            let href = window._CONFIG['domianURL'] + fileUrl
            this.$warning({
              title: message,
              content: (
                <div>
                  <span>{msg}</span>
                  <br />
                  <span>
                    具体详情请{' '}
                    <a href={href} target="_blank" download={fileName}>
                      点击下载
                    </a>{' '}
                  </span>
                </div>
              ),
            })
          } else {
            this.$message.success(info.file.response.message || `${info.file.name} 文件上传成功`)
          }
          this.loadData()
        } else {
          this.$message.error(`${info.file.name} ${info.file.response.message}.`)
        }
      } else if (info.file.status === 'error') {
        if (info.file.response.status === 500) {
          let data = info.file.response
          const token = Vue.ls.get(ACCESS_TOKEN)
          if (token && data.message.includes('Token失效')) {
            Modal.error({
              title: '登录已过期',
              content: '很抱歉，登录已过期，请重新登录',
              okText: '重新登录',
              mask: false,
              onOk: () => {
                store.dispatch('Logout').then(() => {
                  Vue.ls.remove(ACCESS_TOKEN)
                  window.location.reload()
                })
              },
            })
          }
        } else {
          this.$message.error(`文件上传失败: ${info.file.msg} `)
        }
      }
    },
    /* 图片预览 */
    getImgView(text) {
      if (text && text.indexOf(',') > 0) {
        text = text.substring(0, text.indexOf(','))
      }
      return getFileAccessHttpUrl(text)
    },
    /* 文件下载 */
    // update--autor:lvdandan-----date:20200630------for：修改下载文件方法名uploadFile改为downloadFile------
    downloadFile(text) {
      if (!text) {
        this.$message.warning('未知的文件')
        return
      }
      if (text.indexOf(',') > 0) {
        text = text.substring(0, text.indexOf(','))
      }
      let url = getFileAccessHttpUrl(text)
      window.open(url)
    },
  },
}
