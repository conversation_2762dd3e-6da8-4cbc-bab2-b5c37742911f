import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, postAction } from '@/api/manage'

export const JeecgTreeListMixin = {
  mixins: [JeecgListMixin],
  data() {
    return {
      hasChildrenField: 'hasChild', // 是否有子数据字段标识
      pidField: 'pid' // 父级id字段标识
    }
  },
  methods: {
    // 分页切换
    onTableChange({ pagination, filters, sorter }) {
      this.handleTableChange(pagination, filters, sorter)
    },
    // 设置序号
    calcPath(data, parentPath) {
      data.forEach((node, index) => {
        const currentPath = !parentPath ? String(index + 1) : `${parentPath}-${index + 1}`
        node.path = currentPath
        if (node.children && node.children.length > 0) {
          this.calcPath(node.children, currentPath)
        }
      })
    },

    findAllChildIds(data, id, allChildIds = []) {
      for (const item of data) {
        if (item[this.pidField] === id) {
          allChildIds.push(item.id)
          if (item.children && item.children.length > 0) {
            this.findAllChildIds(item.children, item.id, allChildIds)
          }
        }
      }
      return allChildIds
    },

    // 根据已展开的行查询数据（用于保存后刷新时异步加载子级的数据）
    loadDataByExpandedRows(dataList) {
      if (this.tableProps.expandedRowKeys && this.tableProps.expandedRowKeys.length > 0) {
        return getAction(this.url.getChildListBatch, { parentIds: this.tableProps.expandedRowKeys.join(',') }).then(res => {
          if (res.success && res.result.records.length > 0) {
            // 已展开的数据批量子节点
            let records = res.result.records
            const listMap = new Map()
            for (let item of records) {
              let pid = item[this.pidField]
              if (this.tableProps.expandedRowKeys.join(',').includes(pid)) {
                let mapList = listMap.get(pid)
                if (mapList == null) {
                  mapList = []
                }
                mapList.push(item)
                listMap.set(pid, mapList)
              }
            }
            let childrenMap = listMap
            let fn = (list) => {
              if (list) {
                list.forEach(data => {
                  if (this.tableProps.expandedRowKeys.includes(data.id)) {
                    data.children = this.getDataByResult(childrenMap.get(data.id))
                    fn(data.children)
                  }
                })
              }
            }
            fn(dataList)
            // 根据已展开的行查询数据之后
            if (this.loadDataByExpandedRowsAfter && (typeof this.loadDataByExpandedRowsAfter === 'function')) {
              this.loadDataByExpandedRowsAfter(dataList)
            }
          }
        })
      } else {
        return Promise.resolve()
      }
    },
    getDataByResult(result) {
      if (result) {
        return result.map(item => {
          // 判断是否标记了带有子节点
          if (item[this.hasChildrenField] == '1') {
            let loadChild = { id: item.id + '_loadChild', name: 'loading...', isLoading: true }
            item.children = [loadChild]
          }
          return item
        })
      }
    },
    onTableExpand(expanded, record) {
      console.log('test-onTableExpand===>', expanded, record)
      // 判断是否是展开状态
      if (expanded) {
        this.tableProps.expandedRowKeys.push(record.id)
        if (record.children.length > 0 && record.children[0].isLoading === true) {
          let params = this.getQueryParams(1)// 查询条件
          params[this.pidField] = record.id
          params.hasQuery = 'false'
          getAction(this.url.childList, params).then((res) => {
            if (res.success) {
              if (res.result.records) {
                record.children = this.getDataByResult(res.result.records)
                this.tableProps.dataSource = [...this.tableProps.dataSource]
                // 展开后数据处理
                if (this.onTableExpandAfter && (typeof this.onTableExpandAfter === 'function')) {
                  this.onTableExpandAfter(this.tableProps.dataSource)
                }
              } else {
                record.children = ''
                record.hasChildrenField = '0'
              }
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      } else {
        let keyIndex = this.tableProps.expandedRowKeys.indexOf(record.id)
        if (keyIndex >= 0) {
          this.tableProps.expandedRowKeys.splice(keyIndex, 1)
        }
      }
    },
    handleAddBrother: async function(record) {
      // 判断是否为顶级节点,如果是顶级则调用新增接口
      // 展开后数据处理
      let obj = {}
      if (this.handleAddBrotherBefore && (typeof this.handleAddBrotherBefore === 'function')) {
        obj = await this.handleAddBrotherBefore(record)
      } else {

      }
      this.$refs.modalForm.edit(obj)
      this.$refs.modalForm.title = '添加同级'
      this.$refs.modalForm.disableSubmit = false
    },
    // 根据id找节点
    findNodeById(data, id) {
      for (let j = 0; j < data.length; j++) {
        let item = data[j]
        if (item.id === id) {
          return item
        }
        if (item.children) {
          let obj = this.findNodeById(item.children, id)
          if (obj) {
            return obj
          }
        }
      }
      return null  // 如果遍历了所有元素也没有找到，返回 null
    },
    handleAddSub: async function(record) {
      let obj = {}
      if (this.handleAddSubBefore && (typeof this.handleAddSubBefore === 'function')) {
        obj = await this.handleAddSubBefore(record)
      } else {

      }
      this.$refs.modalForm.edit(obj)
      this.$refs.modalForm.title = '添加下级'
      this.$refs.modalForm.disableSubmit = false
    },
    handleMoveUp(id) {
      if (!this.url.moveUp) {
        this.$message.error('请设置url.moveUp!')
        return
      }
      if (typeof id === 'object') {
        id = id.id
      }
      let that = this
      postAction(that.url.moveUp, { id }).then((res) => {
        if (res.success) {
          that.loadData(1)
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    handleMoveDown(id) {
      if (!this.url.moveDown) {
        this.$message.error('请设置url.moveDown!')
        return
      }
      if (typeof id === 'object') {
        id = id.id
      }
      let that = this
      postAction(that.url.moveDown, { id }).then((res) => {
        if (res.success) {
          that.loadData(1)
        } else {
          that.$message.warning(res.message)
        }
      })
    }
  }
}
