<template>
  <div class="h-100pre px-16 pb-16 flex">
    <div class="Fan w-150 mr-16 rounded-6 flex flex-column justify-end">
      <div class="total pl-32 pb-16">
        <div class="text-fff font-24 line-32">
          {{ dataPanelMap.fjSum || 0 }}
          <span>台</span>
        </div>
        <div class="text-fff font-12 line-20">风机总数</div>
      </div>
      <div class="num pl-32 pb-16">
        <div class="text-fff font-24 line-32">
          {{ dataPanelMap.fjcompleteIds.length }}
          <span>台</span>
        </div>
        <div class="text-fff font-12 line-20">已完成</div>
      </div>
    </div>
    <div class="Cable w-150 rounded-6 flex flex-column justify-end">
      <div class="total pl-32 pb-16">
        <div class="text-fff font-24 line-32">
          {{ dataPanelMap.hlSum }}
          <span>段</span>
        </div>
        <div class="text-fff font-12 line-20">线缆总数</div>
      </div>
      <div class="num pl-32 pb-16">
        <div class="text-fff font-24 line-32">
          {{ dataPanelMap.hlcompleteIds.length }}
          <span>段</span>
        </div>
        <div class="text-fff font-12 line-20">已完成</div>
      </div>
    </div>
    <div class="loopBox flex flex-column flex-wrap">
      <div v-for="(value, key) in route" :key="key">
        <div class="flex item-center mx-10 h-100pre">
          <div class="routerName w-70 text-center py-2 font-12 rounded-12 mr-10">
            {{ key }}
          </div>
          <div class="routerBox flex h-100pre item-center">
            <div
              class="syz w-6 h-6"
              :style="{
                borderColor:
                  findPbs(value[0]).fanSection == '1'
                    ? '#fda95c'
                    : dataPanelMap.hlcompleteIds.indexOf(findPbs(value[0]).id) != -1
                    ? '#00b42a'
                    : dataPanelMap.hlunderwayIds.indexOf(findPbs(value[0]).id) != -1
                    ? '#3254ff'
                    : '#d9d9d9',
              }"
            ></div>
            <div v-for="item in value" :key="item" class="pbs px-2" :style="{ width: `calc(100% / ${value.length})` }">
              <div v-show="findPbs(item).type === 'B06A01A01'" class="B06A01A01 pt-18">
                <a-tooltip>
                  <template slot="title">
                    {{ findPbs(item).name }}({{
                      dataPanelMap.fjcompleteIds.indexOf(findPbs(item).id) != -1
                        ? '已完成'
                        : dataPanelMap.fjunderwayIds.map((x) => x.id).indexOf(findPbs(item).id) != -1
                        ? `进行中${
                            dataPanelMap.fjunderwayIds[
                              dataPanelMap.fjunderwayIds.map((x) => x.id).indexOf(findPbs(item).id)
                            ].weights
                          }%`
                        : findPbs(item).fanSection != 1
                        ? '未开始'
                        : '标段外'
                    }})
                  </template>
                  <div class="flex flex-column item-center">
                    <img
                      :src="
                        require(`@/assets/home/<USER>
                          findPbs(item).fanSection == '1'
                            ? '4'
                            : dataPanelMap.fjcompleteIds.indexOf(findPbs(item).id) != -1
                            ? '1'
                            : dataPanelMap.fjunderwayIds.map((x) => x.id).indexOf(findPbs(item).id) != -1
                            ? '2'
                            : '3'
                        }.png`)
                      "
                      class="w-20 h-20"
                    />
                    <span>{{ findPbs(item).name }}</span>
                  </div>
                </a-tooltip>
              </div>
              <a-tooltip>
                <template slot="title">
                  {{ findPbs(item).name }}({{
                    dataPanelMap.hlcompleteIds.indexOf(findPbs(item).id) != -1
                      ? '已完成'
                      : dataPanelMap.hlunderwayIds.indexOf(findPbs(item).id) != -1
                      ? '进行中'
                      : findPbs(item).fanSection != 1
                      ? '未开始'
                      : '标段外'
                  }})
                </template>
                <div
                  :class="
                    findPbs(item).fanSection == '1'
                      ? 'lineSatet-4'
                      : dataPanelMap.hlcompleteIds.indexOf(findPbs(item).id) != -1
                      ? 'lineSatet-1'
                      : dataPanelMap.hlunderwayIds.indexOf(findPbs(item).id) != -1
                      ? 'lineSatet-2'
                      : 'lineSatet-3'
                  "
                  v-show="findPbs(item).type === 'B06A01A02'"
                  class="B06A01A02"
                ></div>
              </a-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAction } from '@api/manage'
export default {
  data() {
    return {
      url: {
        constructInfo: '/dashboard/information/getConstructInfo',
      },
      dataPanelMap: {},
      pbsList: {},
      route: {},
    }
  },
  created() {},
  mounted() {
    this.getConstructInfo()
  },
  methods: {
    getConstructInfo() {
      getAction(this.url.constructInfo, {}).then((res) => {
        if (res.success) {
          console.log('施工信息', res.result)
          let { dataPanelMap, pbsList, route } = res.result || {}
          this.dataPanelMap = dataPanelMap || {}
          this.pbsList = pbsList || []
          this.route = route || {}
        }
      })
    },
    findPbs(id) {
      return this.pbsList.find((x) => x.id === id)
    },
  },
}
</script>

<style scoped lang="scss">
.Fan {
  background-image: url('~@/assets/home/<USER>');
  background-size: 100% 100%;
}
.total,
.num {
  position: relative;
  span {
    font-size: 12px;
    line-height: 20px;
  }
  &::before {
    top: 0;
    left: 22px;
    position: absolute;
    display: inline-block;
    content: '';
    width: 2px;
    height: 52px;
    background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
  }
}
.Cable {
  background-size: 100% 100%;
  background-image: url('~@/assets/home/<USER>');
}
.loopBox {
  width: calc(100% - 300px - 16px);
  > div {
    width: 50%;
    height: 25%;
    border-right: 1px solid #e5e6eb;
    > div {
      border-bottom: 1px solid #e5e6eb;
    }
    .routerName {
      background: #ebeeff;
      display: inline-block;
      color: #3254ff;
    }
    .routerBox {
      width: calc(100% - 70px);
    }
    .pbs {
    }
    .syz {
      border-radius: 50%;
      border-width: 2px;
      border-style: solid;
    }
    .B06A01A02 {
      width: 100%;
      height: 2px;
      border-radius: 2px 2px 2px 2px;
    }
    .lineSatet-1 {
      background: #00b42a;
    }
    .lineSatet-2 {
      background: #3254ff;
    }
    .lineSatet-3 {
      background: #d9d9d9;
    }
    .lineSatet-4 {
      background: #fda95c;
    }
  }
}
</style>
