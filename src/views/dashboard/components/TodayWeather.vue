<template>
  <div class="h-100pre px-45 pb-12 rounded-4 flex">
    <div class="left h-100pre flex item-center justify-center flex-column px-15">
      <img :src="require(`@/assets/weatherIcon/icon/${info.weatherCode}.png`)" class="w-60 h-60 mb-4" />
      <div class="mb-30 text-fff font-36 line-44">{{ info.temperature }}°</div>
      <div class="weatherDescription text-center w-100pre font-14 line-30 bg-fff rounded-16 h-30">
        {{ info.weatherDescription }}
      </div>
    </div>
    <div class="right h-100pre py-36">
      <div v-for="item in typeList" :key="item.key" class="rounded-4 flex item-center justify-between px-20">
        <div class="h-100pre flex item-center">
          <img :src="item.url" class="w-20 h-20 mr-10" />
          <span class="font-14 line-22 name">{{ item.name }}</span>
        </div>
        <div :class="item.key" class="font-12">
          {{ info[item.key] }}{{ item.unit }}
          <img
            v-if="item.key === 'windMark'"
            :style="{ transform: `rotateZ(${info.windDirection}deg)` }"
            :src="require(`@/assets/home/<USER>"
            class="arrow w-20 h-20"
          />
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script>
import { getAction } from '@api/manage'
export default {
  data() {
    return {
      url: {
        realTimeWeather: '/dashboard/information/getRealTimeWeather',
      },
      typeList: [
        { name: '风速', key: 'windSpeed', url: require('@/assets/home/<USER>'), unit: 'm/s' },
        { name: '风向', key: 'windMark', url: require('@/assets/home/<USER>'), unit: '风' },
        { name: '气压', key: 'pressure', url: require('@/assets/home/<USER>'), unit: 'Pa' },
        { name: '能见度', key: 'visibility', url: require('@/assets/home/<USER>'), unit: 'km' },
      ],
      info: {},
    }
  },
  created() {},
  mounted() {
    this.getRealTimeWeather()
  },
  methods: {
    getRealTimeWeather() {
      getAction(this.url.realTimeWeather, {}).then((res) => {
        if (res.success) {
          this.info = res.result || {}
        }
      })
    },
  },
}
</script>
  
  <style scoped lang="scss">
.left {
  background-size: 100% 100%;
  background-image: url('~@/assets/home/<USER>');
  width: 31%;
  .weatherDescription {
    color: #86909c;
  }
}
.right {
  width: 69%;
  background-size: 100% 100%;
  background-image: url('~@/assets/home/<USER>');
  display: grid;
  gap: 12px;
  grid-template-rows: repeat(4, 36px);
  > div {
    width: 80%;
    margin-left: 10%;
    background: rgba(255, 255, 255, 0.65);
    .name {
      color: #4e5969;
    }
    .windSpeed {
      color: #ff7d00;
    }
    .windMark,
    .waveHeight,
    .visibility {
      color: #4b4b4b;
    }
  }
}
</style>
  