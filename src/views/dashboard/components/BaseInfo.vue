<template>
  <div class="BaseInfo h-200 pt-8 pl-20">
    <div class="title line-28 font-18 mb-5">基础信息</div>
    <div class="flex cardBox mb-15">
      <div class="card mr-12 w-240 h-80 rounded-8 pl-20 flex item-center">
        <img :src="require('@/assets/home/<USER>')" alt="" />
        <div>
          <div>{{projectInfo.workedDays}}天</div>
          <div>已开工</div>
        </div>
      </div>
      <div class="card mr-12 w-240 h-80 rounded-8 pl-20 flex item-center">
        <img :src="require('@/assets/home/<USER>')" alt="" />
        <div>
          <div>{{projectInfo.plannedDuration}}天</div>
          <div>计划工期</div>
        </div>
      </div>
      <div class="card mr-12 w-240 h-80 rounded-8 pl-20 flex item-center">
        <img :src="require('@/assets/home/<USER>')" alt="" />
        <div>
          <div>{{projectInfo.plannedCompletionDate}}</div>
          <div>计划完工</div>
        </div>
      </div>
    </div>
    <div class="remark info maxw-744" v-html="projectInfo.remark">
    </div>
  </div>
</template>

<script>
import { getAction } from '@api/manage'
export default {
  name: 'BaseInfo',
  data() {
    return {
      projectInfo: {},
    }
  },
  created() {},
  mounted() {
    this.getProjectInfo()
  },
  methods: {
    getProjectInfo() {
      getAction('/dashboard/information/getProjectInfo', {}).then((res) => {
        if (res.success) {
          this.projectInfo = res.result
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
.BaseInfo {
  background-image: url('~@/assets/home/<USER>');
  background-size: 100% 100%;
  .title {
    color: #212121;
  }
  .cardBox {
    .card {
      img {
        width: 48px;
        height: 48px;
        margin-right: 8px;
      }
      > div {
        color: #121212;
        > div:first-child {
          font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
          font-size: 24px;
          line-height: 36px;
        }
        > div:last-child {
          font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
          font-size: 14px;
          line-height: 22px;
        }
      }
      &:first-child {
        background: linear-gradient(264deg, #f2f7fc 0%, #d6e6ff 100%);
        border: 1px solid #dfebff;
      }
      &:nth-child(2) {
        background: linear-gradient(257deg, #fcfafa 0%, #fff0d9 100%);
        border: 1px solid #fff1dd;
      }
      &:last-child {
        background: linear-gradient(264deg, #f2f6fc 0%, #d8dcff 100%);
        border: 1px solid #e1e5fe;
      }
    }
  }
  .remark {
    height: calc(100% - 28px - 5px - 80px - 15px);
    overflow: auto;
  }
}
</style>
