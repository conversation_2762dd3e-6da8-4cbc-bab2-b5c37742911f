<template>
  <div class="h-100pre px-20">
    <div class="total h-54 rounded-6 pl-12 flex item-center mb-16">
      <img :src="require('@/assets/home/<USER>')" class="w-32 h-32 mr-8" />
      <div>
        <div class="text-fff line-20 font-18">
          {{ total }}
          <span class="font-12 line-18">人</span>
        </div>
        <div class="font-12 line-18 text-fff">在场总人数</div>
      </div>
    </div>
    <div>
      <div class="flex th">
        <div></div>
        <div>在场人数</div>
        <div>20-29岁</div>
        <div>30-39岁</div>
        <div>40-49岁</div>
        <div>50-59岁</div>
        <div>60岁以上</div>
      </div>
      <div class="">
        <div class="tr flex h-40 item-center" v-for="(item, index) in types" :key="item">
          <div>
            <span class="rounded-12 text-center font-12 line-20 py-2 px-8">
              {{ item }}
            </span>
          </div>
          <div class="font-14">
            {{ list[index].total }}
          </div>
          <div class="font-14">
            <span class="circle" :class="{ zero: list[index].age20 == 0 }">
              {{ list[index].age20 }}
            </span>
          </div>
          <div>
            <span class="circle" :class="{ zero: list[index].age30 == 0 }">{{ list[index].age30 }}</span>
          </div>
          <div>
            <span class="circle" :class="{ zero: list[index].age40 == 0 }">{{ list[index].age40 }}</span>
          </div>
          <div>
            <span class="circle" :class="{ zero: list[index].age50 == 0 }">{{ list[index].age50 }}</span>
          </div>
          <div>
            <span class="circle" :class="{ zero: list[index].age60 == 0 }">{{ list[index].age60 }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script>
import { getAction } from '@api/manage'
export default {
  data() {
    return {
      url: {
        personInfo: '/dashboard/information/getPersonInfo',
      },
      total: 0,
      types: [],
      list: [],
    }
  },
  created() {},
  mounted() {
    this.getPersonInfo()
  },
  methods: {
    getPersonInfo() {
      getAction(this.url.personInfo, {}).then((res) => {
        if (res.success) {
          // console.log('人员信息', res.result)
          const data = res.result || {}
          this.total = data.total || 0
          this.types = Object.keys(data)
          this.types.splice(Object.keys(data).indexOf('total'), 1)
          this.list = this.types.map((x) => {
            return {
              total: data[x].remainPersonCount,
              age20: data[x].ageInfo['20-29'],
              age30: data[x].ageInfo['30-39'],
              age40: data[x].ageInfo['40-49'],
              age50: data[x].ageInfo['50-59'],
              age60: data[x].ageInfo['60+'],
            }
          })
        }
      })
    },
  },
}
</script>
  
  <style scoped lang="scss">
.total {
  background: linear-gradient(344deg, rgba(117, 187, 252, 0) 0%, #2f4ee8 100%);
}
.th {
  > div {
    width: calc(100% / 7);
    font-size: 14px;
    color: #86909c;
    text-align: center;
    line-height: 20px;
    &:nth-child(2) {
      color: #4e5969;
    }
  }
}
.tr {
  border-bottom: 1px solid #e5e6eb;
  > div {
    width: calc(100% / 7);
    text-align: center;
    &:first-child {
      span {
        display: inline-block;
        background: #ebeeff;
        color: #3254ff;
      }
    }
    &:nth-child(2) {
      color: #1d2129;
    }
    .circle {
      display: inline-block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      text-align: center;
      font-size: 14px;
      line-height: 24px;
      color: #fff;
      background: linear-gradient(180deg, #5f96ff 0%, #3254ff 100%);
    }
    .zero {
      background: #c9cdd4;
      color: #86909c;
    }
  }
}
</style>
  