<template>
  <div class="h-100pre px-20 pb-20 over-auto">
    <div class="li h-48 flex item-center" v-for="item in list" :key="item.id">
      <img :src="require('@/assets/home/<USER>')" class="w-28 h-28 mr-9" />
      <div class="font-16 name text-ellipsis mr-19">
        {{ item.name }}
      </div>
      <div class="action">详情<a-icon type="right" style="color:#3254ff"/></div>
    </div>
  </div>
</template>
  
  <script>
import { getAction } from '@api/manage'
export default {
  data() {
    return {
      list: [],
    }
  },
  created() {},
  mounted() {
    this.getVideoList()
  },
  methods: {
    getVideoList() {
      getAction('/project/projectCamera/getAllCamera', {}).then((res) => {
        if (res.success) {
          this.list = res.result || []
        }
      })
    },
  },
}
</script>
  
<style scoped lang="scss">
.li {
  border-bottom: 1px solid #e5e6eb;
  .name {
    color: #1d2129;
    width: calc(100% - 45px - 28px - 9px - 19px);
  }
  .action {
    width: 45px;
    font-size: 14px;
    color: #3254ff;
    cursor: pointer;
  }
}
</style>
  