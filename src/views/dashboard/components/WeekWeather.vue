<template>
  <div class="h-100pre mx-22 pb-12 box flex">
    <div
      v-for="(item, index) in list"
      :key="index"
      class="minw-68 h-100pre mr-4 rounded-4 flex flex-column justify-between item-center py-13"
      :style="{
        backgroundImage: `url(${require(`@/assets/home/<USER>
          item.weatherCode,
          item.windSpeed
        )}.png`)})`,
      }"
    >
      <div class="font-14">{{ item.week }}</div>
      <div class="font-12">{{ item.date }}</div>
      <img :src="require(`@assets/weatherIcon/icon/${item.weatherCode}.png`)" class="w-20 h-20" />
      <div class="font-14">{{ item.maxTemperature }}°</div>
      <div class="font-12">{{ item.minTemperature }}°</div>
      <img
        :style="{ transform: `rotateZ(${item.windDirection}deg)` }"
        class="w-20 h-20"
        :src="require(`@/assets/home/<USER>"
      />
      <div class="font-14">{{ item.windSpeed }}级</div>
    </div>
  </div>
</template>
  
  <script>
import { getAction } from '@api/manage'
import seaCode from '../../../../public/static/seaCode.json'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  data() {
    return {
      url: {
        weather7days: '/dashboard/information/getWeather7days',
      },
      list: [],
    }
  },
  created() {},
  mounted() {
    this.getWeather7days()
  },
  methods: {
    getWeather7days() {
      getAction(this.url.weather7days, {}).then((res) => {
        if (res.success) {
          console.log('最近7天天气', res.result)
          const week = res.result.week || []
          this.list = week.map((x, i) => {
            return {
              date: moment(String(x.day)).format('MM/DD'),
              Date: moment(String(x.day)).format('YYYY-MM-DD'),
              weatherDescription: x.weatherDescription,
              windSpeed: this.getJsonLevel(x.windSpeed, 'wind_levels').level,
              weatherCode: x.weatherCode,
              temperature: x.temperature,
              week: i === 0 ? '今天' : moment(String(x.day)).format('dddd'),
              windDirection: x.windDirection,
              maxTemperature: x.maxTemperature,
              minTemperature: x.minTemperature,
            }
          })
        }
      })
    },
    getJsonLevel(value, type) {
      for (let item of seaCode[type]) {
        if (value >= item.range.min && value <= item.range.max) {
          return item
        }
      }
      return item
    },
    weatherType(code, windSpeed) {
      if (code === '1002' || code === '1006' || code === '1028' || windSpeed >= 10) return '3'
      else if (code === '1031' || code === '1032' || code === '1033') return '1'
      else return '2'
    },
  },
}
</script>  
<style scoped lang="scss">
.box {
  overflow-x: auto;
  width: calc(100% - 44px);
  > div {
    width: calc(100% / 7);
    background-size: 100% 100%;
    > div {
      color: #fff;
    }
  }
}
</style>
  