<template>
  <div class="bg-fff rounded-2 pt-16 h-100pre">
    <div class="flex px-20 justify-between mb-12 h-28">
      <div class="title flex item-center">
        <div class="icon mr-10"></div>
        {{ title }}
      </div>
      <slot name="title-right"></slot>
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>
  
  <script>
export default {
  props: {
    title: {
      type: String,
      default: '标题',
    },
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {},
}
</script>
  
  <style scoped lang="scss">
.title {
  font-size: 18px;
  color: #212121;
  line-height: 28px;
  .icon {
    position: relative;
    border-radius: 1px;
    width: 10px;
    height: 10px;
    background: #3254ff;
    box-shadow: 0px 2px 2px 0px rgba(50, 84, 255, 0.16);
    &::before {
      content: '';
      display: inline-block;
      position: absolute;
      border-radius: 1px;
      width: 8px;
      height: 8px;
      background: #97b4ff;
      left: -4px;
      top: -4px;
    }
  }
}
.content{
    height: calc(100% - 40px);
}
</style>
  