<template>
  <div class="h-100pre flex">
    <div class="w-250 pb-8">
      <div id="pie" class="h-100pre"></div>
    </div>
    <div class="right pr-12 pb-8">
      <div class="content rounded-12 h-100pre pt-20 px-16">
        <div class="h-28 line-28 text-center rounded-36 title text-fff font-14 mb-16">今日出海船舶</div>
        <a-carousel v-if="shipData.length > 0">
          <div class="carouselBox h-100pre" v-for="i in Math.ceil(shipData.length / 3)" :key="i">
            <div
              v-for="item in i * 3 > shipData.length ? 3 - (i * 3 - shipData.length) : 3"
              :key="item"
              class="h-100pre bg-fff rounded-6 px-10 pt-18"
            >
              <div class="PlanSail flex item-center pb-11">
                <img :src="require('@/assets/home/<USER>')" class="w-30 h-30 mr-6" />
                <div>
                  <div class="line-20 font-16">{{ shipData[(i - 1) * 3 + item - 1].count }}人</div>
                  <div class="line-20 font-12">出海总人数</div>
                </div>
              </div>
              <div class="flex item-center pt-10 mb-24">
                <img :src="require('@/assets/home/<USER>')" class="w-30 h-30 mr-6" />
                <div>
                  <div class="line-20 font-16">{{ shipData[(i - 1) * 3 + item - 1].hour }}h</div>
                  <div class="line-20 font-12">本次出海时长</div>
                </div>
              </div>
              <a-tooltip>
                <template slot="title">
                  {{ shipData[(i - 1) * 3 + item - 1].name }}
                </template>
                <div class="px-20 text-ellipsis-clamp" style="word-break: break-all">
                  {{ shipData[(i - 1) * 3 + item - 1].name }}
                </div>
              </a-tooltip>
            </div>
          </div>
        </a-carousel>
        <div v-else class="text-center">
          <img :src="require('@/assets/home/<USER>')" class="w-184 h-156" />
        </div>
      </div>
    </div>
  </div>
</template>
  
<script>
import * as echarts from 'echarts'
import moment from 'moment'
import { getAction } from '@api/manage'
export default {
  components: {},
  data() {
    return {
      url: {
        shipInfo: '/dashboard/information/getShipInfo',
      },
      shipData: [],
      shipNum: [],
      total: 0,
    }
  },
  created() {},
  mounted() {
    this.getShipInfo()
  },
  methods: {
    getShipInfo() {
      getAction(this.url.shipInfo, {}).then((res) => {
        if (res.success) {
          const data = res.result || {}
          for (const key in data) {
            this.total += data[key]
            this.shipNum.push({
              value: data[key],
              name: key,
            })
          }
          this.init()
        }
      })
      getAction('dashboard/information/getCurrentShipSailingInfo').then((res) => {
        this.shipData = res.result.map((x) => {
          x.hour = moment(new Date()).diff(x.createTime, 'hours')
          return x
        })
      })
    },
    init() {
      var chartDom = document.getElementById('pie')
      var myChart = echarts.init(chartDom)

      const option = {
        title: {
          text: this.total,
          subtext: '总数',
          left: 'center', // 标题文本水平居中
          top: '35%', // 标题文本垂直居中
          textStyle: {
            fontSize: 24,
          },
          subtextStyle: {},
        },
        tooltip: {
          trigger: 'item',
        },
        legend: {
          top: '70%',
          left: 'center',
          itemWidth: 12,
          itemHeight: 12,
        },
        series: [
          {
            type: 'pie',
            name: '在场船舶统计',
            center: ['50%', '40%'],
            radius: ['50%', '70%'],
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: this.shipNum,
          },
        ],
      }

      option && myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
  },
}
</script>
<style scoped lang="scss">
.right {
  width: calc(100% - 250px);
}
.content {
  background: linear-gradient(180deg, #edf0ff 0%, #f2f4ff 100%);
  .title {
    background: linear-gradient(90deg, #3254ff 0%, #4f95ff 100%);
  }
  .ant-carousel {
    height: calc(100% - 44px - 45px);
    ::v-deep .slick-slider {
      height: 100%;
      .slick-dots {
        bottom: -20px;
        li {
          width: 9px;
          height: 9px;
          background: #bcc7ff;
          border-radius: 50%;
          button {
            width: 100%;
            height: 100%;
            background-color: transparent;
          }
        }
        .slick-active {
          background: #3254ff;
        }
      }
      .slick-list {
        height: 100%;
        .slick-track {
          height: 100%;
          .slick-slide {
            height: 100%;
            > div {
              height: 100%;
              .carouselBox {
                display: flex !important;
                justify-content: flex-start;
                > div {
                  &:last-child {
                    margin-right: 0;
                  }
                  margin-right: 10px;
                  width: calc(calc(100% - 20px) / 3);
                  .PlanSail {
                    border-bottom: 1px solid #e5e6eb;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
  