<template>
  <div class="h-100pre">
    <div class="typeList pl-20">
      <div
        v-for="(item, index) in typeList"
        @click="tabClick(index)"
        :key="item"
        :class="{ active: index === active }"
        class="h-28 line-28 font-14 text-center rounded-2"
      >
        {{ item }}
      </div>
    </div>
    <div class="content" v-if="list.length > 0">
      <div v-for="(item, index) in list" :key="index" class="h-44 mx-20 li flex justify-between item-center">
        <div class="font-16 name flex-1 text-ellipsis">
          <template v-if="active == 0">
            {{ `${item.name}(${item.mmsi})闯入警告！` }}
          </template>
          <template v-if="active == 1">
            <a-tag
              :color="
                item.warningLevel == '红色'
                  ? '#F53F3F'
                  : item.warningLevel == '黄色'
                  ? '#E7BD29'
                  : item.warningLevel == '橙色'
                  ? '#FF7D00'
                  : item.warningLevel == '蓝色'
                  ? '#3254FF'
                  : item.warningLevel == '白色'
                  ? '#E5E6EB'
                  : ''
              "
            >
              {{ item.warningLevel }}预警
            </a-tag>
            {{ `${item.title.split('[')[0]}` }}
          </template>
          <template v-if="active == 2">
            {{ item.name }}
          </template>
        </div>
        <div class="right flex item-center pl-5">
          <div class="line-22 font-14">
            {{
              active == 0
                ? moment(item.lasttime * 1000).format('YYYY-MM-DD')
                : active == 1
                ? item.releaseDate
                : item.createTime
            }}
          </div>
          <div class="action font-12 ml-9" @click="view(item)">
            <span>详情</span>
            <a-icon type="right" style="color: #3254ff" />
          </div>
        </div>
      </div>
    </div>
    <a-empty v-else />
    <component :is="modalForm" ref="modalForm"></component>
  </div>
</template>

<script>
import moment from 'moment'
import { getAction } from '@api/manage'
export default {
  data() {
    return {
      url: {
        shipAlarm: '/dashboard/information/getShipAlarm',
        windnotifycation: '/dashboard/information/windnotifycation',
        extremeWeather: '/dashboard/information/getExtremeWeather',
      },
      typeList: ['围栏警告', '极端天气'],
      active: 0,
      list: [],
      modalForm: '',
    }
  },
  created() {},
  mounted() {
    this.getShipAlarm()
  },
  methods: {
    moment,
    tabClick(index) {
      this.list = []
      this.active = index
      if (index == 0) this.getShipAlarm()
      else if (index == 1) this.getExtremeWeather()
      else this.getWindnotifycation()
    },
    getWindnotifycation() {
      getAction(this.url.windnotifycation, {}).then((res) => {
        if (res.success) {
          this.list = [res.result]
          this.init()
        }
      })
    },
    getShipAlarm() {
      getAction(this.url.shipAlarm, {}).then((res) => {
        if (res.success) {
          const result = res.result || {}
          console.log('船舶告警', res.result)
          this.list = result.inShip || []
          this.init()
        }
      })
    },
    getExtremeWeather() {
      getAction(this.url.extremeWeather, {}).then((res) => {
        if (res.success) {
          console.log('极值气象信息', res.result)
          this.list = res.result || []
          this.init()
        }
      })
    },
    view(item) {
      this.$refs.modalForm.edit(item)
      this.$refs.modalForm.title = '详情'
      this.$refs.modalForm.disableSubmit = true
    },
    async init() {
      let result
      if (this.active == 0) result = await import('@views/modules/ship/shipInfo/components/ShipInfoModal')
      if (this.active == 1) result = await import('@views/modules/sea/ExtremeWeather/components/Modal')
      if (this.active == 2) result = await import('@views/modules/wind/WindNotification/components/Modal')
      this.modalForm = result.default
    },
  },
}
</script>

<style scoped lang="scss">
.typeList {
  display: grid;
  grid-template-columns: repeat(3, 108px);
  gap: 8px;
  div {
    cursor: pointer;
    background-color: rgba(50, 84, 255, 0.1);
    color: #3254ff;
  }
  .active {
    background: #3254ff;
    color: #fff;
  }
}
.ant-empty {
  margin-top: 50px;
}
.content {
  height: calc(100% - 28px - 12px);
  overflow: auto;
  .li {
    border-bottom: 1px solid #e5e6eb;
    .name {
      color: #1d2129;
    }
    .right {
      color: #86909c;
    }
    .action {
      color: #3254ff;
      cursor: pointer;
    }
  }
}
</style>
