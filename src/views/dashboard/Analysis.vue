<template>
  <div class="w-100pre h-100pre box" id="analysis-container" style="min-width:1500px">
    <!-- 基础信息 -->
    <BaseInfo class="mb-16"></BaseInfo>
    <!-- 风场布置图 -->
    <div class=" flex justify-between mb-16 h-500" style="border-radius: 2px">
      <Kanban2D :showAnalysis="false" :container="'analysis-container'"></Kanban2D>
    </div>
    <!-- 消息通知 今天天气情况 未来7天预报 -->
    <div class="center flex justify-between mb-16 h-321">
      <Card :title="'消息通知'">
        <MessageNotification></MessageNotification>
      </Card>
      <Card :title="'今日天气情况'">
        <template v-slot:title-right>
          <div class="font-14 line-24" style="color: #3254ff">{{ nowDate }}</div>
        </template>
        <TodayWeather></TodayWeather>
      </Card>
      <Card :title="'未来7日天气预报'">
        <WeekWeather></WeekWeather>
      </Card>
    </div>
    <!-- 风机施工进度 -->
    <!-- <FanProgress class="h-336 bg-fff rounded-2 mb-16"></FanProgress> -->
    <Card :title="'施工进度'" class="h-336 mb-16">
      <FanProgress class=""></FanProgress>
    </Card>
    <!-- 视频监控 项目船舶概览 项目人员概览 -->
    <div class="bottom h-369 flex justify-between">
      <Card :title="'视频监控'">
        <VideoMonitor></VideoMonitor>
      </Card>
      <Card :title="'项目概览'">
<!--        <ShipSurvey></ShipSurvey>-->
      </Card>
      <Card :title="'项目人员概览'">
        <PersonSurvey></PersonSurvey>
      </Card>
    </div>
  </div>
</template>


<script>
import { getAction } from '@api/manage'
import BaseInfo from './components/BaseInfo'
import MessageNotification from './components/MessageNotification'
import TodayWeather from './components/TodayWeather'
import WeekWeather from './components/WeekWeather'
import FanProgress from './components/FanProgress'
import VideoMonitor from './components/VideoMonitor'
import ShipSurvey from './components/ShipSurvey'
import PersonSurvey from './components/PersonSurvey'
import Card from './components/Card'
import Kanban2D from '../../views/modules/construction/kanban2D'
import moment from 'moment'
moment.locale('zh-cn')

export default {
  name: 'Analysis',
  components: {
    BaseInfo,
    MessageNotification,
    TodayWeather,
    WeekWeather,
    FanProgress,
    VideoMonitor,
    ShipSurvey,
    PersonSurvey,
    Card,
    Kanban2D,
  },
  data() {
    return {
      url: {
        projectInfo: '/dashboard/information/getProjectInfo',
        realTimeWeather: '/dashboard/information/getRealTimeWeather',
        extremeWeather: '/dashboard/information/getExtremeWeather',
        shipAlarm: '/dashboard/information/getShipAlarm',
        windnotifycation: '/dashboard/information/windnotifycation',
        personInfo: '/dashboard/information/getPersonInfo',
        shipInfo: '/dashboard/information/getShipInfo',
        weather7days: '/dashboard/information/getWeather7days',
        constructInfo: '/dashboard/information/getConstructInfo',
      },
      nowDate: '',
    }
  },
  created() {
    // this.getRealTimeWeather()
    // this.getExtremeWeather()
    //
    //
    // this.getShipAlarm()
    // this.getWindnotifycation()
    //
  },
  mounted() {
    const date = new Date()
    this.nowDate = `${moment(date).format('YYYY-MM-DD HH:mm')}  ${moment(date).format('dddd')}`
  },
  methods: {},
}
</script>
<style lang="scss" scoped>
.box {
  overflow: auto;
  --index: 500;
  --left: 54px;
  --top: 50px;
  --right: 54px;
  --bottom: 16px;
}
.center {
  > div {
    width: calc(calc(100% - 32px) / 3);
  }
}
.bottom {
  > div {
    &:first-child {
      width: calc(calc(100% - 32px) * 0.236);
    }
    &:nth-child(2) {
      width: calc(calc(100% - 32px) * 0.43);
    }
    &:last-child {
      width: calc(calc(100% - 32px) * 0.334);
    }
  }
}
::v-deep .ant-modal-wrap {
  position: absolute;
  top: 12px;
  right: 12px;
  bottom: 12px;
  left: 12px;
}
</style>