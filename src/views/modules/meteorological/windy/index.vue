<template>
  <div class="windy-container">
    <div class="windy">
      <iframe
        width="100%"
        :height="iframeHeight"
        :src="`https://embed.windy.com/embed2.html?lat=${lnglat.lat}&lon=${lnglat.lng}&detailLat=${lnglat.lat}&detailLon=${lnglat.lng}&zoom=9&level=surface&overlay=wind&product=ecmwf&menu=&message=&marker=true&calendar=now&pressure=&type=map&location=coordinates&detail=true&metricWind=default&metricTemp=default&radarRange=-1`"
        frameborder="0"
      >
      </iframe>
    </div>
  </div>
</template>

<script>
import mapData from '../../../../../public/static/map/mapData.json'
export default {
  name: 'WeatherFacts',
  data() {
    return {
      lnglat: {
        lng: mapData.mapCenter[0],
        lat: mapData.mapCenter[1],
      },
    }
  },
  created() {},
  methods: {},
  computed: {
    iframeHeight() {
      return `${window.innerHeight - 130}px`
    },
  },
}
</script>

<style scoped>
.windy-container {
  height: 100%;
}
.windy {
  height: 100%;
}
</style>
