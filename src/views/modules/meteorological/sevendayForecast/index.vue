<template>
  <div class="w-100pre h-100pre page-content">
    <a-row :gutter="[16, 16]" class="a-row">
      <!-- 上 -->
      <div class="content-top content-height font-16">
        <!-- 当日详情 -->
        <a-col :span="6" class="h-100pre">
          <div
            class="h-100pre rounded-6 px-20 py-10 top-left-bg"
            :style="{
              backgroundImage: `url(${require(`@/assets/weatherIcon/bg/bg-L-${weatherBg(
                WeatherInfo.weatherCode
              )}.png`)})`,
            }"
            :class="{ night: WeatherInfo.pod == 'n' }"
          >
            <div class="flex-between align-center text-fff mb-20">
              <div class="flex align-center font-12">
                <a-avatar shape="square" :size="16" :src="require('@/assets/position.png')" />
                <span class="ml-10 font-16">{{ WeatherInfo.cityName }}</span>
                <span class="mx-20">{{ decimalDegreesToDMS(WeatherInfo.lon) }}E</span>
                <span class="">{{ decimalDegreesToDMS(WeatherInfo.lat) }}N</span>
              </div>
              <div class="font-12">{{ nowTime }}</div>
            </div>
            <div class="flex align-center text-fff mb-20">
              <div class="font-64 mr-20">{{ WeatherInfo.temperature }}°</div>
              <div class="font-12">
                <div class="mb-10">
                  <a-avatar
                    shape="square"
                    :size="32"
                    :src="require('@/assets/windDirection.png')"
                    :style="{
                      transform: `rotate(${getJsonLevel(WeatherInfo.windDirection, 'wind_directions').icon_angle}deg)`,
                    }"
                  />
                </div>
                <div class="flex align-center">
                  <a-avatar
                    shape="square"
                    :size="18"
                    :src="require(`@/assets/weatherIcon/icon/${WeatherInfo.weatherCode}.png`)"
                  />
                  <div class="mr-15 ml-5">{{ weatherName }}</div>
                  <div class="mr-15">{{ WeatherInfo.windMark }}风</div>
                  <div>{{ WeatherInfo.windLevel }}级</div>
                </div>
              </div>
            </div>
            <div class="bottom">
              <a-row :gutter="[16, 16]">
                <a-col :span="8" v-for="item in todayInfo" :key="item.name" class="text-center">
                  <div class="mb-5" style="color: #98b3ff">{{ item.name }}</div>
                  <div class="text-fff">{{ getFixed(item) }}{{ item.unit }}</div>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-col>
        <!--  -->
        <a-col :span="18" class="h-100pre">
          <div class="w-100pre h-100pre rounded-6">
            <!-- d：白天 n：夜晚 -->
            <a-row
              :gutter="[0]"
              class="h-100prei top-right-bg"
              :style="{
                backgroundImage: `url(${require(`@/assets/weatherIcon/bg/bg-R-${weatherBg(
                  WeatherInfo.weatherCode
                )}.png`)})`,
              }"
              :class="{ night: WeatherInfo.pod == 'n' }"
            >
              <a-col :span="4" class="h-100pre rounded-6">
                <div class="flex align-center justify-end h-100pre rounded-6">
                  <div class="py-22 px-19 typebg rounded-6">
                    <div class="text-fff w-83 text-center">
                      <div
                        @click="changeType(index, item)"
                        class="rounded-12 mb-16 pointer default-border"
                        :class="{ active: index == current }"
                        v-for="(item, index) in weatherType"
                        :key="item.icon"
                      >
                        {{ item.name }}
                      </div>
                    </div>
                  </div>
                </div>
              </a-col>
              <a-col :span="1" class="h-100prei">
                <div class="h-100pre flex align-center justify-center">
                  <div class="line"></div>
                </div>
              </a-col>
              <a-col :span="19" class="h-100prei">
                <div id="top-echarts" class="w-100pre h-100prei"></div>
              </a-col>
            </a-row>
          </div>
        </a-col>
      </div>
      <!-- 未来7天风力趋势 -->
      <div class="content-height" v-for="item in echartsDomIds" :key="item.id">
        <a-col :span="24" class="h-100pre">
          <div class="h-100pre flex flex-column bg-fff px-16 rounded-6">
            <div class="w-100pre flex align-center pt-16 pb-10" style="border-bottom: 1px solid #e8e8e8">
              <div class="blue-line"></div>
              <div class="" style="font-weight: bold">{{ item.name }}</div>
            </div>
            <div class="w-100pre flex-1" :id="item.id"></div>
          </div>
        </a-col>
      </div>
    </a-row>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import * as echarts from 'echarts'
import moment from 'moment'
import seaCode from '../../../../../public/static/seaCode.json'
export default {
  data() {
    return {
      current: 0,
      weatherType: [
        {
          name: '气温',
          unit: '℃',
          key: 'temperature',
          formatter: (params) => {
            return `
            <div style="background-color: black;border-radius: 5px;padding:5px;text-align:center">
                        <img src="${require(`@/assets/weatherIcon/icon/${params.weatherCode}.png`)}" style="width: 32px; height: 32px;margin-bottom:5px">
                        <div style="color: white; font-size: 14px;">${params.value}℃</div>
                      </div>`
          },
          hasIcon: true,
          min: -10,
          max: 40,
        },
        {
          name: '风速',
          unit: 'm/s',
          key: 'windSpeed',
          formatter: (params) => {
            return `
            <div style="background-color: black;border-radius: 5px;padding:5px;text-align:center">
                        <div style="color: white; font-size: 14px;">${
                          this.getJsonLevel(params.value, 'wind_levels').description
                        }</div>
                        <div style="color: white; font-size: 14px;">${params.value}m/s</div>
                      </div>`
          },
          hasIcon: false,
          max: 35,
        },
        // {
        //   name: '浪高',
        //   unit: 'm',
        //   key: 'waveHeight',
        //   formatter: (params) => {
        //     return `
        //     <div style="background-color: black;border-radius: 5px;padding:5px;text-align:center">
        //                 <div style="color: white; font-size: 14px;">${
        //                   this.getJsonLevel(params.value, 'wave_height_levels').level
        //                 }</div>
        //                 <div style="color: white; font-size: 14px;">${params.value}m</div>
        //               </div>`
        //   },
        //   hasIcon: false,
        //   max: 5,
        // },
        {
          name: '能见度',
          unit: 'Km',
          key: 'visibility',
          formatter: (params) => {
            return `
            <div style="background-color: black;border-radius: 5px;padding:5px;text-align:center">
                        <div style="color: white; font-size: 14px;">能见度</div>
                        <div style="color: white; font-size: 14px;">${params.value}Km</div>
                      </div>`
          },
          hasIcon: false,
        },
        {
          name: '降水量',
          unit: 'mm',
          key: 'precipitation',
          formatter: (params) => {
            return `
            <div style="background-color: black;border-radius: 5px;padding:5px;text-align:center">
                        <div style="color: white; font-size: 14px;">降水量</div>
                        <div style="color: white; font-size: 14px;">${params.value}mm</div>
                      </div>`
          },
          hasIcon: false,
          max: 40,
        },
      ],
      todayInfo: [
        {
          name: '相对湿度',
          value: '0',
          unit: '%',
          key: 'relaHumidity',
          bit: 0,
        },
        {
          name: '紫外线指数',
          value: '最弱',
          key: 'ultravioletRays',
          unit: '级',
          bit: 0,
        },
        {
          name: '体感',
          value: '0',
          unit: '°',
          key: 'appTemp',
          bit: null,
        },
        {
          name: '能见度',
          value: '0',
          unit: 'Km',
          key: 'visibility',
          bit: null,
        },
        {
          name: '降水量',
          value: '0.0',
          unit: 'mm',
          key: 'precipitation',
          bit: 1,
        },
        {
          name: '气压',
          value: '0',
          unit: 'mb',
          key: 'pressure',
          bit: null,
        },
      ],
      WeatherInfo: {
        cityName: '北京',
        temperature: '0',
        weatherCode: '1036',
        windMark: '',
        windLevel: 0,
        windDirection: 0,
        lat: 0,
        lon: 0,
        pod: 'd',
      },
      nowTime: '2022-03-10 10:00:00',
      timer: '',
      sevenDayTemperature: {},
      sevenDaySpeed: {},
      sevenDayVisibility: {},
      sevenDayWave: {},
      sevenDayPrecipitation: {},
      topEacherts: {},
      todayList: [],
      echartsDomIds: [
        {
          name: '未来7天温度趋势',
          id: 'temp-echarts',
          funcName: 'initTempEcharts',
          example: 'sevenDayTemperature',
        },
        {
          name: '未来7天风力趋势',
          id: 'speed-echarts',
          funcName: 'initSpeedEcharts',
          example: 'sevenDaySpeed',
        },
        {
          name: '未来7天能见度趋势',
          id: 'visibility-echarts',
          funcName: 'initVisibilityEcharts',
          example: 'sevenDayVisibility',
        },
        // {
        //   name: '未来7天浪高趋势',
        //   id: 'wave-echarts',
        //   funcName: 'initWaveEcharts',
        //   example: 'sevenDayWave',
        // },
        {
          name: '未来7天降水量趋势',
          id: 'precipitation-echarts',
          funcName: 'initPrecipitationEcharts',
          example: 'sevenDayPrecipitation',
        },
      ],
    }
  },
  created() {
    this.initTopEacherts()
    // 7天
    this.echartsDomIds.forEach((item) => {
      if (typeof this[item.funcName] === 'function') {
        this[item.funcName](item.id, item.example)
      }
    })
  },
  mounted() {
    this.timer = setInterval(() => {
      this.nowTime = moment().format('YYYY-MM-DD HH:mm:ss')
    }, 1000)
    this.initData()
  },
  computed: {
    weatherName() {
      return seaCode.weather[this.WeatherInfo.weatherCode]
    },
  },
  watch: {},
  methods: {
    weatherBg(code) {
      if (code == 1031 || code == 1036) return '2'
      else if (code == 1032 || code == 1033) return '1'
      else if (code == 1025 || code == 1026 || code == 1027 || code == 1028 || code == 1029) return '6'
      else if (code == 1016 || code == 1017 || code == 1018 || code == 1019 || code == 1022) return '5'
      else if (code == 1035) return '3'
      else return '4'
    },
    getFixed(item) {
      if (item.bit) {
        return Number(item.value).toFixed(item.bit)
      } else {
        return item.value
      }
    },
    changeType(index, item) {
      this.current = index
      this.set24HourWeather(this.todayList, item)
    },
    initData() {
      getAction('/sea/weatherInfo/get7dayInfo').then((res) => {
        console.log('get7dayInfo', res)
        const { success, result } = res
        if (!success) {
          this.$message.error('七天数据返回异常')
          return
        }
        if (result) {
          const { hours, now, today } = result
          // 当天连续时间
          this.todayList = today
          this.set24HourWeather(today, this.weatherType[this.current])
          // 7天预测准备数据
          let xName = []
          let temperatureValue = []
          let speedValue = []
          let windDirectionList = [] //风向
          let visibilityValue = []
          let waveHeightValue = []
          let precipitationValue = []
          hours.forEach((item) => {
            temperatureValue.push(item.temperature)
            xName.push(item.timestamp)
            speedValue.push(item.windSpeed)
            windDirectionList.push(item.windDirection)
            visibilityValue.push(item.visibility)
            waveHeightValue.push(item.waveHeight)
            precipitationValue.push(item.precipitation)
          })
          this.setChartOptions('sevenDayTemperature', temperatureValue, xName)
          this.setChartOptions('sevenDaySpeed', speedValue, xName, windDirectionList)
          this.setChartOptions('sevenDayVisibility', visibilityValue, xName)
          this.setChartOptions('sevenDayWave', waveHeightValue, xName)
          this.setChartOptions('sevenDayPrecipitation', precipitationValue, xName)
          // 当天天气信息
          this.todayInfo.forEach((element) => {
            element.value = now[element.key]
          })
          const { temperature, cityName, weatherCode, windMark, windSpeed, windDirection, lat, lon, pod } = now
          this.WeatherInfo = {
            temperature,
            cityName,
            weatherCode,
            windMark,
            windLevel: this.getJsonLevel(windSpeed, 'wind_levels').level,
            windDirection,
            lat,
            lon,
            pod,
          }
        }
      })
    },

    setChartOptions(exampleName, data, xData, series1) {
      let timer = setInterval(() => {
        if (this[exampleName].chart) {
          clearInterval(timer)
          let { chart, option } = this[exampleName]
          option.series[0].data = data
          if (series1) {
            option.series[1].data = series1 //备用数据提供给tool 使用
          }
          option.xAxis.data = xData
          option.xAxis.axisLabel = {
            hideOverlap: true,
            interval: 0,
            formatter: (value, index) => {
              console.log('index', index)
              if (index === 0) {
                return moment(Number(value)).format('YYYY-MM-DD') // 第一个刻度始终显示
              } else {
                let xOption = option.xAxis.data[index - 1]
                const prevValue = moment(xOption).format('YYYY-MM-DD')
                let day = moment(Number(value)).format('YYYY-MM-DD')
                if (day !== prevValue) {
                  return day // 与前一个刻度不同的日期才显示标签
                } else {
                  return '' // 与前一个刻度相同的日期不显示标签
                }
              }
            },
          }
          chart.setOption(option)
        }
      }, 300)
    },
    set24HourWeather(data, item) {
      console.log('item', item)
      const setData = () => {
        let y = []
        let x = []
        let back = []
        data.forEach((i) => {
          y.push(i[item.key])
          x.push(moment(i.timestamp).format('HH:mm'))
          back.push(i.weatherCode)
        })
        this.topEacherts.option.series[0].data = y
        this.topEacherts.option.series[1].data = back
        this.topEacherts.option.xAxis.data = x
        // 设置单位
        this.topEacherts.option.yAxis.axisLabel.formatter = `{value} ${this.weatherType[this.current].unit}`
        // 设置鼠标悬停显示
        this.topEacherts.option.tooltip.formatter = (params) => {
          // 判断是不是需要图片
          if (item.hasIcon) {
            params.weatherCode = this.topEacherts.option.series[1].data[params.dataIndex]
          }
          let str = item.formatter(params)
          return str
        }
        if (item.hasOwnProperty('max')) {
          this.topEacherts.option.yAxis.max = item.max
        } else {
          this.topEacherts.option.yAxis.max = null
        }
        this.topEacherts.chart.setOption(this.topEacherts.option)
      }
      if (this.topEacherts.chart) {
        setData()
        return
      }
      let timer = setInterval(() => {
        if (this.topEacherts.chart) {
          clearInterval(timer)
          setData()
        }
      }, 300)
    },
    getJsonLevel(value, type) {
      for (let item of seaCode[type]) {
        if (value >= item.range.min && value <= item.range.max) {
          return item
        }
      }
      return item
    },
    // 24小时
    initTopEacherts() {
      // 设置定时器
      let chartDom
      let checkExistenceInterval = setInterval(() => {
        chartDom = document.getElementById('top-echarts')
        if (chartDom) {
          clearInterval(checkExistenceInterval)
          initEacherts()
        }
      }, 500)
      const initEacherts = () => {
        let myChart = echarts.init(chartDom)
        let option
        option = {
          grid: {
            top: '15%',
            bottom: '15%',
            left: '5%',
            right: '2%',
          },
          xAxis: {
            type: 'category',
            data: [
              '00:00',
              '02:00',
              '04:00',
              '06:00',
              '08:00',
              '10:00',
              '12:00',
              '14:00',
              '16:00',
              '18:00',
              '20:00',
              '22:00',
              '00:00',
            ],
            axisLine: {
              lineStyle: {
                color: 'white', // 设置刻度颜色为白色
              },
            },
            axisLabel: {
              interval: 1, // 设置每隔一个显示一个刻度
            },
            axisTick: {
              alignWithLabel: true, // 刻度线与标签对齐
              lineStyle: {
                color: 'white', // 设置刻度线颜色为白色
              },
            },
          },
          yAxis: {
            type: 'value',
            axisTick: {
              show: false, // 隐藏刻度线
            },
            axisLabel: {
              formatter: '{value} ℃', // 在刻度值后面加上℃
            },
            axisLine: {
              lineStyle: {
                color: 'white', // 设置刻度颜色为白色
              },
            },
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                color: 'rgba(201, 205, 212, 0.30)', // 设置虚线颜色为白色
                type: 'dashed', // 设置线型为虚线
              },
            },
          },
          tooltip: {
            padding: 0,
            borderWidth: 0,
            // 设置鼠标悬停时显示的信息
            formatter: function (params) {
              // 返回自定义的tooltip内容
              return `<div style="background-color: black;border-radius: 5px;padding:5px;text-align:center">
                        <img src="${require('@/assets/weatherIcon/icon/1036.png')}" style="width: 32px; height: 32px;margin-bottom:5px">
                        <div style="color: white; font-size: 14px;">${params.value}℃</div>
                      </div>` // 显示图标和数据点的值
            },
          },

          series: [
            {
              data: [],
              type: 'line',
              lineStyle: {
                color: 'white', // 设置连接线颜色为白色
              },
              itemStyle: {
                color: 'white', // 设置拐点的圆为白色
              },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'white', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(255, 255, 255, 0)', // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
            },
            {
              data: [],
            },
          ],
        }
        option && myChart.setOption(option)
        this.topEacherts = {
          chart: myChart,
          option: option,
        }
      }
    },
    // 未来7天温度趋势
    initTempEcharts(id, example) {
      let chartDom
      let checkExistenceInterval = setInterval(() => {
        chartDom = document.getElementById(id)
        if (chartDom) {
          clearInterval(checkExistenceInterval)
          initEacherts()
        }
      }, 500)
      const initEacherts = () => {
        let myChart = echarts.init(chartDom)
        let option
        option = {
          tooltip: {
            trigger: 'axis', // 设置触发类型为坐标轴触发
            formatter: (params) => {
              // params 是一个数组，包含着该点所有数据信息
              let xValue = moment(Number(params[0].axisValue)).format('YYYY-MM-DD HH:mm:ss') // x 轴数据
              let yValue = params[0].value // y 轴数据
              return `
              <div >
                <div style="margin-bottom:10px;font-size: 14px;">温度：<span style="color: blue;">${yValue}°C</span></div>
                <div style='color: #86909C;font-size: 12px;'>${xValue}</div>
              </div>
              `
            },
          },
          grid: {
            top: '15%',
            bottom: '10%',
            left: '2%',
            right: '1%',
          },
          xAxis: {
            splitNumber: 100,
            type: 'category',
            data: [],
            // moment(item.timestamp).format('YYYY-MM-DD')
            axisLine: {
              lineStyle: {
                color: 'rgba(134, 144, 156, 1)', // 设置刻度颜色为白色
              },
            },
            axisTick: {
              show: false, // 隐藏 X 轴的刻度线
            },
          },
          yAxis: {
            type: 'value',
            name: '(°C)', // 设置 Y 轴顶部的单位
            max: 40,
            min: '-10',
            axisLine: {
              lineStyle: {
                color: 'rgba(134, 144, 156, 1)', // 设置刻度颜色为白色
              },
              show: true, // 显示 Y 轴轴线
            },
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                color: 'rgba(201, 205, 212, 0.30)', // 设置虚线颜色为白色
                type: 'dashed', // 设置线型为虚线
              },
            },
          },
          series: [
            {
              data: [12, 15, 10, 22, 20, 11, 14],
              type: 'line',
              smooth: true,
              symbol: 'none', // 关闭拐点显示
              itemStyle: {
                color: 'rgba(50, 84, 255, 1)',
              },
            },
          ],
        }
        option && myChart.setOption(option)
        this[example] = {
          chart: myChart,
          option: option,
        }
      }
    },
    //未来7天风力趋势
    initSpeedEcharts(id, example) {
      let chartDom
      let checkExistenceInterval = setInterval(() => {
        chartDom = document.getElementById(id)
        if (chartDom) {
          clearInterval(checkExistenceInterval)
          initEacherts()
        }
      }, 500)
      const initEacherts = () => {
        let myChart = echarts.init(chartDom)
        let option
        option = {
          tooltip: {
            trigger: 'axis', // 设置触发类型为坐标轴触发
            formatter: (params) => {
              // params 是一个数组，包含着该点所有数据信息
              let xValue = moment(Number(params[0].axisValue)).format('YYYY-MM-DD HH:mm:ss') // x 轴数据
              let yValue = params[0].value // y 轴数据
              return `
              <div >
                <div style="margin-bottom:10px;font-size: 14px;">等级：<span style="color: blue;">${
                  this.getJsonLevel(yValue, 'wind_levels').level
                }级</span></div>
                <div style="margin-bottom:10px;font-size: 14px;">风速: <span style="color: blue;">${yValue}m/s</span></div>
                <div style="margin-bottom:10px;font-size: 14px;">风向: <span style="color: blue;">${
                  this.getJsonLevel(option.series[1].data[params[0].dataIndex], 'wind_directions').direction
                }</span></div>
                <div style='color: #86909C;font-size: 12px;'>${xValue}</div>
              </div>
              `
            },
          },
          grid: {
            top: '15%',
            bottom: '10%',
            left: '2%',
            right: '1%',
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLine: {
              lineStyle: {
                color: 'rgba(134, 144, 156, 1)', // 设置刻度颜色为白色
              },
            },
            axisTick: {
              show: false, // 隐藏 X 轴的刻度线
            },
          },
          yAxis: {
            type: 'value',
            name: '(m/s)', // 设置 Y 轴顶部的单位
            max: 35,
            axisLine: {
              lineStyle: {
                color: 'rgba(134, 144, 156, 1)', // 设置刻度颜色为白色
              },
              show: true, // 显示 Y 轴轴线
            },
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                color: 'rgba(201, 205, 212, 0.30)', // 设置虚线颜色为白色
                type: 'dashed', // 设置线型为虚线
              },
            },
          },
          series: [
            {
              data: [10, 16, 14, 18, 21, 17, 13],
              type: 'line',
              smooth: true,
              symbol: 'none', // 关闭拐点显示
              itemStyle: {
                color: 'rgba(50, 84, 255, 1)',
              },
            },
            {
              // 风向参数
              data: [10, 16, 14, 18, 21, 17, 13],
            },
          ],
        }
        option && myChart.setOption(option)
        this[example] = {
          chart: myChart,
          option: option,
        }
      }
    },
    //未来7天能见度趋势
    initVisibilityEcharts(id, example) {
      let chartDom
      let checkExistenceInterval = setInterval(() => {
        chartDom = document.getElementById(id)
        if (chartDom) {
          clearInterval(checkExistenceInterval)
          initEacherts()
        }
      }, 500)
      const initEacherts = () => {
        let myChart = echarts.init(chartDom)
        let option
        option = {
          tooltip: {
            trigger: 'axis', // 设置触发类型为坐标轴触发
            formatter: (params) => {
              // params 是一个数组，包含着该点所有数据信息
              let xValue = moment(Number(params[0].axisValue)).format('YYYY-MM-DD HH:mm:ss') // x 轴数据
              let yValue = params[0].value // y 轴数据
              return `
              <div >
                <div style="margin-bottom:10px;font-size: 14px;">能见度：<span style="color: blue;">${yValue}Km</span></div>
                <div style='color: #86909C;font-size: 12px;'>${xValue}</div>
              </div>
              `
            },
          },
          grid: {
            top: '15%',
            bottom: '10%',
            left: '2%',
            right: '1%',
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLine: {
              lineStyle: {
                color: 'rgba(134, 144, 156, 1)', // 设置刻度颜色为白色
              },
            },
            axisTick: {
              show: false, // 隐藏 X 轴的刻度线
            },
          },
          yAxis: {
            type: 'value',
            name: '(Km)', // 设置 Y 轴顶部的单位
            axisLine: {
              lineStyle: {
                color: 'rgba(134, 144, 156, 1)', // 设置刻度颜色为白色
              },
              show: true, // 显示 Y 轴轴线
            },
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                color: 'rgba(201, 205, 212, 0.30)', // 设置虚线颜色为白色
                type: 'dashed', // 设置线型为虚线
              },
            },
          },
          series: [
            {
              data: [10, 16, 14, 18, 21, 17, 13],
              type: 'line',
              smooth: true,
              symbol: 'none', // 关闭拐点显示
              itemStyle: {
                color: 'rgba(50, 84, 255, 1)',
              },
            },
            {
              // 风向参数
              data: [10, 16, 14, 18, 21, 17, 13],
            },
          ],
        }
        option && myChart.setOption(option)
        this[example] = {
          chart: myChart,
          option: option,
        }
      }
    },
    //未来7天浪高趋势
    initWaveEcharts(id, example) {
      let chartDom
      let checkExistenceInterval = setInterval(() => {
        chartDom = document.getElementById(id)
        if (chartDom) {
          clearInterval(checkExistenceInterval)
          initEacherts()
        }
      }, 500)
      const initEacherts = () => {
        let myChart = echarts.init(chartDom)
        let option
        option = {
          tooltip: {
            trigger: 'axis', // 设置触发类型为坐标轴触发
            formatter: (params) => {
              // params 是一个数组，包含着该点所有数据信息
              let xValue = moment(Number(params[0].axisValue)).format('YYYY-MM-DD HH:mm:ss') // x 轴数据
              let yValue = params[0].value // y 轴数据
              return `
              <div >
                <div style="margin-bottom:10px;font-size: 14px;">等级：<span style="color: blue;">${
                  this.getJsonLevel(yValue, 'wave_height_levels').level
                }</span></div>
                <div style="margin-bottom:10px;font-size: 14px;">浪高：<span style="color: blue;">${yValue}m</span></div>
                <div style='color: #86909C;font-size: 12px;'>${xValue}</div>
              </div>
              `
            },
          },
          grid: {
            top: '15%',
            bottom: '10%',
            left: '2%',
            right: '1%',
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLine: {
              lineStyle: {
                color: 'rgba(134, 144, 156, 1)', // 设置刻度颜色为白色
              },
            },
            axisTick: {
              show: false, // 隐藏 X 轴的刻度线
            },
          },
          yAxis: {
            type: 'value',
            name: '(m)', // 设置 Y 轴顶部的单位
            max: 5,
            axisLine: {
              lineStyle: {
                color: 'rgba(134, 144, 156, 1)', // 设置刻度颜色为白色
              },
              show: true, // 显示 Y 轴轴线
            },
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                color: 'rgba(201, 205, 212, 0.30)', // 设置虚线颜色为白色
                type: 'dashed', // 设置线型为虚线
              },
            },
          },
          series: [
            {
              data: [10, 16, 14, 18, 21, 17, 13],
              type: 'line',
              smooth: true,
              symbol: 'none', // 关闭拐点显示
              itemStyle: {
                color: 'rgba(50, 84, 255, 1)',
              },
            },
            {
              // 风向参数
              data: [10, 16, 14, 18, 21, 17, 13],
            },
          ],
        }
        option && myChart.setOption(option)
        this[example] = {
          chart: myChart,
          option: option,
        }
      }
    },
    //未来7天降水量趋势
    initPrecipitationEcharts(id, example) {
      let chartDom
      let checkExistenceInterval = setInterval(() => {
        chartDom = document.getElementById(id)
        if (chartDom) {
          clearInterval(checkExistenceInterval)
          initEacherts()
        }
      }, 500)
      const initEacherts = () => {
        let myChart = echarts.init(chartDom)
        let option
        option = {
          tooltip: {
            trigger: 'axis', // 设置触发类型为坐标轴触发
            formatter: (params) => {
              // params 是一个数组，包含着该点所有数据信息
              let xValue = moment(Number(params[0].axisValue)).format('YYYY-MM-DD HH:mm:ss') // x 轴数据
              let yValue = params[0].value // y 轴数据
              return `
              <div >
                <div style="margin-bottom:10px;font-size: 14px;">降雨量：<span style="color: blue;">${yValue}mm</span></div>
                <div style='color: #86909C;font-size: 12px;'>${xValue}</div>
              </div>
              `
            },
          },
          grid: {
            top: '15%',
            bottom: '10%',
            left: '2%',
            right: '1%',
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLine: {
              lineStyle: {
                color: 'rgba(134, 144, 156, 1)', // 设置刻度颜色为白色
              },
            },
            axisTick: {
              show: false, // 隐藏 X 轴的刻度线
            },
          },
          yAxis: {
            type: 'value',
            name: '(mm/hr)', // 设置 Y 轴顶部的单位
            max: 40,
            axisLine: {
              lineStyle: {
                color: 'rgba(134, 144, 156, 1)', // 设置刻度颜色为白色
              },
              show: true, // 显示 Y 轴轴线
            },
            splitLine: {
              show: true, // 显示分隔线
              lineStyle: {
                color: 'rgba(201, 205, 212, 0.30)', // 设置虚线颜色为白色
                type: 'dashed', // 设置线型为虚线
              },
            },
          },
          series: [
            {
              data: [10, 16, 14, 18, 21, 17, 13],
              type: 'line',
              smooth: true,
              symbol: 'none', // 关闭拐点显示
              itemStyle: {
                color: 'rgba(50, 84, 255, 1)',
              },
            },
            {
              // 风向参数
              data: [10, 16, 14, 18, 21, 17, 13],
            },
          ],
        }
        option && myChart.setOption(option)
        this[example] = {
          chart: myChart,
          option: option,
        }
      }
    },
    // 地理位置转换
    decimalDegreesToDMS(decimalDegrees) {
      let degrees = Math.floor(decimalDegrees)
      let minutes = Math.floor((decimalDegrees - degrees) * 60)
      let seconds = ((decimalDegrees - degrees - minutes / 60) * 3600).toFixed(0)
      return Math.abs(degrees) + '°' + minutes + '′' + seconds + '″'
    },
  },
  destroyed() {
    clearInterval(this.timer)
  },
}
</script>

<style lang="scss" scoped>
.page-content {
  .a-row {
    width: 100% !important;
    height: 100% !important;
    .content-height {
      height: 320px;
    }
  }
}
.night {
  background-color: #5461a6 !important; //#5461A6
}
.top-left-bg {
  background-color: #066fec; //#1e42ee
  // background-image: url('../../../../assets/night.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 100%;
  height: 100%;
}
.top-right-bg {
  background-color: #066fec; //#1e42ee
  // background-image: url('../../../../assets/day.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 6px;
}
.line {
  background: linear-gradient(to bottom, rgba(83, 111, 253, 0) 0%, rgba(83, 111, 253, 1) 100%);
  width: 1px;
  height: 120px;
}
.active {
  color: #3254ff;
  background-color: white;
  outline: none !important;
}
.default-border {
  outline: #999 1px solid;
}
.typebg {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.18), rgba(246, 246, 246, 0.02) 100%);
  border-radius: 4px 4px 4px 4px;
  border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.18), rgba(255, 255, 255, 0)) 1 1;
}
.blue-line {
  width: 4px;
  height: 10px;
  background-color: #0096ff;
  margin-right: 5px;
}
.content-top {
  .bottom {
    border-radius: 4px 4px 4px 4px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.18), rgba(246, 246, 246, 0.02) 100%);
    border-radius: 6px 6px 6px 6px;
    border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.18), rgba(255, 255, 255, 0.02)) 1 1;
  }
}
</style>
