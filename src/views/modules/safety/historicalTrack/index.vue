<template>
  <left-right-layout :spanConfig="{ left: 6, right: 18 }">
    <template #left>
      <a-card :bordered="false" class="left-card">
        <div class="calendar">
          <a-calendar :fullscreen="false" v-model="alarmDay" value="month" @select="alarmDaySelect" />
        </div>
        <a-divider />
        <div class="title">{{ alarmDay.format('YYYY年MM月DD日') }} 场内船舶活动记录</div>
        <div class="chart-box">
          <div id="pie"></div>
          <div class="legend">
            <div>
              <span class="icon"></span>
              <span class="name">内部船舶</span>
              <span class="num"
                >{{ shipTotal ? Math.ceil(((statisticsData.interior || 0) / shipTotal) * 100) : '0' }}%</span
              >
            </div>
            <div>
              <span class="icon"></span>
              <span class="name">外部船舶</span>
              <span class="num"
                >{{ shipTotal ? Math.ceil(((statisticsData.without || 0) / shipTotal) * 100) : '0' }}%</span
              >
            </div>
          </div>
        </div>
        <div class="card-box">
          <div v-for="(value, key) in statisticsData.shipType || {}" :key="key">
            <div class="icon">
              <img :src="require('@/assets/image/historicalTrackMap/shipIcon.png')" />
            </div>
            <div class="detail">
              <div class="name">{{ key }}</div>
              <div class="num">{{ value }}</div>
            </div>
          </div>
        </div>
      </a-card>
    </template>
    <template #right>
      <a-card :bordered="false" class="right-card">
        <table-layout
          :search-props="searchProps"
          :table-props="tableProps"
          @init-params="initParams"
          @search-submit="loadData"
          @table-change="loadData"
        >
          <template #belong="{ record }">
            <span :class="'belongTag belongTag-' + record.belong">{{
              record.belong == 'interior' ? '内部船舶' : record.belong == 'without' ? '外部船舶' : ''
            }}</span>
          </template>
          <template #isIn="{ record }">
            <span>{{
              record.isIn == '0' ? '否' : record.isIn == '1' ? '是' : ''
            }}</span>
          </template>
        </table-layout>
      </a-card>
    </template>
  </left-right-layout>
</template>
    
    <script>
import { getAction, postAction, deleteAction } from '@/api/manage'
import { Calendar as ACalendar } from 'ant-design-vue'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import LeftRightLayout from '@/components/tableLayout/LeftRightLayout.vue'
import TableLayout from '@/components/tableLayout/TableLayout.vue'
import moment from 'moment'
import * as echarts from 'echarts'
import { nextTick } from 'vue'
export default {
  name: 'historicalTrack',
  //   mixins: [JeecgListMixin],
  components: {
    LeftRightLayout,
    ACalendar,
    TableLayout,
  },
  data() {
    return {
      meageParams: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      total: 0,
      data: [],
      url: {
        list: '/ship/shipAlarm/list',
        childList: '/ProjectInfo/qzhfProjectPbs/childList',
        getChildListBatch: '/ProjectInfo/qzhfProjectPbs/getChildListBatch',
        delete: '/ProjectInfo/qzhfProjectPbs/delete',
        deleteBatch: '/ProjectInfo/qzhfProjectPbs/deleteBatch',
        moveUp: 'ProjectInfo/qzhfProjectPbs/moveUp',
        moveDown: 'ProjectInfo/qzhfProjectPbs/moveDown',
        statistics: '/ship/shipAlarm/queryStatistics',
      },
      // 搜索组件的props
      searchProps: {
        formModel: {
          type: null,
          name: null,
        },
        formItems: [
          { key: 'shipName', label: '船舶名称', type: 'text' },
          {
            key: 'belong',
            label: '船舶归属',
            type: 'select',
            options: [
              { label: '内部船舶', value: 'interior' },
              { label: '外部船舶', value: 'without' },
            ],
          },
          {
            key: 'isIn',
            label: '进入围栏',
            type: 'select',
            options: [
              { label: '是', value: '1' },
              { label: '否', value: '0' },
            ],
          },
        ],
      },
      // 表格组件的props
      tableProps: {
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '船舶名称',
            align: 'center',
            dataIndex: 'shipName',
          },
          {
            title: 'MMSI',
            align: 'center',
            dataIndex: 'mmsi',
          },
          {
            title: '类型',
            align: 'center',
            dataIndex: 'shipTypeName',
          },
          {
            title: '船舶归属',
            align: 'center',
            dataIndex: 'belong',
            scopedSlots: { customRender: 'belong' },
          },
          {
            title: '进入围栏',
            align: 'center',
            dataIndex: 'isIn',
            scopedSlots: { customRender: 'isIn' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看轨迹',
            handler: this.handleDetail,
          },
        ],
      },
      alarmDay: moment(),
      statisticsData: {},
      shipTotal: 0,
    }
  },
  mounted() {
    // nextTick(() => {
    this.loadData(this.meageParams)
    this.getStatistics()
    // })
  },
  computed: {},
  watch: {},
  methods: {
    initParams(params) {
      this.meageParams = params
    },
    /**
     * @description 日期切换
     */
    alarmDaySelect(date) {
      this.alarmDay = date
      this.loadData(this.meageParams)
      this.getStatistics()
    },
    /**
     * @description 加载列表
     */
    loadData(data) {
      let params = {
        ...data.params,
      }
      params.alarmDay = this.alarmDay.format('YYYY-MM-DD')
      getAction(this.url.list, params).then((res) => {
        this.tableProps.dataSource = res.result.records
        this.tableProps.ipagination.total = res.result.total
      })
    },
    onShowSizeChange(current, pageSize) {
      this.pageSize = pageSize
    },
    onChange(pageNumber) {
      this.current = pageNumber
    },
    handleDetail({ mmsi, shipName, alarmDay }) {
      this.$router.push({
        path: `/historicalTrackMap/${mmsi}/${shipName}/${alarmDay}`,
      })
    },
    /**
     * @description 分析统计
     */
    getStatistics() {
      getAction(this.url.statistics, { alarmDay: this.alarmDay.format('YYYY-MM-DD') }).then((res) => {
        this.statisticsData = res.result || {}
        let { interior, without } = res.result || {}
        this.shipTotal = (interior || 0) + (without || 0)
        this.initChart()
      })
    },
    /**
     * @description 图表
     */
    initChart() {
      var chartDom = document.getElementById('pie')
      var myChart = echarts.init(chartDom)
      var option
      option = {
        tooltip: {
          trigger: 'item',
          show: false,
        },
        title: {
          text: (this.statisticsData.interior || 0) + (this.statisticsData.without || 0),
          subtext: '船舶总数',
          left: 'center',
          top: '35%',
        },
        legend: {
          right: '5%',
          top: 'center',
          orient: 'vertical',
          icon: 'circle',
          show: false,
          formatter: function (name) {
            return `${name}`
          },
        },
        color: ['#3254FF', '#FF7C32'],
        series: [
          {
            type: 'pie',
            radius: ['55%', '70%'],
            left: 0,
            label: {
              show: true,
              formatter: '{c}艘',
            },
            data: [
              { value: this.statisticsData.interior || 0, name: '内部船舶' },
              { value: this.statisticsData.without || 0, name: '外部船舶' },
            ],
          },
        ],
      }

      option && myChart.setOption(option)
    },
  },
}
</script>
<style scoped lang="less">
@import '~@assets/less/common.less';
.left-card,
.right-card {
  height: 100%;
  ::v-deep .ant-card-body {
    height: 100%;
    padding: 0;
  }
}
.left-card {
  .calendar {
    margin: 0 8px;
    border-radius: 2px;
    border: 1px solid #e5e6eb;
    height: 270px;
    > div {
      height: 100%;
      /deep/ .ant-fullcalendar-header {
        background: #f2f3f5;
        .ant-radio-group {
          display: none;
        }
      }
      /deep/ .ant-fullcalendar {
        height: calc(100% - 46px);
        .ant-fullcalendar-calendar-body {
          height: 100%;
          table {
            height: 100%;
          }
        }
      }
    }
    
  }
  .ant-divider {
    margin: 16px 0;
  }
  > .title {
    height: 24px;
    font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
    font-size: 14px;
    color: #1d2129;
    line-height: 24px;
    margin: 0 8px;
    &::before {
      display: inline-block;
      content: '';
      width: 3px;
      height: 10px;
      margin-right: 7px;
      background: #3254ff;
    }
  }
  .chart-box {
    margin: 8px;
    height: 140px;
    background: #f0f5ff;
    border-radius: 2px;
    display: flex;
    #pie {
      height: 100%;
      width: calc(100% - 128px);
    }
    .legend {
      width: 128px;
      height: 43px;
      margin-top: 52px;
      // transform: translateY(-50%);
      padding-left: 21px;
      border-left: 1px solid #e5e6eb;
      > div {
        display: flex;
        align-items: center;
        .icon {
          width: 6px;
          height: 6px;
          background: #3254ff;
          margin-right: 4px;
          display: inline-block;
          border-radius: 50%;
        }
        .name {
          font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
          font-size: 10px;
          color: #999999;
          line-height: 15px;
          margin-right: 9px;
          display: inline-block;
        }
        .num {
          font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
          font-size: 12px;
          color: #3254ff;
          line-height: 20px;
        }
        &:first-child {
          .icon {
            background: #3254ff;
          }
          .num {
            color: #3254ff;
          }
        }
        &:last-child {
          .icon {
            background: #ff7c32;
          }
          .num {
            color: #ff7c32;
          }
        }
      }
    }
  }
  .card-box {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    padding: 0 8px;
    gap: 8px;
    max-height: calc(100% - 270px - 33px - 21px - 156px);
    overflow: overlay;
    > div {
      height: 64px;
      background: #edf0ff;
      border-radius: 2px;
      padding: 8px 0 8px 12px;
      display: flex;
      align-items: center;
      .icon {
        width: 40px;
        height: 32px;
        padding-right: 8px;
        border-right: 1px solid #c4ceff;
        margin-right: 8px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .detail {
        .name {
          font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
          font-size: 14px;
          color: #1d2129;
          line-height: 22px;
          margin-bottom: 2px;
        }
        .num {
          font-family: Roboto, Roboto;
          font-weight: 500;
          font-size: 14px;
          color: #3254ff;
          line-height: 24px;
        }
      }
    }
  }
}
.right-card {
  ::v-deep .ant-table {
    .belongTag {
      width: 72px;
      height: 22px;
      border-radius: 11px;
      display: inline-block;
    }
    .belongTag-interior {
      background: rgba(50, 84, 255, 0.15);
      color: #3254ff;
      border: 1px solid #3254ff;
    }
    .belongTag-without {
      background: rgba(255, 125, 0, 0.2);
      color: #ff7d00;
      border: 1px solid #ff7d00;
    }
  }
}
</style>
    