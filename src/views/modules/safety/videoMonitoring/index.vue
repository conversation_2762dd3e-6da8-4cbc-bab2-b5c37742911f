<template>
  <left-right-layout>
    <template #left>
      <a-card :bordered="false">
        <a-tree
          @select="onSelect"
          :treeData="treeData"
          :defaultExpandAll="true"
          :replaceFields="{ title: 'name', key: 'id' }"
        />
      </a-card>
    </template>
    <template #right>
      <!-- <a-card :bordered="true" style="height: 100%"> -->
        <div id="mse" class="w-100pre h-100pre"></div>

        <!-- <video src="http://gcalic.v.myalicdn.com/gc/wgw05_1/index.m3u8?contentid=2820180516001" type="application/x-mpegURL" controls>

        </video> -->
        <!-- <img :src="require('@/assets/image/video/noVideo.png')" alt=""> -->
      <!-- </a-card> -->
    </template>
  </left-right-layout>
</template>
  
  <script>
import { getAction, postAction, deleteAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import LeftRightLayout from '@/components/tableLayout/LeftRightLayout.vue'

export default {
  name: 'videoMonitoring',
  //   mixins: [JeecgListMixin],
  components: {
    LeftRightLayout,
  },
  data() {
    return {
      url: {
        list: '/project/projectCamera/getAllCamera',
        childList: '/project/projectPbs/childList',
        getChildListBatch: '/project/projectPbs/getChildListBatch',
        delete: '/project/projectPbs/delete',
        deleteBatch: '/project/projectPbs/deleteBatch',
        moveUp: 'project/projectPbs/moveUp',
        moveDown: 'project/projectPbs/moveDown',
      },
      treeData: [
        {
          name: '摄像头列表',
          id: '',
          children: [],
        },
      ],
    }
  },
  created() {
    this.getList()
  },
  computed: {},
  watch: {},
  mounted(){

  },
  methods: {
    play(url){
      let doc = document.getElementById("mse");
      console.log("documentdocument",doc)
      let player = new window.Player({
          id: 'mse',
          url: url,
          autoplay: true,
          playsinline: true,
          height: doc.clientHeight,
          width: doc.clientWidth,
          plugins: [window.HlsPlayer]
      });
    },
    /**
     * @description 获取摄像头列表
     */
    getList() {
      getAction(this.url.list).then((res) => {
        this.treeData[0].children = res.result || []
        this.$nextTick(()=>{
      this.play(this.treeData[0].children[0].serial)
    })
      })
    },
    /**
     * @description 点击摄像头
     */
    onSelect(selectedKeys, {node,selectedNodes}) {
      if (node.dataRef.id) {
        this.play(selectedNodes[0].data.props.serial)
      } else {
        this.$message.info('请点击摄像头')
      }
    },
  },
}
</script>
  <style scoped lang="less">
@import '~@assets/less/common.less';
.right-panel{
  img{
width: 320px;
height: 320px;
margin-top: 127px;
margin-left: 50%;
transform: translateX(-50%);
  }
}
</style>
  