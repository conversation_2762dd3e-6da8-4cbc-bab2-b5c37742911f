<template>
  <div class="pageMain">
    <!-- 7day -->
    <div class="content-top">
      <div class="item" v-for="(item, index) in sevenDays" :key="item.title" @click="changeDay(index)">
        <div class="date" :class="{ active: currentDay == index }">
          <div class="dot"></div>
          <div class="date">{{ getDay(item.title) }}</div>
          <div class="week">{{ getWeek(item.title) }}</div>
        </div>
        <div class="info" :class="{ bgActive: currentDay == index }">
          <div class="info-ccontent scrollBar">
            <div v-if="!item.alarms || item.alarms.length == 0" class="info-ccontent-img">
              <a-avatar shape="square" :src="require(`@/assets/chuhang.png`)" />
              <div :class="{ nameActive: currentDay == index }">预报天气正常!</div>
              <div class="name" :class="{ nameActive: currentDay == index }">适合出航</div>
            </div>
            <div v-else>
              <div
                class="wind-speed"
                :class="{ windSpeedActive: currentDay == index }"
                v-for="(inItem, inIndex) in item.alarms"
                :key="inIndex"
              >
                <div class="wind">
                  <div class="icon">
                    <a-avatar :size="16" :src="require(`@/assets/${getUnit(inItem.name, 'icon')}`)" />
                  </div>
                  <div class="">{{ getUnit(inItem.name, 'name') }}</div>
                  <div class="value">{{ getRule(inItem.num) }}{{ inItem.num }}{{ getUnit(inItem.name, 'unit') }}</div>
                </div>
                <div class="time">
                  <div class="name">影响时长</div>
                  <div class="value">{{ inItem.hours }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- table -->
    <div class="content-bottom">
      <a-table
        ref="table"
        bordered
        :columns="columns"
        :data-source="tableData"
        :rowClassName="rowClassName"
        :pagination="false"
        :scroll="{ x: true }"
      >
        <span v-for="col in columns" :slot="col.dataIndex" v-bind:key="col.dataIndex" slot-scope="text, record, index">
          <a-popover
            v-if="col.dataIndex === 'name' || col.dataIndex === 'type'"
            :content="getColumnName(text, record, index, col)"
          >
            <div class="w-100pre text-left ml-5">{{ getColumnName(text, record, index, col) }}</div>
          </a-popover>
          <span v-else>
            <a-popover placement="rightTop" trigger="click" v-if="getColumnName(text, record, index, col) < 3">
              <template slot="title"> </template>
              <template slot="content">
                <a-table
                  ref="poptable"
                  :columns="popoverColumns"
                  :data-source="popoverTableData"
                  :rowClassName="popoverRowClassName"
                  :pagination="false"
                  :showHeader="false"
                >
                  <template slot="title">
                    <div style="display: flex; font-size: 12px; font-weight: bold">
                      <div style="flex: 1">指标</div>
                      <div style="flex: 1">预测天气</div>
                      <div style="flex: 1">适宜施工天气</div>
                    </div>
                  </template>
                  <span slot="index" slot-scope="text, record" class="first-column">
                    <a-avatar style="margin-right: 5px" :size="16" :src="require(`@/assets/${record.icon}`)" />
                    <span>{{ text }}</span>
                  </span>
                  <span slot="forecastWeather" slot-scope="text, record" class="first-column">
                    <span :style="alertName(record)">{{ text }}</span>
                  </span>
                </a-table>
              </template>

              <span @click.prevent="initPopover(text, record, index, col)">
                <span v-if="getColumnName(text, record, index, col) == 1">
                  <a-avatar :size="22" :src="require('@/assets/ok.png')"
                /></span>
                <span v-if="getColumnName(text, record, index, col) == 2">
                  <a-avatar :size="22" :src="require('@/assets/alert.png')"
                /></span>
              </span>
            </a-popover>
            <span v-else> <a-avatar :size="22" /></span>
          </span>
        </span>
      </a-table>
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import moment from 'moment'
import { getDictItems } from '@/components/dict/JDictSelectUtil'

const rule = {
  gt: '>',
  gte: '>=',
  eq: '=',
  lt: '<',
  lte: '<=',
}

const wearthType = {
  wind: {
    name: '风速',
    icon: 'win.png',
    unit: 'm/s',
  },
  wave: {
    name: '浪高',
    icon: 'wave.png',
    unit: 'm',
  },
  vis: {
    name: '能见度',
    icon: 'visibility.png',
    unit: 'km',
  },
  water: {
    name: '降雨',
    icon: 'rainfall.png',
    unit: 'mm',
  },
  tem: {
    name: '温度',
    icon: 'temperature.png',
    unit: '℃',
  },
}
export default {
  data() {
    return {
      tsest: true,
      currentDay: 0,
      // 7天数据
      sevenDays: [
        {
          day: '2024-04-15',
          week: '周一',
          windSpeed: 0,
        },
        {
          day: '2024-04-16',
          windSpeed: 0,
          week: '周二',
        },
        {
          day: '2024-04-17',
          windSpeed: 0,
          week: '周三',
        },
        {
          windSpeed: 0,
          day: '2024-04-18',
          week: '周四',
        },
        {
          day: '2024-04-19',
          windSpeed: 0,
          week: '周五',
        },
        {
          day: '2024-04-20',
          windSpeed: 0,
          week: '周六',
        },
        {
          day: '2024-04-21',
          windSpeed: 0,
          week: '周日',
        },
      ],
      tableData: [
        {
          name: '沉桩施工',
          type: '风机安装',
          key: '1',
        },
        {
          name: '沉桩施工',
          type: '风机安装',
          key: '2',
        },
        {
          name: '沉桩施工',
          type: '风机安装',
          key: '3',
        },
        {
          name: '沉桩施工',
          type: '风机安装',
          key: '4',
        },
        {
          name: '沉桩施工',
          type: '风机安装',
          key: '5',
        },
        {
          name: '沉桩施工',
          type: '风机安装',
          key: '6',
        },
      ],
      columns: [
        {
          title: '船舶名称',
          dataIndex: 'name',
          slots: { title: '船舶名称' },
          align: 'center',
          scopedSlots: { customRender: 'name' },
          key: 'name',
          width: 300,
          ellipsis: true,
          fixed: 'left', //需要固定宽度
        },
      ],
      popoverColumns: [
        {
          title: '指标',
          dataIndex: 'index',
          key: 'index',
          slots: { title: 'customTitle' },
          scopedSlots: { customRender: 'index' },
        },
        {
          title: '预测天气',
          dataIndex: 'forecastWeather',
          key: 'forecastWeather',
          slots: { title: 'forecastWeather' },
          scopedSlots: { customRender: 'forecastWeather' },
        },
        {
          title: '适宜施工天气',
          dataIndex: 'wather',
          key: 'wather',
        },
      ],
      popoverTableData: [
        {
          index: '风速(m/s)',
          forecastWeather: 3.0,
          wather: '>0.00;5.00',
          forecasKey: 'windSpeed',
          key: 'windCondition',
          icon: 'win.png',
        },
        {
          index: '浪高(m)',
          forecastWeather: 0.5,
          wather: '>0.00;5.00',
          forecasKey: 'waveHeight',
          key: 'waveCondition',
          icon: 'wave.png',
        },
        {
          index: '能见度(km)',
          forecastWeather: 0.5,
          wather: '>0.00;5.00',
          forecasKey: 'visibility',
          key: 'visibilityCondition',
          icon: 'visibility.png',
        },
        {
          index: '温度(℃)',
          forecastWeather: 0.5,
          wather: '>0.00;5.00',
          forecasKey: 'temperature',
          key: 'temperatureCondition',
          icon: 'temperature.png',
        },
        {
          index: '降雨量(mm)',
          forecastWeather: 0.5,
          wather: '>0.00;5.00',
          forecasKey: 'precipitation',
          key: 'precipitationCondition',
          icon: 'rainfall.png',
        },
      ],
      processType: [],
      sevenDaysTable: [],
      shipTyp: [],
    }
  },

  created() {
    this.initDict()
    this.allData()
  },
  mounted() {
    this.initTable()
  },
  methods: {
    alertName(record) {
      console.log('record.checkrecord.check', record)
      if (record.check) {
        return 'color:#000'
      } else {
        return 'color:#f53f3f'
      }
    },
    initDict() {
      getDictItems('ship_typ_code').then((resp) => {
        this.shipTyp = resp
      })
    },
    // 扁平化树结构
    flatten(data, result = []) {
      for (let item of data) {
        const { children, ...rest } = item
        result.push(rest)
        if (children && children.length > 0) {
          this.flatten(children, result)
        }
      }
      return result
    },
    initPopover(text, record, index, col) {
      if (!text) {
        this.popoverTableData.forEach((item) => {
          item.forecastWeather = '/'
          item.wather = '/'
        })
        return
      }
      this.popoverTableData.forEach((item) => {
        item.forecastWeather = text[item.forecasKey]
        item.check = text[item.key.replace('Condition', 'Check')]
        if (text[item.key]) {
          let json = JSON.parse(text[item.key])
          console.log('json', json)
          if (rule[json[0].symbol]) {
            item.wather = `${rule[json[0].symbol] ? rule[json[0].symbol] : ''}${
              json[0].num || json[0].num == 0 ? json[0].num : ''
            };${rule[json[1].symbol] ? rule[json[1].symbol] : ''}${json[1].num || json[1].num == 0 ? json[1].num : ''}`
          } else {
            item.wather = `/`
            item.check = true
          }
        } else {
          item.check = true
          item.wather = '/'
        }
      })
      this.$set(this, 'popoverTableData', this.popoverTableData)
    },
    getColumnName(text, record, index, col) {
      if (col.dataIndex === 'type') {
        let item = this.shipTyp.find((x) => x.id == record.value)
        if (item) {
          return item.title
        }
        return ''
      }
      if (col.dataIndex === 'name') {
        return record.name
      }
      //无时间值时显示灰色
      if (record[col.dataIndex]) {
        return record[col.dataIndex].weatherCheck ? 1 : 2
      } else {
        return 3
      }
    },
    changeDay(index) {
      this.currentDay = index
      this.setTableDate(this.sevenDaysTable[index])
    },
    getRule(value) {
      let icon = ''
      if (value) {
        icon = rule[value]
      }
      return icon
    },
    getDay(value) {
      return moment(value).format('YYYY-MM-DD')
    },
    getWeek(value) {
      return moment(value).format('dddd')
    },
    getUnit(value, key) {
      let unit = ''
      if (value) {
        unit = wearthType[value][key]
      }
      return unit
    },
    allData() {
      let url = '/sea/weatherInfo/sailingAssess'
      getAction(url).then((res) => {
        const { success, result } = res
        console.log('result', result)
        if (!success) {
          this.$message.error('请求失败')
        }
        // 找出7天数据
        let sevenDays = []
        let sevenDaysTable = []
        result.forEach((element) => {
          sevenDays.push({
            alarms: element.alarms,
            title: element.title,
          })
          sevenDaysTable.push(element.processCheck)
        })
        this.sevenDays = sevenDays
        this.sevenDaysTable = sevenDaysTable
        this.setTableDate(sevenDaysTable[0])
      })
    },
    setTableDate(tableData) {
      console.log('表格数据', tableData)
      this.tableData = tableData
    },
    rowClassName(record, index) {
      // 双数
      if (index % 2 === 0) {
        return 'even-row'
      }
      // 单数
      return 'odd-row'
    },
    popoverRowClassName(record, index) {
      // console.log('record', record, index)
      return 'popover-row'
    },
    initTable() {
      // 循环24次
      let columns = []
      for (let i = 0; i < 24; i++) {
        const hour = i.toString().padStart(2, '0')
        columns.push({
          title: `${hour}`,
          dataIndex: hour, // 使用hour作为dataIndex
          key: hour,
          scopedSlots: { customRender: hour },
        })
      }
      this.columns = this.columns.concat(columns)
      console.log('列', this.columns)
      //6条数据  每条数据 添加这24个时间点
      // this.tableData.forEach((element) => {
      //   // 每一条添加 24个数据
      //   for (let i = 1; i < 25; i++) {
      //     const hour = i.toString().padStart(2, '0')
      //     element[`${hour}:00`] = 1
      //     element[`hour_${i}`] = i
      //   }
      // })
    },
  },
}
</script>
<style lang="less" scoped>
.pageMain {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px 24px;
  background-color: #fff;
  .content-top {
    display: flex;
    padding: 8px 8px;
    margin-bottom: 16px;
    background-color: #e5e8f7;
    .item {
      flex: 1;
      margin-right: 15px; /* 添加右侧间隔 */
      font-size: 12px;
      padding: 10px;
      > .date {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .dot {
          width: 5px;
          height: 5px;
          background-color: #999;
          border-radius: 50%;
          margin-right: 5px;
        }
        .date {
          margin-right: 20px;
          font-weight: bold;
        }
        .week {
          font-weight: bold;
        }
      }
      .bgActive {
        background: linear-gradient(180deg, #5f96ff 0%, #3254ff 100%);
      }
      .info {
        border-radius: 4px;
        height: 203px;
        padding: 15px 8px;
        background-color: #fff;
        overflow: hidden;
        .info-ccontent {
          height: 100%;
          overflow: auto;
          .windSpeedActive {
            background-color: #fff;
          }
          .wind-speed {
            background-color: #f2f3f5;
            width: 100%;
            padding: 10px 5px;
            border-radius: 4px;
            height: fit-content;
            margin-bottom: 10px;
            .wind {
              display: flex;
              padding: 0 10px 5px 0;
              align-items: center;
              font-weight: bold;
              .icon {
                margin-right: 5px;
              }
              .value {
                margin-left: auto;
                color: #f53f3f;
              }
            }
            .time {
              height: 22px;
              background: #fdcdc5;
              border-radius: 2px 2px 2px 2px;
              display: flex;
              padding: 10px;
              align-items: center;
              font-weight: bold;
              justify-content: space-between;
              .name {
                color: #f53f3f;
              }
              .value {
                color: #f53f3f;
              }
            }
          }
        }
        .info-ccontent-img {
          width: 100%;
          height: 100%;
          text-align: center;
          font-weight: bold;
          .nameActive {
            color: #fff !important;
          }
          .name {
            color: #179a16;
          }
          ::v-deep .ant-avatar-image {
            width: calc(100% - 20px);
            height: 120px;
            margin-bottom: 15px;
          }
        }
      }
    }
    .item:last-child {
      margin-right: 0; /* 最后一个 item 不添加右侧间隔 */
    }
  }
  .content-bottom {
    flex: 1;
    background-color: #fff;
    ::v-deep .ant-table-thead > tr > th {
      background-color: #3254ff;
      color: #fff;
    }
    ::v-deep .ant-table-thead th {
      padding: 15px 0px !important;
      text-align: center;
    }

    ::v-deep .ant-table-tbody td {
      padding: 15px 0px !important;
      text-align: center;
    }
    .first-column {
      ::v-deep .ant-table-thead th {
        padding: 15px 0px !important;
        text-align: left;
      }

      ::v-deep .ant-table-tbody td {
        padding: 15px 0px !important;
        text-align: left;
      }
    }
  }
}

::v-deep .popover-row {
  font-size: 12px !important;
  display: flex;
  td {
    flex: 1;
    padding: 5px 0px;
  }
}

::v-deep .ant-table-title {
  padding: 0 0 5px 0 !important;
  width: 300px;
  border-bottom: 1px solid #e5e8f7;
}
::v-deep .odd-row {
  background-color: #eff5ff;
}

.scrollBar::-webkit-scrollbar {
  width: 3px;
  height: 5px !important;
  /**/
}
.scrollBar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}
.scrollBar::-webkit-scrollbar-thumb {
  // background: #999;
  background: transparent;
  border-radius: 10px;
}
.scrollBar::-webkit-scrollbar-thumb:hover {
  background: transparent;
  // background: #666;
}
.scrollBar::-webkit-scrollbar-corner {
  background: #179a16;
}
.active {
  color: #0096ff;
}
</style>
