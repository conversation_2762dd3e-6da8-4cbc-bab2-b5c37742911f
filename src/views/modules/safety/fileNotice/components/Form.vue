<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="通知名称" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <a-input
                autocomplete="off"
                v-decorator="['name', validatorRules.name]"
                placeholder="请输入通知名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="上传人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                disabled
                v-decorator="['uploader', validatorRules.uploader]"
                placeholder="请输入上传人"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="上传时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择上传时间"
                v-decorator="['uploadTime', validatorRules.uploadTime]"
                :trigger-change="true"
                :show-time="true"
                date-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="文件接收人" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <j-multi-select-tag
                type="list_multi"
                v-decorator="['recipient', validatorRules.recipient]"
                :trigger-change="true"
                dictCode="person_info,name,user_id"
                placeholder="请选择文件接收人"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="文件上传" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload v-decorator="['noticeFile', validatorRules.noticeFile]" :trigger-change="true"></j-upload>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <div  v-if="isShow">
          <a-table :columns="columns" :dataSource="safeFileNoticeDetailList">

          </a-table>
          </div>

          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { getStore } from '@/utils/storage.js'
import moment from 'dayjs'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'SafeFileNoticeForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        name: {
          rules: [{ required: true, message: '请输入通知名称!' }],
        },
        uploader: {
          rules: [{ required: true, message: '请输入上传人!' }],
        },
        uploadTime: {
          rules: [{ required: true, message: '请输入上传时间!' }],
        },
        recipient: {
          rules: [{ required: true, message: '请输入文件接收人!' }],
        },
        noticeFile: {
          rules: [{ required: true, message: '请上传文件!' }],
        },
      },
      url: {
        add: '/safety/safeFileNotice/add',
        edit: '/safety/safeFileNotice/edit',
        queryById: '/safety/safeFileNotice/queryById',
      },
      columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '接收人',
            align: 'center',
            dataIndex: 'recipient',
          },
          {
            title: '已读状态',
            align: 'center',
            dataIndex: 'readStatus',
          },
          {
            title: '已读日期',
            align: 'center',
            dataIndex: 'readTime',
          },
        ],
        safeFileNoticeDetailList:[]
    }
  },
  computed: {
    userInfo() {
      return getStore('pro__Login_UserinfoNew')
    },
    isShow(){
      return Boolean(this.model.uploader==this.userInfo.workUserName)
    },
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.form.resetFields()
      this.edit({})
    },
    edit(record) {
      if (this.model.id) {
        this.form.resetFields()
      } else {
        this.form.setFieldsValue({ uploader: this.userInfo.workUserName })
        this.form.setFieldsValue({ uploadTime: moment(String(new Date())).format('YYYY-MM-DD HH:mm:ss'), })
      }
      this.safeFileNoticeDetailList = record.safeFileNoticeDetailList
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'name', 'uploader', 'uploadTime', 'recipient','noticeFile','safeFileNoticeDetailList'))
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'name', 'uploader', 'uploadTime', 'recipient','noticeFile','safeFileNoticeDetailList'))
    },
  },
}
</script>

<style lang="scss" scoped>

</style>