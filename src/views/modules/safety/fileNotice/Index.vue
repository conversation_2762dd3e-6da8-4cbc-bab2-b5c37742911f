<template>
  <div class="list-page safe-file-notice-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { postAction } from '@/api/manage'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { getStore } from '@/utils/storage.js'

export default {
  name: 'SafeFileNoticeList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {
      rightTitle: '文件通知管理列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          name: null,
        },
        formItems: [
          { key: 'name', label: '通知名称', type: 'text' },
          {
            key: 'receivingStatus',
            label: '通知状态',
            classType: 'list',
            dictCode: 'receiving_status',
            placeholder: '请选择人员类型',
          },
        ],
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '通知名称',
            align: 'center',
            dataIndex: 'name',
          },
          {
            title: '上传人',
            align: 'center',
            dataIndex: 'uploader',
          },
          {
            title: '上传时间',
            align: 'center',
            dataIndex: 'uploadTime',
          },
          {
            title: '文件接收人',
            align: 'center',
            dataIndex: 'recipient_dictText',
          },
          {
            title: '状态',
            align: 'center',
            dataIndex: 'receivingStatus_dictText',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
            disabled: (record) => {
              return record.createBy !== this.userInfo.username
            },
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
            disabled: (record) => {
              return record.createBy !== this.userInfo.username
            },
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus',
            handler: this.handleAdd,
          },
        ],
      },

      url: {
        list: '/safety/safeFileNotice/list',
        delete: '/safety/safeFileNotice/delete',
        deleteBatch: '/safety/safeFileNotice/deleteBatch',
        exportXlsUrl: '/safety/safeFileNotice/exportXls',
        importExcelUrl: 'safety/safeFileNotice/importExcel',
        view: '/safety/safeFileNotice/view',
      },
    }
  },
  created() {},
  computed: {
    userInfo() {
      return getStore('pro__Login_UserinfoNew')
    },
  },
  watch: {},
  methods: {
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },

    // modalFormOk() {
    //   const formModel = { ...this.searchData.formModel }
    //   delete formModel.date
    //   this.loadData(formModel)
    // },
    handleDetail(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '详情'
      this.$refs.modalForm.disableSubmit = true
      postAction(this.url.view, record).then((res) => {
        if (res.success) {
          // this.modalFormOk()
        }
      })
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>