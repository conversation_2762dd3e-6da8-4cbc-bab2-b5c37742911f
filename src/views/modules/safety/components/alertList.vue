<template>
  <div class="window">
    <a-popover placement="bottomLeft" trigger="focus" v-model="popoverShow" overlayClassName="alert-popover">
      <template slot="title">
        <div class="window-alert-title">
          <div class="">报警消息</div>
          <div class="" @click="close">×</div>
        </div>
      </template>
      <template slot="content">
        <div class="window-content">
          14566
        </div>
      </template>
    </a-popover>
  </div>
</template>
<script>
import moment from 'moment'
const statusMap = {
  0: '在航(主机推动)',
  1: '锚泊',
  2: '失控',
  3: '操纵受限',
  4: '吃水受限',
  5: '靠泊',
  6: '搁浅',
  7: '捕捞作业',
  8: '靠帆船提供动力',
}
export default {
  name: 'alertShipList',
  props: {
    alertShipList: {
      type: Object,
      default: () => [],
    },
    showTable: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    showTable: {
      handler(newVal) {
        this.popoverShow = newVal
      },
    },
  },
  data() {
    return {
      popoverShow: true,
    }
  },
  methods: {
    getTime(time) {
      return moment(time).format('YYYY-MM-DD HH:mm:ss')
    },
    getStatus(code) {
      let statusName = statusMap[code] || ''
      return statusName
    },
    close() {
      this.$emit('closealertShipList')
    },
  },

  mounted() {},
}
</script>
<style lang="less" scoped>
::v-deep .ant-descriptions-title {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
  margin-bottom: 5px;
}

::v-deep .ant-descriptions-item-label {
  width: 80px;
}
.window-content {
  width: 500px;
  padding: 20px 10px;
}

.window-alert-title {
  background-color: #1890ff;
  color: #fff;
  height: 50px;
  line-height: 50px;
  border-radius: 8px 8px 0 0;
  padding: 0 20px;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  width: 500px;
}
</style>
