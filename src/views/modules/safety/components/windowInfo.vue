<template>
  <div class="window">
    <a-popover placement="bottomLeft" trigger="focus" v-model="popoverShow" overlayClassName="alert-popover">
      <template slot="title">
        <div class="window-alert-title">
          <div class="">{{ windowInfo.name }}</div>
          <div class="" @click="close">×</div>
        </div>
      </template>
      <template slot="content">
        <div class="window-content">
          <a-descriptions title="AIS信息" :column="2">
            <a-descriptions-item label="MMSI">{{ windowInfo.mmsi }}</a-descriptions-item>
            <a-descriptions-item label="航道向">{{ windowInfo.hdg / 100 }}度</a-descriptions-item>
            <a-descriptions-item label="呼号">{{ windowInfo.callsign }}</a-descriptions-item>
            <a-descriptions-item label="航迹向">99.0度</a-descriptions-item>
            <a-descriptions-item label="IMO">{{ windowInfo.imo }}</a-descriptions-item>
            <a-descriptions-item label="航速">{{ windowInfo.sog }}m/s</a-descriptions-item>
            <a-descriptions-item label="类型">其他类型的船舶</a-descriptions-item>
            <a-descriptions-item label="纬度">{{ (windowInfo.lat * Math.pow(10, -6)).toFixed(6) }}</a-descriptions-item>
            <a-descriptions-item label="状态">{{ getStatus(windowInfo.navistat) }}</a-descriptions-item>
            <a-descriptions-item label="经度">{{ (windowInfo.lon * Math.pow(10, -6)).toFixed(6) }}</a-descriptions-item>
            <a-descriptions-item label="船长">{{ windowInfo.length }}米</a-descriptions-item>
            <a-descriptions-item label="目的地">{{ windowInfo.dest_std }}</a-descriptions-item>
            <a-descriptions-item label="船宽">{{ windowInfo.width }}米</a-descriptions-item>
            <a-descriptions-item label="预计时间">{{ windowInfo.eta_std }}</a-descriptions-item>
            <a-descriptions-item label="吃水">{{ windowInfo.draught / 1000 }}米</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ getTime(windowInfo.lasttime) }}</a-descriptions-item>
          </a-descriptions>
        </div>
      </template>
    </a-popover>
  </div>
</template>
<script>
import moment from 'moment'
const statusMap = {
  0: '在航(主机推动)',
  1: '锚泊',
  2: '失控',
  3: '操纵受限',
  4: '吃水受限',
  5: '靠泊',
  6: '搁浅',
  7: '捕捞作业',
  8: '靠帆船提供动力',
}
export default {
  name: 'WindowInfo',
  props: {
    windowInfo: {
      type: Object,
      default: () => {},
    },
    showWindow: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    showWindow: {
      handler(newVal) {
        this.popoverShow = newVal
      },
    },
  },
  data() {
    return {
      popoverShow: false,
    }
  },
  methods: {
    getTime(time) {
      return moment(time).format('YYYY-MM-DD HH:mm:ss')
    },
    getStatus(code) {
      let statusName = statusMap[code] || ''
      return statusName
    },
    close() {
      this.$emit('closeWindowInfo')
    },
  },

  mounted() {},
}
</script>
<style lang="less" scoped>
::v-deep .ant-descriptions-title {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
  margin-bottom: 5px;
}

::v-deep .ant-descriptions-item-label {
  width: 80px;
}
.window-content {
  width: 500px;
  padding: 20px 10px;
}

.window-alert-title {
  background-color: #1890ff;
  color: #fff;
  height: 50px;
  line-height: 50px;
  border-radius: 8px 8px 0 0;
  padding: 0 20px;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  width: 500px;
}
</style>
