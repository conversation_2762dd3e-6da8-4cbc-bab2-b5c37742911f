<template>
  <div class="list-page safe-dangerous-project-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'

export default {
  name: 'SafeDangerousProjectList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal
  },
  data() {
    return {
      rightTitle: '危大工程列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          'projectName': null,
          'constructionLeader': null,
          'startDate': null,
          'currentStage': null,
        },
        formItems: [
          { key: 'projectName', label: '危大工程名称', type:'text', },
          { key: 'constructionLeader', label: '施工负责人', type:'text', },
          { key: 'startDate1', label: '危大开始时间', type:'datetime_range', keyParams: ['startTime', 'endTime'],},
          { key: 'currentStage', label: '当前阶段', type:'list', dictCode:'current_stage', },
        ]
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            }
          },
        {
            title:'危大工程名称',
            align:"center",
            dataIndex: 'projectName'
        },
        {
            title:'施工负责人',
            align:"center",
            dataIndex: 'constructionLeader_dictText'
        },
        {
            title:'安全负责人',
            align:"center",
            dataIndex: 'safetyLeader_dictText'
        },
        {
            title:'危大开始时间',
            align:"center",
            dataIndex: 'startDate',
            customRender: (text) => {
            return !text?"":(text.length>10?text.substr(0,10):text)
        }
        },
        {
            title:'预计完成时间',
            align:"center",
            dataIndex: 'expectedEndDate',
            customRender: (text) => {
            return !text?"":(text.length>10?text.substr(0,10):text)
        }
        },
        {
            title:'施工方案',
            align:"center",
            dataIndex: 'constructionPlanFile',
            scopedSlots: {customRender: 'fileSlot'}
        },
        {
            title:'隐患整改',
            align:"center",
            dataIndex: 'hazardRectification'
        },
        {
            title:'当前阶段',
            align:"center",
            dataIndex: 'currentStage_dictText'
        },
        {
            title:'监护人1',
            align:"center",
            dataIndex: 'guardian_dictText'
        },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons: [
            {
                text: '查看',
                handler: this.handleDetail,
            },
            {
                text: '编辑',
                handler: this.handleEdit,
            },
            {
                text: '删除',
                type: 'danger',
                handler: this.handleDelete,
            },
        ],
        headerButtons: [
            {
                text: '新增',
                icon: 'plus-circle',
                handler: this.handleAdd,
            },
        ]
      },

      url: {
        list: "/safety/safeDangerousProject/list",
        delete: "/safety/safeDangerousProject/delete",
        deleteBatch: "/safety/safeDangerousProject/deleteBatch",
        exportXlsUrl: "/safety/safeDangerousProject/exportXls",
        importExcelUrl: "safety/safeDangerousProject/importExcel",
      }
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>