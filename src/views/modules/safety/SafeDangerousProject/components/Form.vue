<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="危大工程名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input :maxlength="100" autocomplete="off" v-decorator="['projectName', validatorRules.projectName]" placeholder="请输入危大工程名称"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="施工负责人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['constructionLeader', validatorRules.constructionLeader]" :trigger-change="true" dictCode="person_info,name,id" placeholder="请选择施工负责人" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="安全负责人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['safetyLeader', validatorRules.safetyLeader]" :trigger-change="true" dictCode="person_info,name,id" placeholder="请选择安全负责人" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="危大开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择危大开始时间" v-decorator="['startDate', validatorRules.startDate]" :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="预计完成时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择预计完成时间" v-decorator="['expectedEndDate', validatorRules.expectedEndDate]" :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="施工方案" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload v-decorator="['constructionPlanFile', validatorRules.constructionPlanFile]" :trigger-change="true"  ></j-upload>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="隐患整改" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea :maxlength="500" v-decorator="['hazardRectification', validatorRules.hazardRectification]" rows="4" placeholder="请输入隐患整改" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="当前阶段" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['currentStage', validatorRules.currentStage]" :trigger-change="true" dictCode="current_stage" placeholder="请选择当前阶段" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="监护人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['guardian', validatorRules.guardian]" :trigger-change="true" dictCode="person_info,name,id" placeholder="请选择监护人" />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'SafeDangerousProjectForm',
    components: {
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
          projectName: {
            rules: [
              { required: true, message: '请输入危大工程名称!'},
            ]
          },
          constructionLeader: {
            rules: [
              { required: true, message: '请输入施工负责人!'},
            ]
          },
          safetyLeader: {
            rules: [
              { required: true, message: '请输入安全负责人!'},
            ]
          },
          startDate: {
            rules: [
              { required: true, message: '请输入危大开始时间!'},
            ]
          },
          expectedEndDate: {
            rules: [
              { required: true, message: '请输入预计完成时间!'},
            ]
          },
          constructionPlanFile: {
            rules: [
              { required: true, message: '请输入施工方案!'},
            ]
          },
          hazardRectification: {
            rules: [
              { required: true, message: '请输入隐患整改!'},
            ]
          },
          currentStage: {
            rules: [
              { required: true, message: '请输入当前阶段!'},
            ]
          },
          guardian: {
            rules: [
              { required: true, message: '请输入监护人!'},
            ]
          },
        },
        url: {
          add: "/safety/safeDangerousProject/add",
          edit: "/safety/safeDangerousProject/edit",
          queryById: "/safety/safeDangerousProject/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      }
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'projectName','constructionLeader','safetyLeader','startDate','expectedEndDate','constructionPlanFile','hazardRectification','currentStage','guardian'))
        })
      },
      //渲染流程表单数据
      showFlowData(){
        if(this.formBpm === true){
          let params = {id:this.formData.dataId};
          getAction(this.url.queryById,params).then((res)=>{
            if(res.success){
              this.edit (res.result);
            }
          });
        }
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            let formData = Object.assign(this.model, values);
            console.log("表单提交数据",formData)
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'projectName','constructionLeader','safetyLeader','startDate','expectedEndDate','constructionPlanFile','hazardRectification','currentStage','guardian'))
      },
    }
  }
</script>