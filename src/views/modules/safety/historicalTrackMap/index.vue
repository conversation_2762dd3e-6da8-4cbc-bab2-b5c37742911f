<template>
  <div class="historicalTrackMap">
    <div class="title">
      <div class="name">{{ shipName }}({{ mmsi }})</div>
      <a-button type="primary" icon="rollback" @click="back"> 返回 </a-button>
    </div>
    <div class="map">
      <!-- 地图 -->
      <Map :defaultLegend="false" @initMap="initMapData" :defaultShip="false">
        <template slot="top-left">
          <div class="search">
            <a-range-picker
              v-model="alarmDay"
              :allowClear="false"
              format="YYYY-MM-DD HH:mm"
              :disabled-date="disabledDate"
              @calendarChange="calendarPriceRangeChange"
              @change="changePriceRangeDate"
            />
            <a-button type="primary" @click="query"> 查询 </a-button>
            <a-button type="primary"> 导出 </a-button>
          </div>
        </template>
      </Map>
      <!-- 底部 -->
      <div class="packUp">
        <img
          :src="require(`@/assets/image/historicalTrackMap/${fold ? 'fold' : 'unfold'}.png`)"
          @click="fold = !fold"
        />
      </div>
      <div class="bottom" v-show="fold">
        <div class="title">
          <span>航速分析</span>
        </div>
        <div id="chart"></div>
      </div>
      <!-- 弹出层 -->
      <div
        v-show="showPopUpLayer"
        class="popUpLayer"
        :style="{ left: popUpLayerX + 100 + 'px', top: popUpLayerY + 20 + 'px' }"
      >
        <div class="title">
          <span>
            <span class="icon"></span>
            轨迹详情
          </span>
        </div>
        <div>
          <span
            >速度
            <span class="unit"> (节) </span>
          </span>
          <span>{{ popUpLayerData.sog }}</span>
        </div>
        <div>
          <span
            >航迹向
            <span class="unit">(94度)</span>
          </span>
          <span>{{ popUpLayerData.hdg }}</span>
        </div>
        <div>
          <span>时间</span> <span>{{ popUpLayerData.createTime }}</span>
        </div>
        <div>
          <span>经度</span> <span>{{ popUpLayerData.lon }}</span>
        </div>
        <div>
          <span>纬度</span> <span>{{ popUpLayerData.lat }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Map from '@/components/Map'
import MapService from '@/utils/MapService'
import { getAction, postAction, deleteAction } from '@/api/manage'
import moment from 'moment'
import * as echarts from 'echarts'
export default {
  name: 'historicalTrackMap',
  inject: ['closeCurrent'],
  components: { Map },
  data() {
    return {
      map: null,
      alarmDay: [],
      dataSource: [],
      fold: true,
      shipName: '',
      mmsi: '',
      mapDefaultZoom: 14,
      showPopUpLayer: false,
      popUpLayerY: 0,
      popUpLayerX: 0,
      popUpLayerData: {},
      selectPriceDate: '',
    }
  },
  created() {},
  mounted() {
    let { alarmDay, shipName, mmsi } = this.$route.params
    this.alarmDay = [moment(alarmDay + ' 00:00'), moment(alarmDay + ' 23:59')]
    this.mmsi = mmsi
    this.shipName = shipName
    this.initBar()
  },
  methods: {
    /**
     * @description 返回
     */
    back() {
      this.closeCurrent()
      this.$router.go(-1)
    },
    /**
     * @description 加载地图数据
     */
    initMapData(map) {
      this.map = map
      let params = {
        shipName: this.shipName,
        mmsi: this.mmsi,
        startTime: moment(this.alarmDay[0]).format('YYYY-MM-DD HH:mm'),
        endTime: moment(this.alarmDay[1]).format('YYYY-MM-DD HH:mm'),
      }
      getAction('/ship/shipAlarmList/list', params).then((res) => {
        this.dataSource = res.result.records
        this.initBar()
        let linePoints = []
        res.result.records.forEach((x, i) => {
          if (i === 0) this.map.centerAndZoom(new T.LngLat(x.lon, x.lat, 10))
          let position = new T.LngLat(x.lon + i * 0.3, x.lat + i * 0.3)
          linePoints.push(position)
          // 节点图标
          let icon = new T.Icon({
            iconUrl: require(`@/assets/image/historicalTrackMap/${
              i === 0 ? 'startIcon' : i === this.dataSource.length - 1 ? 'endIcon' : 'timeNode'
            }.png`),
            iconSize: new T.Point(10, 10), //图标可视区域的大小。
            // iconAnchor: new T.Point(20, 20), //图标的定位锚点
          })
          // 节点文字
          let label = new T.Label({
            text: x.createTime, //文本标注的内容
            position: position, //文本标注的地理位置
            offset: new T.Point(15, 15), //文本标注的位置偏移值
          })
          // 节点箭头
          let icon1 = new T.Icon({
            iconUrl: require(`@/assets/image/historicalTrackMap/arrow.png`),
            iconSize: new T.Point(40, 40), //图标可视区域的大小。
            iconAnchor: new T.Point(0, 10), //文本标注的位置偏移值
          })

          let marker = new T.Marker(position, {
            icon: icon,
          })
          marker.id = x.id
          let marker1 = new T.Marker(position, {
            icon: icon1,
          })
          marker.addEventListener('mouseover', ({ containerPoint, target }) => {
            this.popUpLayerData = this.dataSource.find((x) => x.id === target.id)
            this.showPopUpLayer = true
            this.popUpLayerX = containerPoint.x
            this.popUpLayerY = containerPoint.y
          })
          marker.addEventListener('mouseout', ({ containerPoint, target }) => {
            this.showPopUpLayer = false
          })
          this.map.addOverLay(marker)
          this.map.addOverLay(label)
          if (i === this.dataSource.length - 1) {
            let line = new T.Polyline(linePoints, { color: '#785DE8' })
            this.map.addOverLay(line)
          }
        })
      })
    },
    /**
     * @description 加载数据
     */
    query() {
      this.initMapData()
    },
    /**
     * @description 初始化表格
     */
    initBar() {
      var chartDom = document.getElementById('chart')
      var chart = echarts.init(chartDom)
      var option

      option = {
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.dataSource.map((x) => x.createTime.slice(5, 16)),
        },
        yAxis: {
          type: 'value',
          name: '航速(节)',
        },
        grid: {
          left: 40,
          right: 40,
          top: 30,
          bottom: 30,
        },
        series: [
          {
            data: this.dataSource.map((x) => x.sog),
            type: 'line',
            smooth: true,
          },
        ],
      }

      option && chart.setOption(option)
      // 监听点击事件
      chart.on('click', (params) => {
        // console.log('🚀 ~ this.chart.on ~ params:', params)
        if (params.componentType === 'series') {
          // 判断点击的组件类型是否为 series，即折线图上的数据点
          console.log('🚀 ~ 节点索引，节点name:', params.dataIndex, params.name)
        }
      })
    },
    //选择完时间 清空限制
    changePriceRangeDate() {
      this.selectPriceDate = ''
    },
    //选择开始时间/结束时间
    calendarPriceRangeChange(date) {
      this.selectPriceDate = date[0]
    },
    disabledDate(current) {
      if (this.selectPriceDate) {
        return (
          current > moment(this.selectPriceDate).add(6, 'days') ||
          current < moment(this.selectPriceDate).subtract(6, 'days')
        )
      } else {
        return false
      }
    },
  },
}
</script>

<style scoped lang="less">
.historicalTrackMap {
  background-color: white;
  padding: 16px 24px;
  height: 100%;
  > .title {
    border-bottom: 1px solid #e5e6eb;
    display: flex;
    justify-content: space-between;
    padding-bottom: 8px;
    .name {
      height: 32px;
      font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
      font-size: 16px;
      color: #1d2129;
      line-height: 32px;
      &::before {
        display: inline-block;
        content: '';
        width: 3px;
        height: 10px;
        background: #3254ff;
        margin-right: 4px;
      }
    }
  }
  .map {
    height: calc(100% - 49px);
    margin-top: 8px;
    position: relative;
    border: 1px solid #e5e6eb;
    #map {
      height: 100%;
      border-radius: 4px;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 5;
    }
    .search {
      // position: absolute;
      // top: 16px;
      // left: 207px;
      // z-index: 500;
      .ant-btn {
        margin-left: 10px;
      }
    }
    .bottom {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 242px;
      z-index: 500;
      .title {
        height: 32px;
        background: #3254ff;
        line-height: 32px;
        padding-left: 10px;
        span {
          font-size: 14px;
          color: #ffffff;
          &::before {
            display: inline-block;
            background-color: #ffffff;
            content: '';
            width: 3px;
            height: 10px;
            margin-right: 4px;
          }
        }
      }
      #chart {
        background-color: white;
        height: calc(100% - 32px);
      }
    }
    .packUp {
      position: absolute;
      right: 16px;
      bottom: 258px;
      width: 36px;
      height: 36px;
      //   background-color: #5587ff;
      //   display: flex;
      //   border-radius: 4px;
      //   flex-direction: column;
      //   justify-content: center;
      //   font-size: 14px;
      //   align-items: center;
      //   color: white;
      z-index: 500;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .popUpLayer {
      width: 242px;
      position: absolute;
      background: #ffffff;
      box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
      z-index: 500;
      left: 200px;
      top: 200px;
      padding: 0 8px;
      > div {
        border-bottom: 1px solid #e5e6eb;
        height: 37px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        > span:first-child {
          font-family: Alibaba PuHuiTi 2, Alibaba PuHuiTi 20;
          font-size: 12px;
          color: #4e5969;
          span {
            font-family: Roboto, Roboto;
            font-weight: 400;
            font-size: 12px;
            color: #86909c;
          }
        }
        > span:last-child {
          font-family: Roboto, Roboto;
          font-weight: 500;
          font-size: 12px;
          color: #1d2129;
        }
        &:first-child {
          .icon {
            width: 3px;
            height: 8px;
            background: #3254ff;
            display: inline-block;
          }
        }
      }
    }
  }
}
</style>
