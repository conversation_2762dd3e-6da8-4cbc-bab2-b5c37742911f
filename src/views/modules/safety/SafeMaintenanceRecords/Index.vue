<template>
  <div class="list-page safe-maintenance-records-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'

export default {
  name: 'SafeMaintenanceRecordsList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal
  },
  data() {
    return {
      rightTitle: 'ai设备维护记录列表',
      // 搜索组件的props
      searchProps: {
        formModel: {},
        formItems: [
          {
            label: '设备编码',
            type: 'input',
            key: 'code',
            placeholder: '请输入设备编码'
          },
          {
            label: '设备名称',
            type: 'input',
            key: 'name',
            placeholder: '请输入设备名称'
          }
        ]
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            }
          },
          {
            title: '设备编码',
            align: 'center',
            dataIndex: 'code'
          },
          {
            title: '设备名称',
            align: 'center',
            dataIndex: 'name'
          },
          {
            title: '维护时间',
            align: 'center',
            dataIndex: 'maintenanceTime',
            customRender: (text) => {
              return !text ? '' : (text.length > 10 ? text.substr(0, 10) : text)
            }
          },
          {
            title: '设备类型字典',
            align: 'center',
            dataIndex: 'type_dictText'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail
          },
          {
            text: '编辑',
            handler: this.handleEdit
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete
          }
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd
          }
        ]
      },

      url: {
        list: '/safety/safeMaintenanceRecords/list',
        delete: '/safety/safeMaintenanceRecords/delete',
        deleteBatch: '/safety/safeMaintenanceRecords/deleteBatch',
        exportXlsUrl: '/safety/safeMaintenanceRecords/exportXls',
        importExcelUrl: 'safety/safeMaintenanceRecords/importExcel'
      }
    }
  },
  created() {
  },
  computed: {},
  watch: {},
  methods: {


    initParams(mergeParams) {
      this.queryParam = mergeParams.params
    },
    onTableChange(params) {
      this.loadData(params)
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>