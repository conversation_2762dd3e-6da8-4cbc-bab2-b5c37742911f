<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="检查编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['code', validatorRules.code]"
                placeholder="请输入检查编号"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查工区" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['workArea', validatorRules.workArea]"
                placeholder="请输入检查工区"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['inspectionType', validatorRules.inspectionType]"
                :trigger-change="true"
                dictCode="inspection_type"
                placeholder="请选择检查类型"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择检查日期"
                v-decorator="['inspectionDate', validatorRules.inspectionDate]"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查结果" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['inspectionResult', validatorRules.inspectionResult]"
                :trigger-change="true"
                dictCode="inspection_result"
                placeholder="请选择检查结果"
                @change="handleSelectedInspectionResult"

              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查人员" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['inspector', validatorRules.inspector]"
                :trigger-change="true"
                dictCode="person_info,name,id"
                placeholder="请选择检查人员"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查上报人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                disabled
                v-decorator="['reporter', validatorRules.reporter]"
                placeholder="请输入检查上报人"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['reportingUnit', validatorRules.reportingUnit]"
                :trigger-change="true"
                dictCode="project_unit,name,id"
                placeholder="请选择检查单位"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="检查内容" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <a-textarea
                autocomplete="off"
                v-decorator="['inspectionContent', validatorRules.inspectionContent]"
                placeholder="请输入检查内容"
              ></a-textarea>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="检查依据" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <a-textarea
                autocomplete="off"
                v-decorator="['inspectionBasis']"
                placeholder="请输入检查依据"
              ></a-textarea>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>

        <!-- 隐患记录子表 -->
        <a-row v-if="selectedInspectionResult==='ywt'">
          <a-col :span="24">
            <div class="detail-container">
              <div class="record-header">
                <span class="detail-title">隐患记录列表</span>
                <a-button type="primary" icon="plus" @click="addHiddenDangerRecord">新增</a-button>
              </div>
              <a-table

                :dataSource="hiddenDangerRecords"
                :columns="hiddenDangerColumns"
                :pagination="false"
                bordered
                size="small"
                :rowKey="(record, index) => index"
              >
                <template slot="dangerLevel" slot-scope="text, record">
                  <j-dict-select-tag
                    :value="record.dangerLevel"
                    type="list"
                    :trigger-change="false"
                    dictCode="danger_level"
                    :disabled="true"
                    style="width: 100%"
                  />
                </template>
                <template slot="rectificationDeadline" slot-scope="text, record">
                  <span v-if="!text">-</span>
                  <div v-else>
                    <div>{{ formatDate(text) }}</div>
                    <a-tag :color="getDeadlineStatusColor(text, record.rectificationMeasure)">
                      {{ getDeadlineStatusText(text, record.rectificationMeasure) }}
                    </a-tag>
                  </div>
                </template>
                <template slot="action" slot-scope="text, record, index">
                  <a
                    v-if="!record.rectificationMeasure || record.rectificationMeasure.trim() === ''"
                    @click="editHiddenDangerRecord(record, index)"
                    style="margin-right: 8px;"
                  >
                    编辑
                  </a>
                  <span
                    v-else
                    style="margin-right: 8px; color: #ccc; cursor: not-allowed;"
                    title="已有整改措施，不允许编辑"
                  >
                    编辑
                  </span>
                  <a-popconfirm
                    v-if="!record.rectificationMeasure || record.rectificationMeasure.trim() === ''"
                    title="确定删除吗?"
                    @confirm="deleteHiddenDangerRecord(index)"
                  >
                    <a style="color: #ff4d4f;">删除</a>
                  </a-popconfirm>
                  <span
                    v-else
                    style="color: #ccc; cursor: not-allowed;"
                    title="已有整改措施，不允许删除"
                  >
                    删除
                  </span>
                </template>
              </a-table>
            </div>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>

      </a-form>
    </j-form-container>

    <!-- 隐患记录表单弹窗 -->
    <hidden-danger-form ref="hiddenDangerForm" @submit="handleHiddenDangerSubmit" />
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { getStore } from '@/utils/storage.js'
import { validateDuplicateValue } from '@/utils/util'
import HiddenDangerForm from './HiddenDangerForm.vue'

export default {
  name: 'SafeInspectionRecordsForm',
  components: {
    HiddenDangerForm
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      selectedInspectionResult: null,
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      // 隐患记录相关
      hiddenDangerRecords: [],
      hiddenDangerColumns: [
        {
          title: '序号',
          width: 60,
          align: 'center',
          customRender: (text, record, index) => index + 1
        },
        {
          title: '隐患名称',
          dataIndex: 'name',
          align: 'center'
        },
        {
          title: '隐患编号',
          dataIndex: 'code',
          align: 'center'
        },
        {
          title: '整改部位',
          dataIndex: 'rectificationPart',
          align: 'center'
        },
        {
          title: '隐患级别',
          dataIndex: 'dangerLevel',
          align: 'center',
          scopedSlots: { customRender: 'dangerLevel' }
        },
        {
          title: '整改期限',
          dataIndex: 'rectificationDeadline',
          align: 'center',
        },
        // {
        //   title: '整改措施',
        //   dataIndex: 'rectificationMeasure',
        //   align: 'center',
        //   width: 150,
        //   customRender: (text) => {
        //     if (!text || text.trim() === '') {
        //       return '-'
        //     }
        //     return text.length > 20 ? text.substring(0, 20) + '...' : text
        //   }
        // },
        {
          title: '状态',
          align: 'center',
          width: 80,
          customRender: (text, record) => {
            if (record.rectificationMeasure && record.rectificationMeasure.trim() !== '') {
              return <a-tag color="green">已整改</a-tag>
            }
            return <a-tag color="orange">待整改</a-tag>
          }
        },
        {
          title: '操作',
          width: 120,
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      validatorRules: {
        code: {
          rules: [{ required: true, message: '请输入检查编号!' }],
        },
        workArea: {
          rules: [{ required: true, message: '请输入检查工区!' }],
        },
        inspectionType: {
          rules: [{ required: true, message: '请输入检查类型!' }],
        },
        inspectionDate: {
          rules: [{ required: true, message: '请输入检查日期!' }],
        },
        inspectionResult: {
          rules: [{ required: true, message: '请输入检查结果!' }],
        },
        inspector: {
          rules: [{ required: true, message: '请输入检查人员!' }],
        },
        reporter: {
          rules: [{ required: true, message: '请输入检查上报人!' }],
        },
        reportingUnit: {
          rules: [{ required: true, message: '请输入检查单位!' }],
        },
        inspectionContent: {
          rules: [{ required: true, message: '请输入检查内容!' }],
        },
      },
      url: {
        add: '/safety/safeInspectionRecords/add',
        edit: '/safety/safeInspectionRecords/edit',
        queryById: '/safety/safeInspectionRecords/queryById',
      },
    }
  },
  computed: {
    userInfo() {
      return getStore('pro__Login_UserinfoNew')
    },
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    handleSelectedInspectionResult(value){
      this.selectedInspectionResult = value
    },
    add() {
      console.log('userInfo', this.userInfo)

      this.edit({})
    },
    edit(record) {
      if (this.model.id) {
        this.form.resetFields()
      } else {
        this.form.setFieldsValue({ reporter: this.userInfo.workUserName })
        this.form.setFieldsValue({ reportingUnit: this.userInfo.workDepartmentId })
      }

      this.model = Object.assign({}, record)

      // 处理隐患记录数据
      if (record.details && Array.isArray(record.details)) {
        this.hiddenDangerRecords = [...record.details]
      } else {
        this.hiddenDangerRecords = []
      }

      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'code',
            'workArea',
            'inspectionType',
            'inspectionDate',
            'inspectionResult',
            'inspector',
            'reporter',
            'reportingUnit',
            'inspectionContent',
            'inspectionBasis'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          // 将隐患记录作为 details 字段添加到提交数据中
          formData.details = this.hiddenDangerRecords
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'code',
          'workArea',
          'inspectionType',
          'inspectionDate',
          'inspectionResult',
          'inspector',
          'reporter',
          'reportingUnit',
          'inspectionContent',
          'inspectionBasis'
        )
      )
    },

    // 格式化日期显示
    formatDate(dateStr) {
      if (!dateStr) return ''
      return dateStr.length > 10 ? dateStr.substr(0, 10) : dateStr
    },

    // 获取期限状态文本
    getDeadlineStatusText(deadline, rectificationMeasure) {
      // 如果已有整改措施，显示已完成
      if (rectificationMeasure && rectificationMeasure.trim() !== '') {
        return '已完成'
      }

      if (!deadline) return '未设置'

      const now = new Date()
      const deadlineDate = new Date(deadline)
      const diffTime = deadlineDate.getTime() - now.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays < 0) {
        return `逾期${Math.abs(diffDays)}天`
      } else if (diffDays === 0) {
        return '今日到期'
      } else if (diffDays <= 3) {
        return `${diffDays}天内到期`
      } else if (diffDays <= 7) {
        return `${diffDays}天后到期`
      } else {
        return '正常'
      }
    },

    // 获取期限状态颜色
    getDeadlineStatusColor(deadline, rectificationMeasure) {
      // 如果已有整改措施，显示绿色
      if (rectificationMeasure && rectificationMeasure.trim() !== '') {
        return 'green'
      }

      if (!deadline) return 'default'

      const now = new Date()
      const deadlineDate = new Date(deadline)
      const diffTime = deadlineDate.getTime() - now.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays < 0) {
        return 'red'        // 逾期 - 红色
      } else if (diffDays === 0) {
        return 'orange'     // 今日到期 - 橙色
      } else if (diffDays <= 3) {
        return 'gold'       // 3天内到期 - 金色
      } else if (diffDays <= 7) {
        return 'blue'       // 7天内到期 - 蓝色
      } else {
        return 'green'      // 正常 - 绿色
      }
    },

    // 新增隐患记录
    addHiddenDangerRecord() {
      this.$refs.hiddenDangerForm.add()
    },

    // 编辑隐患记录
    editHiddenDangerRecord(record, index) {
      // 检查是否有整改措施内容，如果有则不允许编辑
      if (record.rectificationMeasure && record.rectificationMeasure.trim() !== '') {
        this.$message.warning('该记录已有整改措施，不允许编辑')
        return
      }
      this.$refs.hiddenDangerForm.edit(record)
    },

    // 删除隐患记录
    deleteHiddenDangerRecord(index) {
      const record = this.hiddenDangerRecords[index]
      // 检查是否有整改措施内容，如果有则不允许删除
      if (record.rectificationMeasure && record.rectificationMeasure.trim() !== '') {
        this.$message.warning('该记录已有整改措施，不允许删除')
        return
      }
      this.hiddenDangerRecords.splice(index, 1)
      this.$message.success('删除成功')
    },

    // 处理隐患记录表单提交
    handleHiddenDangerSubmit(formData) {
      // 检查是否为编辑模式
      if (formData.id) {
        // 编辑模式：查找并更新现有记录
        const index = this.hiddenDangerRecords.findIndex(record => record.id === formData.id)
        if (index !== -1) {
          this.hiddenDangerRecords.splice(index, 1, formData)
          this.$message.success('编辑成功')
        }
      } else {
        // 新增模式：添加到记录列表
        this.hiddenDangerRecords.push(formData)
        this.$message.success('新增成功')
      }
    },
  },
}
</script>

<style scoped>
/* 隐患记录列表容器 */
.detail-container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
  margin-top: 16px;
}

/* 记录表头样式 */
.record-header {
  margin-bottom: 16px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

/* 标题样式 */
.detail-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}
</style>