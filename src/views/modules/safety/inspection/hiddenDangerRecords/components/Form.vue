<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="隐患名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['name', validatorRules.name]"
                placeholder="请输入隐患名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="隐患编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['code', validatorRules.code]"
                placeholder="请输入隐患名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="整改部位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['rectificationPart', validatorRules.rectificationPart]"
                placeholder="请输入整改部位"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查工区" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['workArea', validatorRules.workArea]"
                placeholder="请输入检查工区"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="隐患来源" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['dangerSource', validatorRules.dangerSource]"
                :trigger-change="true"
                dictCode="danger_source"
                placeholder="请选择隐患来源"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="隐患级别" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['dangerLevel', validatorRules.dangerLevel]"
                :trigger-change="true"
                dictCode="danger_level"
                placeholder="请选择隐患级别"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="整改期限" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择整改期限"
                v-decorator="['rectificationDeadline', validatorRules.rectificationDeadline]"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="整改内容" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <a-textarea
                v-decorator="['rectificationContent', validatorRules.rectificationContent]"
                rows="4"
                placeholder="请输入整改内容"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="整改建议" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <a-textarea
                v-decorator="['rectificationProposal', validatorRules.rectificationProposal]"
                rows="4"
                placeholder="请输入整改建议"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="隐患图片" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload v-decorator="['dangerFiles', validatorRules.dangerFiles]" :trigger-change="true"></j-upload>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="整改措施" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <a-textarea
                v-decorator="['rectificationMeasure', validatorRules.rectificationMeasure]"
                rows="4"
                placeholder="请输入整改措施"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="整改情况" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <a-textarea
                v-decorator="['rectificationSituation', validatorRules.rectificationSituation]"
                rows="4"
                placeholder="请输入整改情况"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="整改完成时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择整改完成时间"
                v-decorator="['rectificationCompleteDate', validatorRules.rectificationCompleteDate]"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="上报人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" disabled v-decorator="['reporter']" placeholder="请输入上报人"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="上报人电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                disabled
                v-decorator="['reporterPhone']"
                placeholder="请输入上报人电话"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="上报日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择上报日期"
                v-decorator="['reportDate']"
                disabled
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="上报人单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                disabled
                v-decorator="['reporterDepart']"
                placeholder="请输入上报人部门"
              ></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="整改图片" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload v-decorator="['reportFiles']" :trigger-change="true"></j-upload>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="隐患整改人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['rectifier', validatorRules.rectifier]"
                :trigger-change="true"
                dictCode="person_info,name,id"
                placeholder="请选择隐患整改人"
              />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { getStore } from '@/utils/storage.js'
import moment from 'dayjs'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'SafeHiddenDangerRecordsForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        name: {
          rules: [{ required: true, message: '请输入隐患名称!' }],
        },
        code: {
          rules: [{ required: true, message: '请输入隐患名称!' }],
        },
        rectificationPart: {
          rules: [{ required: true, message: '请输入整改部位!' }],
        },
        workArea: {
          rules: [{ required: true, message: '请输入检查工区!' }],
        },
        dangerSource: {
          rules: [{ required: true, message: '请输入隐患来源!' }],
        },
        dangerLevel: {
          rules: [{ required: true, message: '请输入隐患级别!' }],
        },
        rectificationDeadline: {
          rules: [{ required: true, message: '请输入整改期限!' }],
        },
        rectificationContent: {
          rules: [{ required: true, message: '请输入整改内容!' }],
        },
        rectificationProposal: {
          rules: [{ required: true, message: '请输入整改建议!' }],
        },
        dangerFiles: {
          rules: [{ required: true, message: '请输入隐患图片!' }],
        },
        rectificationMeasure: {
          rules: [{ required: true, message: '请输入整改措施!' }],
        },
        rectificationSituation: {
          rules: [{ required: true, message: '请输入整改情况!' }],
        },
        rectificationCompleteDate: {
          rules: [{ required: true, message: '请输入整改完成时间!' }],
        },
        rectifier: {
          rules: [{ required: true, message: '请输入隐患整改人!' }],
        },
      },
      url: {
        add: '/safety/safeHiddenDangerRecords/add',
        edit: '/safety/safeHiddenDangerRecords/edit',
        queryById: '/safety/safeHiddenDangerRecords/queryById',
      },
    }
  },
  computed: {
    userInfo() {
      return getStore('pro__Login_UserinfoNew')
    },
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      if (this.model.id) {
        this.form.resetFields()
      } else {
        this.form.setFieldsValue({ reporter: this.userInfo.workUserName })
        this.form.setFieldsValue({ reporterPhone: this.userInfo.phone })
        this.form.setFieldsValue({ reporterDepart: this.userInfo.workDepartmentName })
        this.form.setFieldsValue({ reportDate: moment(String(new Date())).format('YYYY-MM-DD')})
      }

      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'name',
            'code',
            'rectificationPart',
            'workArea',
            'dangerSource',
            'dangerLevel',
            'rectificationDeadline',
            'rectificationContent',
            'rectificationProposal',
            'dangerFiles',
            'overdueStatus',
            'rectificationMeasure',
            'rectificationSituation',
            'rectificationCompleteDate',
            'rectificationStatus',
            'reporter',
            'reporterName',
            'reporterPhone',
            'reporterDepart',
            'reportDate',
            'reportFiles',
            'rectifier'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok',res.result)
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'name',
          'code',
          'rectificationPart',
          'workArea',
          'dangerSource',
          'dangerLevel',
          'rectificationDeadline',
          'rectificationContent',
          'rectificationProposal',
          'dangerFiles',
          'overdueStatus',
          'rectificationMeasure',
          'rectificationSituation',
          'rectificationCompleteDate',
          'rectificationStatus',
          'reporter',
          'reporterName',
          'reporterPhone',
          'reporterDepart',
          'reportDate',
          'reportFiles',
          'rectifier'
        )
      )
    },
  },
}
</script>