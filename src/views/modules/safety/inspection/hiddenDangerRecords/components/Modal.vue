<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    :maskClosable="false"
    @ok="handleSubmit"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <data-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></data-form>
    <template slot="footer">
      <a-button @click="handleOk"> 保存 </a-button>
      <a-button type="primary" @click="handleSubmit"> 提交 </a-button>
    </template>
  </j-modal>
</template>

<script>
import DataForm from './Form'
export default {
  name: 'SafeHiddenDangerRecordsModal',
  components: {
    DataForm,
  },
  data() {
    return {
      title: '',
      width: 896,
      visible: false,
      disableSubmit: false,
      issubmit: false, //提交
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },

    handleOk() {
      this.issubmit = false
      this.$refs.realForm.submitForm()
    },
    //提交按钮
    handleSubmit() {
      this.issubmit = true
      this.$refs.realForm.submitForm()
    },
    submitCallback(dataid) {
      let id = ''
      if (dataid instanceof Object) {
        id = dataid.id
      } else {
        id = dataid
      }
      if (this.issubmit) {
        if (id) this.$emit('submit', id)
      } else {
        this.$emit('ok')
      }
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
