<template>
  <div class="list-page safe-hidden-danger-records-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk" @submit="modalFormSubmit"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { BpmNodeInfoMixin } from '@/views/modules/bpm/mixins/BpmNodeInfoMixin'
import { postAction } from '@/api/manage'

export default {
  name: 'SafeHiddenDangerRecordsList',
  mixins: [JeecgTreeListMixin, BpmNodeInfoMixin],
  components: {
    Modal,
  },
  data() {
    return {
      rightTitle: '隐患整改列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          workArea: null,
          dangerLevel: null,
          reportDate: null,
          rectificationStatus: null,
          overdueStatus: null,
        },
        formItems: [
          { key: 'workArea', label: '施工工区', type: 'text' },
          { key: 'dangerLevel', label: '隐患级别', type: 'list', dictCode: 'danger_level' },
          {
            key: 'datetime',
            label: '检查日期',
            type: 'datetime_range',
            keyParams: ['startDate', 'endDate'],
            format: 'YYYY-MM-DD',
          },
          { key: 'rectificationStatus', label: '整改状态', type: 'list', dictCode: 'rectification_status' },
          { key: 'overdueStatus', label: '逾期状态', type: 'list', dictCode: 'overdue_status' },
        ],
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '隐患名称',
            align: 'center',
            dataIndex: 'name',
          },
          {
            title: '施工工区',
            align: 'center',
            dataIndex: 'workArea',
          },
          {
            title: '隐患来源',
            align: 'center',
            dataIndex: 'dangerSource_dictText',
          },
          {
            title: '隐患级别',
            align: 'center',
            dataIndex: 'dangerLevel_dictText',
          },
          {
            title: '整改期限',
            align: 'center',
            dataIndex: 'rectificationDeadline',
            customRender: (text) => {
              return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
            },
          },
          {
            title: '逾期状态',
            align: 'center',
            dataIndex: 'overdueStatus_dictText',
          },
          {
            title: '整改状态',
            align: 'center',
            dataIndex: 'rectificationStatus_dictText',
          },
          {
            title: '上报人',
            align: 'center',
            dataIndex: 'reporter',
          },
          {
            title: '上报日期',
            align: 'center',
            dataIndex: 'reportDate',
            customRender: (text) => {
              return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
            },
          },
          {
            title: '隐患整改人',
            align: 'center',
            dataIndex: 'rectifier_dictText',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
            disabled: (record) => {
              return record.bpmStatus == '2' || record.bpmStatus == '3'
            },
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
            disabled: (record) => {
              return record.bpmStatus == '2' || record.bpmStatus == '3'
            },
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus',
            handler: this.handleAdd,
          },
        ],
      },
      flowCode: 'dev_safe_hidden_danger_records_001',

      url: {
        list: '/safety/safeHiddenDangerRecords/list',
        delete: '/safety/safeHiddenDangerRecords/delete',
        deleteBatch: '/safety/safeHiddenDangerRecords/deleteBatch',
        exportXlsUrl: '/safety/safeHiddenDangerRecords/exportXls',
        importExcelUrl: 'safety/safeHiddenDangerRecords/importExcel',
        startProcess: '/act/process/extActProcess/startMutilProcess',
      },
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
    modalFormSubmit(data) {
      this.startProcess({ id: data })
    },
    startProcess: function (record) {
      var that = this
      var key = 'startProcess'
      this.$message.loading({ content: '正在提交流程...', key })
      var param = {
        flowCode: that.flowCode,
        id: record.id,
        formUrl: 'modules/safety/inspection/hiddenDangerRecords/components/Form',
        formUrlMobile: '',
      }
      postAction(that.url.startProcess, param).then((res) => {
        if (res.success) {
          that.$message.success({ content: res.message + '!', key, duration: 1 })
          that.modalFormOk()
        } else {
          that.$message.warning({ content: res.message, key, duration: 1 })
        }
      })
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
