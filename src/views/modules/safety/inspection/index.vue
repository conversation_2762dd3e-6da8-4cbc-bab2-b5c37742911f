<template>
  <div class="check-page">
    <a-tabs type="card" default-active-key="1" @change="callback">
      <a-tab-pane key="1" tab="安全检查">
        <inspections />
      </a-tab-pane>
      <a-tab-pane key="2" tab="隐患管理">
        <hiddenDangerRecords />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>
import inspections from './inspectionRecords/index.vue'
import hiddenDangerRecords from './hiddenDangerRecords/index.vue'
export default {
  components: { inspections, hiddenDangerRecords },
  data() {
    return {}
  },
  methods: {
    callback(key) {
      console.log(key)
    },
  },
}
</script>

<style lang="scss" scoped>
.check-page {
  height: 100%;
  background-color: #fff;
  ::v-deep .ant-tabs {
    height: 100%;
    .ant-tabs-content {
      height: calc(100% - 40px - 40px);
      .ant-tabs-tabpane-active {
        height: 100%;
      }
    }
  }
}
</style>
