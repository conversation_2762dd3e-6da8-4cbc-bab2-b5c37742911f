<template>
  <div class="pageMain">
    <Map @clickMap="clickMap" ref="Map" @initMap="initMap">
      <!-- 搜索栏 -->
      <template slot="top-left">
        <div class="ship-search">
          <a-input-search
            class="input-search"
            placeholder="请输入船舶名称或MMSI码"
            :allowClear="true"
            enter-button
            @search="onSearch"
          />
        </div>
      </template>
      <template slot="right-top">
        <div class="tip-ship-box">
          <div class="tab">
            <div :class="{ active: shipType == 'alarmShip' }" @click="shipType = 'alarmShip'">告警船舶</div>
            <div :class="{ active: shipType == 'inShip' }" @click="shipType = 'inShip'">在场施工船舶</div>
          </div>
          <div class="ship-box-content">
            <div
              class="shipItem"
              v-for="item in shipType == 'inShip' ? shipData.inShip : shipData.alarmShip"
              :key="item.mmsi"
              @click="locatingShip(item)"
            >
              <div>
                <img :src="require('@/assets/map/img/shipIcon.png')" />
                <span>{{ item.name }}</span>
              </div>
              <div class="date">{{ moment(item.lasttime).format('MM-DD HH:mm:ss') }}</div>
            </div>
            <div class="noData">
              <img
                v-if="
                  (shipType == 'inShip' && shipData.inShip && shipData.inShip.length < 1) ||
                  (shipType == 'alarmShip' && shipData.alarmShip && shipData.alarmShip.length < 1)
                "
                :src="
                  require(`@/assets/image/shipPosition/${
                    shipType == 'inShip' ? 'noInternalShip' : 'noExternalShip'
                  }.png`)
                "
              />
            </div>
          </div>
        </div>
      </template>
    </Map>
  </div>
</template>
<script>
import Map from '@/components/Map'
import { getAction } from '@/api/manage'
import shipWindow from '@views/modules/safety/components/windowInfo.vue'
import alertList from '@views/modules/safety/components/alertList.vue'
import moment from 'moment'
export default {
  components: { shipWindow, alertList, Map },
  data() {
    return {
      map: '',
      mapDefaultZoom: 14,
      mapTool: ['alert', 'menu', 'position', 'big', 'xiao'],
      type: '1',
      // 出海情况
      shipStatus: {
        ship: '5',
        out: '5',
        people: '3',
      },
      alertShipList: [],
      shipAlert: '',
      mousemoveLnglat: {
        lng: '0',
        lat: '0',
      },
      shipType: 'alarmShip',
      shipWindow: false,
      areashipList: [],
      windowInfo: {},
      alertTable: [],
      showTable: false,
      shipData: {},
    }
  },
  mounted() {},
  methods: {
    moment,
    clickMap(event, type) {
      console.log('🚀 ~ clickMap ~ event,type:', event.type, type)
    },
    /**
     * @description 地图初始化完成
     */
    initMap(map, shipData) {
      this.shipData = shipData
    },
    /**
     * @description 定位船舶
     */
    locatingShip(ship) {
      this.$refs.Map.setCenter(ship.lon * Math.pow(10, -6), ship.lat * Math.pow(10, -6))
    },
    /**
     * @description 设计施工 实际施工
     */
    radioChange(e) {
      this.type = e.target.value
      this.$refs.Map.changeType(this.type)
    },
    // 开始加载地图数据
    async initMapData() {
      this.getAlertShipList()
    },
    // 设置船舶
    setShipMarks(markers) {
      markers.forEach((marker) => {
        let lng = marker.lon * Math.pow(10, -6)
        let lat = marker.lat * Math.pow(10, -6)
        // console.log('经纬度', lng, lat)
        const markerLayer = new T.Marker(new T.LngLat(lng, lat), {
          icon: new T.Icon({
            iconUrl: require('@/assets/map/img/shipAlert.png'),
            iconSize: new T.Point(42, 32), // 图标大小
            // iconAnchor: new T.Point(10, 32), // 图标锚点，通常是图标中心
          }),
        })
        markerLayer.addEventListener('click', () => {
          this.onSearch(marker.name)
        })
        // 创建标记的文本标签
        const label = new T.Label({
          offset: new T.Point(-45, 40), // 偏移量，使标签在标记上方显示
          text: marker.name, // 显示标记的名称
          position: new T.LngLat(lng, lat), // 标签的位置
        })
        // 将文本标签添加到地图上
        this.map.addOverLay(label)
        this.map.addOverLay(markerLayer)
      })
    },

    // ===============地图工具类操作===================
    handleMapToolClick(eventName) {
      if (!eventName) {
        return
      }
      this[eventName]()
    },
    alert() {
      console.log('alert')
      // 初始化轮播
      setTimeout(() => {
        // this.startAutoScroll('scrollList', 'shipAlert')
      })
    },
    menu() {
      console.log('menu')
    },
    position() {
      const { mapCenter } = mapData
      this.map.panTo(new T.LngLat(mapCenter[0], mapCenter[1]), this.mapDefaultZoom)
    },
    big() {
      this.map.zoomIn()
      console.log('big')
    },
    xiao() {
      this.map.zoomOut()
      console.log('xiao')
    },
    // ===============end===================
    // 查询单个船舶
    onSearch(value) {
      console.log('查询船舶', value, this.areashipList)
      if (!value) {
        this.$message.warning('请输入船舶名称或MMSI码')
        return
      }
      // 实际上就是从区域船数据中过滤   只过滤出条数据
      let ship = this.areashipList.find((x) => x.name.includes(value) || x.mmsi.toString().includes(value))
      if (!ship) {
        this.$message.warning('当前无此船舶数据')
        return
      }
      const { lat, lon } = ship
      console.log('单船', ship)
      // 定位到船舶
      this.map.panTo(new T.LngLat((lon * Math.pow(10, -6)).toFixed(6), lat * Math.pow(10, -6)), this.mapDefaultZoom)
      // 让弹窗展示
      // 413267760
      this.windowInfo = ship
      this.shipWindow = true
    },
    closeWindowInfo() {
      this.shipWindow = false
    },

    // 查询预警船舶列表
    getAlertShipList() {
      let url = '/ship/shipAlarm/list'

      let params = {
        alarmDay: moment(new Date()).format('YYYY-MM-DD'),
      }
      getAction(url, params).then((res) => {
        const { success, result } = res
        if (!success) {
          this.$message.error('获取预警船舶数据失败')
          return
        }
        if (result.records && Array.isArray(result.records)) {
          this.alertShipList = result.records
        }
      })
    },
    // 列表滚动
    startAutoScroll(ref, timerName) {
      const scrollList = document.getElementById('scrollList')
      console.log('scrollList', scrollList)
      // 获取父元素的高度
      const parentHeight = scrollList.parentElement.offsetHeight
      console.log('父元素的高度', parentHeight)
      // 获取 li 的高度
      let liHeight = scrollList.querySelector('.td').offsetHeight
      console.log('父元素的高度', liHeight)
      this[timerName] = setInterval(() => {
        // 获取当前滚动条位置
        let scrollTop = scrollList.scrollTop
        console.log('获取当前滚动条位置', scrollTop)
        const isAtBottom = scrollTop >= parentHeight + liHeight
        // 如果已经滚动到最底部，则回到顶部重新开始滚动
        if (isAtBottom) {
          scrollTop = 0
        } else {
          scrollTop += liHeight
        }
        scrollList.scrollTo({
          top: scrollTop,
          behavior: 'smooth',
        })
      }, 2000)
    },
    // 预警记录查询
    getShipAlertById() {
      console.log('查询记录')
    },
  },
  destroyed() {
    clearInterval(this.shipAlert)
  },
}
</script>

<style lang="less" scoped>
.pageMain {
  --index: 500;
  --left: 54px;
  --left1: 24px;
  --top: 50px;
  --top16: 16px;
  --right: 54px;
  --bottom: 16px;
  width: 100%;
  height: 100%;
  position: relative;
  #map {
    width: 100%;
    /* 先这样定义页面高度 后面需要动态计算 */
    // height: 100%; //有个bug  第一次进来地图会展示不全  但是再次进来就好了
    height: calc(88vh - 5px);
  }

  .ship-info-window {
    position: absolute;
    top: var(--top);
    left: var(--left);
    z-index: var(--index);
    width: 500px;
    height: 48px;
  }
  .alert-info-window {
    position: absolute;
    top: 80px;
    right: var(--right);
    z-index: var(--index);
    width: 500px;
    height: 48px;
  }
  .map-tool {
    position: absolute;
    bottom: 128px;
    right: var(--top);
    z-index: var(--index);
    .tool-item {
      width: 36px;
      height: 36px;
      border-radius: 4px;
      margin-bottom: 10px;
      cursor: pointer;
    }
    .alert {
      background: url('../../../../assets/map/img/1.png') no-repeat;
      background-size: 100%;
    }
    .alert:active {
      background: url('../../../../assets/map/img/1-active.png') no-repeat;
      background-size: 100%;
    }
    .menu {
      background: url('../../../../assets/map/img/2.png') no-repeat;
      background-size: 100%;
    }
    .menu:active {
      background: url('../../../../assets/map/img/2-active.png') no-repeat;
      background-size: 100%;
    }
    .position {
      background: url('../../../../assets/map/img/3.png') no-repeat;
      background-size: 100%;
    }
    .position:active {
      background: url('../../../../assets/map/img/3-active.png') no-repeat;
      background-size: 100%;
    }
    .big {
      background: url('../../../../assets/map/img/4.png') no-repeat;
      background-size: 100%;
    }
    .big:active {
      background: url('../../../../assets/map/img/4-active.png') no-repeat;
      background-size: 100%;
    }
    .xiao {
      background: url('../../../../assets/map/img/5.png') no-repeat;
      background-size: 100%;
    }
    .xiao:active {
      background: url('../../../../assets/map/img/5-active.png') no-repeat;
      background-size: 100%;
    }
  }
  .ship-search {
    .input-search {
      width: 260px;
      height: 36px;
      ::v-deep .ant-btn-primary {
        background-color: #3254ff;
      }
    }
  }
  .ship-status {
    width: 500px;
    height: 48px;
    position: absolute;
    bottom: var(--bottom);
    left: var(--left);
    z-index: var(--index);
    background: #f2f3f5;
    box-shadow: 0px 2px 4px 0px rgba(195, 195, 195, 0.25);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e5e6eb;
    background: #f2f3f5;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #e5e6eb;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding-left: 20px;
    padding-right: 20px;
    .line {
      width: 1px;
      height: 12px;
      background-color: #d3d4d4;
    }
    .item {
      display: flex;
      .name {
        margin-right: 5px;
      }
      .value {
        font-weight: bold;
        color: #3254ff;
      }
    }
  }

  .tip-ship-box {
    height: 100%;
    margin-bottom: 20px;
    padding: 18px 16px;
    background: #ffffff;
    box-shadow: 0px 3px 8px 0px rgba(31, 49, 141, 0.15);
    border-radius: 6px;
    .tab {
      display: flex;
      margin-bottom: 7px;
      justify-content: space-between;
      div {
        width: calc(50% - 8px);
        height: 30px;
        background: #ffffff;
        text-align: center;
        color: #3254ff;
        line-height: 30px;
        font-size: 14px;
        border-radius: 2px;
        border: 1px solid #3254ff;
        cursor: pointer;
        &.active {
          background: #3254ff;
          color: white;
        }
      }
    }
    .ship-box-content {
      height: calc(100% - 37px);
      overflow: auto;
      .shipItem {
        height: 39px;
        border-bottom: 1px solid #c9cdd4;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        img {
          width: 20px;
          height: 20px;
          margin-right: 12px;
        }
        span {
          font-size: 14px;
          color: #3254ff;
        }
        .date {
          font-size: 12px;
          color: #86909c;
        }
      }
      .noData {
        width: 100%;
        height: 100%;
        text-align: center;
      }
    }
  }
}

.map-tool-alert-title {
  background-color: #1890ff;
  color: #fff;
  height: 50px;
  line-height: 50px;
  border-radius: 8px 8px 0 0;
  padding: 0 20px;
  font-size: 16px;
  font-weight: bold;
}
.content {
  height: 203px;
  width: 500px;
  .th {
    display: flex;
    background-color: rgb(223, 235, 254);
    height: 40px;
    line-height: 40px;
    > div {
      width: 25%;
      text-align: center;
    }
  }
  .scroll-container {
    height: calc(100% - 60px); /* 设置容器高度，超出部分会出现滚动条 */
    overflow: auto; /* 显示滚动条 */
    .scrollBar {
      .td {
        display: flex;
        > div {
          width: 25%;
          text-align: center;
          border: 1px solid #f2f7ff;
          height: 30px;
          line-height: 30px;
          padding: 0 10px;
        }
      }
    }
  }
}

.scrollBar::-webkit-scrollbar {
  width: 8px;
  height: 5px !important;
  /**/
}
.scrollBar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}
.scrollBar::-webkit-scrollbar-thumb {
  background: #399df0;
  border-radius: 10px;
}
.scrollBar::-webkit-scrollbar-thumb:hover {
  background: #399df0;
}
.scrollBar::-webkit-scrollbar-corner {
  background: #179a16;
}
</style>
