<template>
  <div class="list-page safe-security-alert-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="handleSearch"
      @table-change="onTableChange"
    >
      <template #table="{ record }">
        <div class="row-container" style="margin: 0 0 16px 0;">
          <div class="row-container">
            <span class="text">总体事件:</span>
            <span class="total-count">{{ totalRisk }}</span>
            <span class="text">件</span>
            <span class="text" style="margin-left: 20px">高危事件:</span>
            <span class="high-risk-count">{{ highRisk }}</span>
            <span>件</span>
          </div>
          <div>
            <a-radio-group v-model="viewType" button-style="solid">
              <a-radio-button value="table">
                <a-icon type="table" />
                表格视图
              </a-radio-button>
              <a-radio-button value="media">
                <a-icon type="video-camera" />
                媒体视图
              </a-radio-button>
            </a-radio-group>
          </div>
        </div>
        <div v-if="viewType === 'media'" class="media-row">
          <a-row :gutter="[16, 10]">
            <a-col :span="6" v-for="(media, index) in mediaList" :key="media.id">
              <Media
                :fileType="media.fileType"
                :file="media.mediaUrl"
                :createTime="media.createTime"
                :name="media.eventType_dictText"
                :lvl="media.riskLevel_dictText"
                :deviceInfo="media.deviceInfo_dictText"
                @click-media="
                (file, fileType) => {
                  handlePreview(file, fileType, index)
                }
              "
              >
              </Media>
            </a-col>
          </a-row>
        </div>
        <div class="table-container" v-else>
          <a-table
            style="flex-grow: 1;"
            :dataSource="mediaList"
            :columns="tableProps.columns"
            :pagination="false"
            @rowClick="onRowClick"
            :loading="tableProps.loading"
            rowKey="id"
            size="middle"
            bordered
          >
            <!--            <template slot="media" slot-scope="text, record">-->
            <!--              <div class="media-thumbnail" @click="handlePreview(record.media, record.fileType, record.id)">-->
            <!--                <img v-if="record.fileType === 0" :src="record.media" alt="图片" />-->
            <!--                <video v-else-if="record.fileType === 1" :options="{ sources: [{ src: record.media }] }" />-->
            <!--                <a-icon v-else type="file" style="font-size: 32px;" />-->
            <!--              </div>-->
            <!--            </template>-->

            <template slot="action" slot-scope="text, record">
              <a @click="handleDetail(record)">查看</a>
              <a-divider type="vertical" />
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record)">
                <a style="color: red">删除</a>
              </a-popconfirm>
            </template>
          </a-table>
          <div class="video-container" v-if="selectedRow">
            <img
              v-if="selectedRow && selectedRow.fileType===0"
              :src="selectedRow.mediaUrl"
              alt="Media content"
              class="media-image"
            />
            <video
              ref="video"
              v-else
              :src="selectedRow.mediaUrl"
              controls
              class="media-video"
              autoplay
            />
            <div class="media-content">
              <div class="row-container">
                <span>发生时间:</span>
                <span>{{ selectedRow.eventTime }}</span>
              </div>
              <div class="row-container">
                <span>描述:</span>
                <span>{{ selectedRow.deviceInfo_dictText }}</span>
              </div>
              <div class="row-container">
                <span>等级:</span>
                <span>{{ selectedRow.riskLevel_dictText }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="pagination">
          <a-pagination
            v-model="pageNo"
            :page-size-options="pageSizeOptions"
            :total="total"
            show-size-changer
            show-quick-jumper
            :default-current="pageNo"
            :page-size="pageSize"
            :show-total="(total) => `共 ${total} 个`"
            @showSizeChange="onShowSizeChange"
            @change="onPageChange"
          >
          </a-pagination>
        </div>
      </template>
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
    <a-modal
      :visible="preview.visable"
      :destroyOnClose="true"
      width="60%"
      @cancel="handleCancel"
      :footer="false"
      :keyboard="false"
      :maskClosable="false"
    >
      <div class="w-100pre h-500 customModal"></div>
      <template slot="title">
        <div class="flex align-center">
          <div class="line1 mr-8"></div>
          <div class="home" style="color: #444; font-weight: bolder">预览</div>
        </div>
      </template>
    </a-modal>

  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import Media from './components/Media.vue'
import { deleteAction, getAction, getFileAccessHttpUrl } from '@api/manage'

export default {
  name: 'SafeSecurityAlertList',
  mixins: [JeecgTreeListMixin],
  components: {
    Media,
    Modal
  },
  data() {
    return {
      preview: {
        visable: false,
        previewImageUrl: '',
        fileType: 0 //0图片,1视频
      },
      totalRisk: 0,
      highRisk: 0,
      selectedRow: {},
      pageSizeOptions: ['10', '20', '50', '100'],
      total: 0,
      pageSize: 20,
      pageNo: 1,
      totalPage: 0,
      selectedItems: [],
      disableMixinCreated: false,
      currentCode: null,
      queryParam: {},
      isorter: {},
      viewType: 'table',//是否有默认列表显示
      mediaList: [],
      rightTitle: '智能安全识别预警列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          'eventType': null,
          'deviceType': null,
          'riskLevel': null,
          'deviceInfo': null,
          'eventTime': null
        },
        formItems: [
          { key: 'eventType', label: '事件类型	', type: 'list', dictCode: 'event_type' },
          { key: 'deviceType', label: '设备类型	', type: 'list', dictCode: 'device_type' },
          { key: 'riskLevel', label: '危险等级	', type: 'list', dictCode: 'risk_level' },
          { key: 'deviceInfo', label: '设备信息	', type: 'list', dictCode: 'device_info' },
          { key: 'eventTime', label: '发生时间	', type: 'date' }
        ]
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        noResetPagination: true,
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            }
          },
          // {
          //   title: '视频/照片	',
          //   align: 'center',
          //   dataIndex: 'media'
          // },
          {
            title: '事件类型	',
            align: 'center',
            dataIndex: 'eventType_dictText'
          },
          {
            title: '设备类型	',
            align: 'center',
            dataIndex: 'deviceType_dictText'
          },
          {
            title: '危险等级	',
            align: 'center',
            dataIndex: 'riskLevel_dictText'
          },
          {
            title: '设备信息	',
            align: 'center',
            dataIndex: 'deviceInfo_dictText'
          },
          {
            title: '发生时间	',
            align: 'center',
            dataIndex: 'eventTime',
            customRender: (text) => {
              return !text ? '' : (text.length > 10 ? text.substr(0, 10) : text)
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail
          },
          {
            text: '编辑',
            handler: this.handleEdit
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete
          }
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd
          }
        ]
      },

      url: {
        list: '/safety/safeSecurityAlert/list',
        delete: '/safety/safeSecurityAlert/delete',
        deleteBatch: '/safety/safeSecurityAlert/deleteBatch',
        exportXlsUrl: '/safety/safeSecurityAlert/exportXls',
        importExcelUrl: 'safety/safeSecurityAlert/importExcel',
        statics: 'safety/safeSecurityAlert/statics'
      }
    }
  },
  created() {
  },
  mounted() {
    this.handleSearch()
    this.statics()
    // 添加键盘事件监听
    document.addEventListener('keydown', this.handleKeyDown)
  },
  beforeDestroy() {
    // 移除键盘事件监听
    document.removeEventListener('keydown', this.handleKeyDown)
  },
  computed: {},
  watch: {},
  methods: {
    statics() {
      getAction(this.url.statics).then((res) => {
        if (res.success) {
          this.totalRisk = res.result.total
          this.highRisk = res.result.high
        }
      })
    },
    handleCancel() {
      this.preview.visable = false
      // 确保关闭图片预览插件
      if (this.$closeImagePreview) {
        this.$closeImagePreview({ way: 'closeBtn' })
      }
    },
    onRowClick(row) {
      this.selectedRow = row
    },
    getSearchParams() {
      let params = {
        folderId: this.currentCode,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      }
      // 合并查询参数和排序参数
      params = Object.assign(params, this.queryParam, this.isorter)
      return params
    },
    handleSearch() {
      let url = this.url.list
      let params = this.getSearchParams()
      getAction(url, params).then((res) => {
        if (res.success) {
          let records = res.result.records
          if (records && Array.isArray(records) && records.length > 0) {
            this.selectedRow = records[0]
          }
          console.log('this.selectedRow', this.selectedRow)
          this.total = res.result.total
          this.totalPage = res.result.pages
          console.log('handleSearch!!', res.result.pages)
          records.forEach((record) => {
            let arr = JSON.parse(record.media)
            if (arr.length > 0) {
              record.mediaUrl = getFileAccessHttpUrl(arr[0].url)
              record.fileType = arr[0].type.indexOf('video') > -1 ? 1 : 0
            }
          })
          console.log('searchResult', records)
          this.mediaList = records
          if (this.mediaList.length > 0) {
            this.selectedRow = this.mediaList[0]
          }
        } else {
          this.$message.warn(res.message)
        }
      })
    },
    onPageChange(page, pageSize) {
      console.log('页面变化:', page, pageSize)
      this.pageNo = page
      if (pageSize) {
        this.pageSize = pageSize
      }
      this.handleSearch()
    },
    onShowSizeChange(current, pageSize) {
      this.pageSize = pageSize
      this.handleSearch()
    },
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
    handlePreview(file, fileType, index) {
      // 只有图片才显示modal预览
      if (fileType === 0) {
        this.preview.visable = true
        this.preview.fileType = fileType
        this.$nextTick(() => {
          let files = this.mediaList.filter((x) => x.fileType == 0)
          let urls = files.map((x) => x.mediaUrl)
          this.$ImgPreview({
            multiple: true,
            nowImgIndex: index,
            imgList: urls,
            targetElement: '.customModal', //这个是需要插入的元素的类名
            onClose: () => {
              // 图片预览关闭时，同时关闭modal
              this.preview.visable = false
            }
          })
          this.$previewRefresh()
        })
      }
      // 视频文件不需要modal预览，直接在页面中播放
    },

    // 处理键盘事件
    handleKeyDown(event) {
      // 只有在预览modal显示时才处理ESC键
      if (event.key === 'Escape' && this.preview.visable) {
        event.preventDefault()
        event.stopPropagation()
        this.handleCancel()
      }
    },

    // 添加缺失的CRUD方法
    handleAdd() {
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.disableSubmit = false
    },

    handleEdit(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.disableSubmit = false
    },

    handleDetail(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '详情'
      this.$refs.modalForm.disableSubmit = true
    },

    handleDelete(record) {
      if (!record.id) {
        this.$message.warning('请选择一条记录！')
        return
      }

      deleteAction(this.url.delete, { id: record.id }).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.handleSearch()
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    modalFormOk() {
      this.handleSearch()
    },
    onTableChange(pagination, filters, sorter) {
      // 处理表格变化事件
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = sorter.order === 'ascend' ? 'asc' : 'desc'
      }
      this.handleSearch()
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';

.media-row {
  flex-grow: 1;
  overflow: hidden;
}

.pagination {
  flex-shrink: 1;
  display: flex;
  justify-content: flex-end;
  margin-top: 10px
}

.table-container {
  display: flex;
  flex-direction: row;
  align-items: start;
  flex-grow: 1;
  overflow: hidden;
  width: 100%;
}

.video-container {
  margin-left: 10px;
  flex-grow: 0;
  overflow: hidden;
  width: 500px;

  .media-image {
    width: 100%;
  }

  .media-video {
    width: 100%;
  }

  .media-content {
    display: flex;
    flex-direction: column;

  }
}

.high-risk-count {
  font-size: 18px;
  font-weight: bold;
  color: #f5222d; /* 红色 */
  margin: 0 4px;
}

.total-count {
  font-size: 18px;
  font-weight: bold;
  color: #722ed1; /* 紫色 */
  margin: 0 4px;
}

.row-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.text {
  font-size: 14px;
}
</style>