<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>

          <a-col :span="12">
            <a-form-item label="事件类型	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['eventType']" :trigger-change="true" dictCode="event_type"
                                 placeholder="请选择事件类型	" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备类型	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['deviceType']" :trigger-change="true" dictCode="device_type"
                                 placeholder="请选择设备类型	" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="危险等级	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['riskLevel']" :trigger-change="true" dictCode="risk_level"
                                 placeholder="请选择危险等级	" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备信息	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['deviceInfo']" :trigger-change="true" dictCode="device_info"
                                 placeholder="请选择设备信息	" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="发生时间	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择发生时间	" v-decorator="['eventTime']" :trigger-change="true"
                      style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="视频/照片	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload v-decorator="['media',validatorRules.media]" :file-type="'all'" :isMultiple="true" :limit="1"
                        size="'mine'"></j-upload>



            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'SafeSecurityAlertForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {},
      url: {
        add: '/safety/safeSecurityAlert/add',
        edit: '/safety/safeSecurityAlert/edit',
        queryById: '/safety/safeSecurityAlert/queryById'
      }
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'media', 'eventType', 'deviceType', 'riskLevel', 'deviceInfo', 'eventTime'))
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }

      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'media', 'eventType', 'deviceType', 'riskLevel', 'deviceInfo', 'eventTime'))
    }
  }
}
</script>