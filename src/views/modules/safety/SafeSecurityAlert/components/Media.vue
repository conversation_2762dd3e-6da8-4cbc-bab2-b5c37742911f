<template>
  <a-card :title="name" class="media-card">
    <template #extra>
      <span>低危</span>
    </template>
    <div class="media-content">
      <img
        v-if="fileType === 0"
        :src="file"
        alt="Media content"
        class="media-image"
        @click="
          (event) => {
            handleClick(event, file, fileType)
          }
        "
      />
      <video
        ref="video"
        v-if="fileType === 1"
        :src="file"
        controls
        class="media-video"
        @click="
          (event) => {
            handleClick(event, file, fileType)
          }
        "
        @play="
          (event) => {
            handlePlay(event)
          }
        "
        @fullscreenchange="
          (event) => {
            handleFullscreenChange(event)
          }
        "
      />
    </div>
      <div class="media-footer">
        <span class="media-time">{{ formattedCreateTime }}</span>
        <span class="media-name">{{ deviceInfo }}</span>
      </div>
  </a-card>
</template>

<script>
import Template1 from '@views/jeecg/JVxeDemo/layout-demo/Template1.vue'

export default {
  name: 'Media',
  components: { Template1 },
  props: {
    fileType: {
      type: Number,
      default: 0
    },
    file: {
      type: String,
      required: true
    },
    checked: {
      type: Boolean,
      default: false
    },
    createTime: {
      type: String,
      required: true
    },
    lvl: {
      type: String,
      required: true
    },
    deviceInfo:{
      type: String,
      required: true
    },
    name: {
      type: String,
      required: true
    }
  },
  computed: {
    formattedCreateTime() {
      const options = { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' }
      return new Date(this.createTime).toLocaleDateString(undefined, options)
    }
  },
  mounted() {
    document.addEventListener('fullscreenchange', this.handleFullscreenChange)
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('msfullscreenchange', this.handleFullscreenChange)
  },
  beforeDestroy() {
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('msfullscreenchange', this.handleFullscreenChange)
  },
  methods: {
    // handleFullscreenChange() {
    //   let video = this.$refs.video
    //   if (
    //     !document.fullscreenElement &&
    //     !document.webkitFullscreenElement &&
    //     !document.mozFullScreenElement &&
    //     !document.msFullscreenElement
    //   ) {
    //     video.pause()
    //   }
    // },
    handleClick(event, file, fileType) {
      console.log('媒体被点击', file, fileType)
      this.$emit('click-media', file, fileType)
      event.preventDefault()
      if (fileType === 1) {
        event.target.play()
        event.target.requestFullscreen()
      }
    },
    downloadFile() {
      const link = document.createElement('a')
      link.href = this.file
      link.download = this.name

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    forceDownload(blob, filename) {
      const a = document.createElement('a')
      a.download = filename
      a.href = window.URL.createObjectURL(blob)
      document.body.appendChild(a)
      a.click()
      a.remove()
      window.URL.revokeObjectURL(a.href)
    },
    handlePlay(event) {
      this.pauseAllVideosExcept(event.target)
    },
    downloadResource(url, filename) {
      fetch(url)
        .then((response) => response.blob())
        .then((blob) => this.forceDownload(blob, filename))
        .catch((e) => console.error('出现错误!', e))
    },
    handleFullscreenChange(event) {
      const video = event.target
      if (document.fullscreenElement === video) {
        video.play()
      } else {
        video.pause()
      }
    },
    pauseAllVideosExcept(currentVideo) {
      const videos = document.querySelectorAll('video')
      videos.forEach((video) => {
        if (video !== currentVideo) {
          video.pause()
        }
      })
    }
  }
}
</script>

<style scoped>
.media-card {
  width: 100%;
  position: relative;
}

.media-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  margin-bottom: 8px;
  border-top: 1px solid #e8e8e8;
}

.media-checkbox {
  background-color: white;
  padding: 2px;
}

.media-content {
  text-align: center;
}

.media-image,
.media-video {
  width: 100%;
  height: 200px;
  display: block; /* 防止图片默认的内联显示方式引起的尺寸问题 */
  object-fit: cover; /* 保持图片的纵横比 */
}

.media-footer {
  padding-top: 10px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.media-name {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.media-time {
  font-size: 12px;
  color: #888;
}
::v-deep .ant-card-body{
  padding: 10px;
}
</style>
