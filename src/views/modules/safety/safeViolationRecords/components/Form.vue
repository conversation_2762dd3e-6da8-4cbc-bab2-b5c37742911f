<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="违规类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['violationType', validatorRules.violationType]"
                :trigger-change="true"
                dictCode="violation_type"
                placeholder="请选择违规类型"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="违规时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择违规时间"
                v-decorator="['violationTime', validatorRules.violationTime]"
                :trigger-change="true"
                :show-time="true"
                date-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="所属单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['unit', validatorRules.unit]"
                :trigger-change="true"
                dictCode="project_unit,name,id"
                placeholder="请选择所属单位"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="违规人员" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['person', validatorRules.person]"
                :trigger-change="true"
                dictCode="person_info,name,id"
                placeholder="请选择人员"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="地点" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea v-decorator="['location', validatorRules.location]" rows="4" placeholder="请输入地点" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="照片、视频" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload v-decorator="['mediaFiles', validatorRules.mediaFiles]" :trigger-change="true"></j-upload>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'SafeViolationRecordsForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        delFlag: {
          rules: [{ required: true, message: '请输入delFlag!' }],
        },
        violationType: {
          rules: [{ required: true, message: '请输入违规类型!' }],
        },
        violationTime: {
          rules: [{ required: true, message: '请输入违规时间!' }],
        },
        unit: {
          rules: [{ required: true, message: '请输入所属单位!' }],
        },
        person: {
          rules: [{ required: true, message: '请输入人员!' }],
        },
        location: {
          rules: [{ required: true, message: '请输入地点!' }],
        },
        mediaFiles: {
          rules: [{ required: true, message: '请输入图片!' }],
        },
      },
      url: {
        add: '/safety/safeViolationRecords/add',
        edit: '/safety/safeViolationRecords/edit',
        queryById: '/safety/safeViolationRecords/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(this.model, 'delFlag', 'violationType', 'violationTime', 'unit', 'person', 'location', 'mediaFiles')
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(row, 'delFlag', 'violationType', 'violationTime', 'unit', 'person', 'location', 'mediaFiles')
      )
    },
  },
}
</script>