<template>
  <div class="list-page safe-violation-records-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'

export default {
  name: 'SafeViolationRecordsList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {
      rightTitle: '违章列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          violationType: null,
          unit: null,
          person: null,
        },
        formItems: [
          { key: 'violationType', label: '违规类型', type: 'list', dictCode: 'violation_type' },
          { key: 'unit', label: '所属单位', type: 'list', dictCode: 'project_unit,name,id' },
          { key: 'person', label: '违规人员', type: 'list', dictCode: 'person_info,name,id' },
        ],
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '违规类型',
            align: 'center',
            dataIndex: 'violationType_dictText',
          },
          {
            title: '违规时间',
            align: 'center',
            dataIndex: 'violationTime',
          },
          {
            title: '所属单位',
            align: 'center',
            dataIndex: 'unit_dictText',
          },
          {
            title: '人员',
            align: 'center',
            dataIndex: 'person_dictText',
          },
          {
            title: '地点',
            align: 'center',
            dataIndex: 'location',
          },
          {
            title: '照片、视频',
            align: 'center',
            dataIndex: 'mediaFiles',
            scopedSlots: { customRender: 'fileSlot' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus',
            handler: this.handleAdd,
          },
        ],
      },

      url: {
        list: '/safety/safeViolationRecords/list',
        delete: '/safety/safeViolationRecords/delete',
        deleteBatch: '/safety/safeViolationRecords/deleteBatch',
        exportXlsUrl: '/safety/safeViolationRecords/exportXls',
        importExcelUrl: 'safety/safeViolationRecords/importExcel',
      },
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>