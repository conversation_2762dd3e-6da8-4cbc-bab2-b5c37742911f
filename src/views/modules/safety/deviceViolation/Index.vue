<template>
  <div class="list-page safe-device-violation-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'

export default {
  name: 'SafeDeviceViolationList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal
  },
  data() {
    return {
      rightTitle: '人员行为安全预警列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          'deviceName': null,
          'user': null,
          'violationTime': null,
        },
        formItems: [
          { key: 'deviceName', label: '设备名称', type:'text', },
          { key: 'user', label: '使用人', type:'list', dictCode:'person_info,name,id', },
          { key: 'datetime', label: '违规时间', type:'datetime_range', keyParams: ['startTime', 'endTime'],},
        ]
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            }
          },
        {
            title:'设备名称',
            align:"center",
            dataIndex: 'deviceName'
        },
        {
            title:'设备编号',
            align:"center",
            dataIndex: 'deviceNumber'
        },
        {
            title:'设备状态',
            align:"center",
            dataIndex: 'deviceStatus_dictText'
        },
        {
            title:'使用人',
            align:"center",
            dataIndex: 'user_dictText'
        },
        {
            title:'违规时间',
            align:"center",
            dataIndex: 'violationTime'
        },
        {
            title:'预警内容',
            align:"center",
            dataIndex: 'warningContent'
        },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons: [
            {
                text: '查看',
                handler: this.handleDetail,
            },
            {
                text: '编辑',
                handler: this.handleEdit,
            },
            {
                text: '删除',
                type: 'danger',
                handler: this.handleDelete,
            },
        ],
        headerButtons: [
            {
                text: '新增',
                icon: 'plus',
                handler: this.handleAdd,
            },
        ]
      },

      url: {
        list: "/safety/safeDeviceViolation/list",
        delete: "/safety/safeDeviceViolation/delete",
        deleteBatch: "/safety/safeDeviceViolation/deleteBatch",
        exportXlsUrl: "/safety/safeDeviceViolation/exportXls",
        importExcelUrl: "safety/safeDeviceViolation/importExcel",
      }
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less'
</style>