<template>
  <j-modal
    :title="title"
    :width="width"
    :maskClosable="false"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
    :keyboard="false"
    destroyOnClose
    class="disableSubmit"
  >
    <data-form
      ref="realForm"
      :pageType="pageType"
      @ok="submitCallback"
      :disabled="disableSubmit"
      :areaList="areaList"
    ></data-form>
  </j-modal>
</template>

<script>
import DataForm from './Form'
export default {
  name: '',
  props: {
    pageType: {
      type: String,
      default: '',
      required: true,
    },
    areaList: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    DataForm,
  },
  data() {
    return {
      title: '',
      width: '65%',
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add(record)
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>

<style lang="scss" scoped>
// .ant-modal-footer 下有 的.jee-hidden 这个属性的 隐藏
::v-deep .ant-modal-footer:has(.jee-hidden) {
  display: none !important;
}
</style>
