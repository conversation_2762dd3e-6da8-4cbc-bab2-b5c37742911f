<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="disabled">
      <a-form :form="form" slot="detail" labelAlign="right">
        <!-- 闸机 -->
        <a-row v-if="pageType == 'gate'">
          <a-col :span="12">
            <a-form-item label="闸机名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                :maxLength="50"
                autocomplete="off"
                v-decorator="['name', validatorRules.name]"
                placeholder="请输入"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="安装区域" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select class="search-item" v-decorator="['areaId', validatorRules.areaId]" placeholder="请选择">
                <a-select-option :value="item.id" v-for="item in areaList" :key="item.id"
                  >{{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="安装日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="请选择"
                v-decorator="['installDate']"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="安装位置" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                :maxLength="50"
                autocomplete="off"
                v-decorator="['installPosition']"
                placeholder="请输入"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea
                :maxLength="200"
                autocomplete="false"
                v-decorator="['remark']"
                :auto-size="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入备注"
              ></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 监控 -->
        <a-row v-else>
          <a-col :span="12">
            <a-form-item label="监控名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                :maxLength="50"
                autocomplete="off"
                v-decorator="['name', validatorRules.name]"
                placeholder="请输入监控名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="安装区域" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select class="search-item" v-decorator="['areaId', validatorRules.areaId]" placeholder="请选择">
                <a-select-option :value="item.id" v-for="item in areaList" :key="item.id"
                  >{{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                :maxLength="50"
                autocomplete="off"
                v-decorator="['equipmentSerial', validatorRules.equipmentSerial]"
                placeholder="请输入设备序列号"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['monitorType', validatorRules.monitorType]"
                :trigger-change="true"
                dictCode="minitorDeviceType"
                placeholder="请选择设备类型"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="安装日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="请选择"
                v-decorator="['installDate']"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="安装位置" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                :maxLength="50"
                v-decorator="['installPosition', validatorRules.installPosition]"
                placeholder="请输入安装位置"
              ></a-input>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-row type="flex" :gutter="8">
              <a-col :span="11">
                <a-form-item label="可视化坐标" :labelCol="{ span: 13 }" :wrapperCol="{ span: 11 }">
                  <a-input autocomplete="off" v-decorator="['modelX']" placeholder="X轴坐标" type="number" @input="inputNumber($event,'modelX')"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item :labelCol="{ span: 0 }" :wrapperCol="{ span: 24 }">
                  <a-input :maxLength="10" autocomplete="off" v-decorator="['modelY']" placeholder="Y轴坐标" type="number" @input="inputNumber($event,'modelY')"></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item :labelCol="{ span: 0 }" :wrapperCol="{ span: 24 }">
                  <a-input :maxLength="10" autocomplete="off" v-decorator="['modelZ']" placeholder="Z轴坐标" type="number" @input="inputNumber($event,'modelZ')"></a-input>
                </a-form-item>
              </a-col>
            </a-row>
          </a-col>

          <a-col :span="12">
            <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea
                :maxLength="200"
                autocomplete="false"
                v-decorator="['remark']"
                :auto-size="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入备注"
              ></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 子表 -->
        <a-row v-if="pageType == 'gate'">
          <a-col :span="24">
            <table-layout style="height: 350px" :table-props="tableProps">
              <template #header="{ record }">
                <a-button :disabled="formDisabled" type="primary" @click="addRow" icon="plus-circle">新 增</a-button>
              </template>
              <template #equipmentSerial="{ record }">
                <a-input
                  :maxLength="50"
                  v-model="record.equipmentSerial"
                  autocomplete="false"
                  placeholder="请输入"
                ></a-input>
              </template>
              <template #gateType="{ record }">
                <a-select :allowClear="true" style="width: 100%" v-model="record.gateType" placeholder="请选择">
                  <a-select-option v-for="item in deviceList" :key="item.value" :value="item.value">
                    {{ item.title }}
                  </a-select-option>
                </a-select>
              </template>
              <template #equipmentUrl="{ record }">
                <a-input
                  :maxLength="500"
                  v-model="record.equipmentUrl"
                  autocomplete="false"
                  placeholder="请输入"
                ></a-input>
              </template>
              <template #opration="{ record }">
                <span class="pointer underline" style="color: red" @click="removeRow(record)">删除</span>
              </template>
            </table-layout>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import TableLayout from '@/components/tableLayout/TableLayout.vue'
// import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'
export default {
  // mixins: [JeecgTreeListMixin],
  name: '',
  components: {
    TableLayout,
  },
  props: {
    // 表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
    //页面类型
    pageType: {
      type: String,
      default: '',
      required: true,
    },
    areaList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      deviceList: [],
      form: this.$form.createForm(this),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 17 },
      },
      confirmLoading: false,
      validatorRules: {
        name: {
          rules: [
            {
              required: true,
              message: '请输入名称',
            },
          ],
        },
        areaId: {
          rules: [
            {
              required: true,
              message: '请选择安装区域',
            },
          ],
        },
        equipmentSerial: {
          rules: [
            {
              required: true,
              message: '请输入设备序列号',
            },
          ],
        },
        monitorType: {
          rules: [
            {
              required: true,
              message: '请选择设备类型',
            },
          ],
        },
        installPosition: {
          rules: [
            {
              required: false,
              message: '请输入安装位置',
            },
          ],
        },
        equipmentUrl: {
          rules: [
            {
              required: true,
              message: '请输入监控编码',
            },
          ],
        },
      },
      url: {
        add: '/device/deviceInfo/add',
        edit: '/device/deviceInfo/edit',
      },

      tableProps: {
        dataSource: [],
        ipagination: false,
        rowKey: 'index',
        columns: [
          {
            title: '设备序列号',
            align: 'center',
            dateIndex: 'equipmentSerial',
            scopedSlots: { customRender: 'equipmentSerial' },
          },
          {
            title: '设备类型',
            dataIndex: 'gateType',
            align: 'center',
            scopedSlots: { customRender: 'gateType' },
          },
          {
            title: '监控编码',
            dataIndex: 'equipmentUrl',
            align: 'center',
            scopedSlots: { customRender: 'equipmentUrl' },
          },
          {
            title: '操作',
            dataIndex: 'opration',
            align: 'center',
            scopedSlots: { customRender: 'opration' },
          },
        ],
        // actionButtons: [
        //   {
        //     text: '删除',
        //     type: 'danger',
        //     disabled: (record) => {
        //       return this.formDisabled
        //     },
        //     handler: this.removeRow,
        //   },
        // ],
      },
      formData: {},
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
  },
  created() {},
  mounted() {
    this.initDictData('gateDeviceType')
  },
  methods: {
    inputNumber(event, key) {
      const value = event.target.value
      if (value.length > 16) {
        this.form.setFieldsValue({ [key]: value.slice(0, 16) })
      }
    },
    // 禁止使用混入中的默认调用
    loadData() {},
    initDictData(dictCode) {
      //优先从缓存中读取字典配置
      if (getDictItemsFromCache(dictCode)) {
        this.deviceList = getDictItemsFromCache(dictCode)
        console.log(' this.deviceList', this.deviceList)
        return
      }
      //根据字典Code, 初始化字典数组
      ajaxGetDictItems(dictCode, null).then((res) => {
        if (res.success) {
          this.deviceList = res.result
          console.log(' this.deviceList', this.deviceList)
        }
      })
    },
    add(oldRecord) {
      console.log('add', oldRecord)
      // this.edit(oldRecord)
      this.form.resetFields()
      console.log('add', this.form.getFieldsValue())
      this.formData = {}
    },
    edit(record) {
      this.formData = JSON.parse(JSON.stringify(record))
      if (this.pageType == 'gate') {
        if (this.formData.deviceGateList && Array.isArray(this.formData.deviceGateList)) {
          this.tableProps.dataSource = this.formData.deviceGateList || []
        } else {
          this.tableProps.dataSource = []
        }
      }
      this.form.resetFields()
      this.form.setFieldsValue(this.formData)
    },

    submitForm() {
      // 触发表单验证
      this.form.validateFields((err, values) => {
        console.log('err', err)
        if (!err) {
          let formData = JSON.parse(JSON.stringify(Object.assign(this.formData, this.form.getFieldsValue())))
          if (this.pageType == 'gate') {
            if (this.tableProps.dataSource.length == 0) {
              this.$message.warning('至少关联一条配套设备数据')
              return
            }
            if (!this.validateRecord()) return
            formData.deviceGateList = this.tableProps.dataSource
          }
          console.log('formData', formData)
          this.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!formData.id) {
            httpurl = this.url.add
            method = 'post'
          } else {
            httpurl = this.url.edit
            method = 'put'
          }
          console.log('提交url', formData.id, httpurl)
          console.log('表单提交数据', formData)
          formData.deviceType = this.pageType
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                this.$message.success(res.message)
                this.$emit('ok')
              } else {
                this.$message.warning(res.message)
              }
            })
            .finally(() => {
              this.confirmLoading = false
            })
        }
      })
    },

    validateRecord() {
      for (let index = 0; index < this.tableProps.dataSource.length; index++) {
        const record = this.tableProps.dataSource[index]
        if (!record.equipmentSerial) {
          this.$message.error('请输入设备序列号')
          return false
        }
        if (!record.gateType) {
          this.$message.error('请选择设备类型')
          return false
        } else {
          //摄像头需要校验字段
          if (record.gateType == 'camera' && !record.equipmentUrl) {
            this.$message.error('请输入监控地址')
            return false
          }
        }
      }
      return true
    },
    addRow() {
      if (!this.validateRecord()) {
        return
      }
      this.tableProps.dataSource.push({
        id: undefined,
        equipmentSerial: '',
        gateType: '',
        gateId: this.formData.id,
      })
    },
    removeRow(record) {
      const index = this.tableProps.dataSource.findIndex((item) => item.index === record.index)
      if (index > -1) {
        this.tableProps.dataSource.splice(index, 1)
      }
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep .ant-table-thead > tr {
  th:nth-of-type(1) > .ant-table-header-column .ant-table-column-title::before {
    content: '*';
    color: red;
    top: 4px;
    position: relative;
  }
  th:nth-of-type(2) > .ant-table-header-column .ant-table-column-title::before {
    content: '*';
    color: red;
    top: 4px;
    position: relative;
  }
  th:nth-of-type(3) > .ant-table-header-column .ant-table-column-title::before {
    content: '*';
    color: red;
    top: 4px;
    position: relative;
  }
}
</style>
