<template>
  <div class="list-page watch-dolphin-log-list h-100pre">
    <table-layout
      ref="tableLayout"
      :search-props="searchProps"
      :table-props="tableProps"
      @search-submit="searchQuery"
      @table-change="handleSearchSubmit"
    >
      <template #rightHeader>
        <div class="flex align-center">
          <div
            v-for="(item, index) in topOparation"
            class="w-72 h-28 line-28 text-center mr-10 pointer"
            :class="index == current ? 'topActive' : 'topDefault'"
            :key="index"
            @click="changeCurrent(index, item)"
          >
            {{ item.name }}
          </div>
        </div>
      </template>
      <template slot="name" slot-scope="{ text, record }">
        <div class="text-0096 pointer" @click="handleDetail(record)">{{ text }}</div>
      </template>

      <template slot="areaId" slot-scope="{ text }">
        <div class="text-left">{{ getInstallArea(text) }}</div>
      </template>
      <template slot="installPosition" slot-scope="{ text }">
        <div class="text-left">{{ text }}</div>
      </template>
      <template slot="baseGateEquipmentList" slot-scope="{ text }">
        <div class="text-left">{{ getEquipmentSerials(text) }}</div>
      </template>
    </table-layout>
    <modal ref="modalForm" @ok="submitOk" :pageType="pageType" :areaList="areaList"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { getDictItems } from '@/components/dict/JDictSelectUtil'
import { putAction, getAction, postAction } from '@/api/manage'
export default {
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {
      rightTitle: '党员信息',
      areaList: [],
      queryParam: {
        deviceType: 'gate',
      },
      pageType: 'gate',
      topOparation: [
        {
          name: '闸机',
          code: 'gate',
        },
        {
          name: '监控',
          code: 'monitor',
        },
      ],
      current: 0,
      isorter: {
        //此处禁用排序
        column: '',
        order: '',
      },

      // 搜索组件的props
      searchProps: {
        formModel: {
          deviceType: 'gate',
          name: '',
          installPosition: null,
        },
        formItems: [
          { key: 'name', label: '闸机名称', placeholder: '请输入' },
          { key: 'installPosition', label: '安装位置', placeholder: '请输入' },
        ],
      },
      // 表格组件的props
      tableProps: {
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['20', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        isort: {
          column: 'watchDate',
          order: 'desc',
        },
        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '闸机名称',
            align: 'center',
            dataIndex: 'name',
            scopedSlots: { customRender: 'name' },
            width: 180,
          },
          {
            title: '安装区域',
            align: 'center',
            dataIndex: 'areaId',
            scopedSlots: { customRender: 'areaId' },
            width: 250,
          },
          {
            title: '安装位置',
            align: 'center',
            dataIndex: 'installPosition',
            scopedSlots: { customRender: 'installPosition' },
          },
          {
            title: '安装日期',
            align: 'center',
            dataIndex: 'installDate',
            width: 160,
          },
          {
            title: '设备序列号',
            align: 'center',
            dataIndex: 'deviceGateList',
            scopedSlots: { customRender: 'baseGateEquipmentList' },
          },
        ],
        actionButtons: [
          {
            text: '编辑',
            handler: this.handleEdit,
            // disabled: (record) => {
            //   return record.username !== this.username
            // },
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],
        bordered: false,
      },
      url: {
        list: '/device/deviceInfo/list',
        delete: '/device/deviceInfo/delete',
      },
      installAreaLis: [],
    }
  },
  async created() {
    // 获取区域areaList
    await this.getareaList()
  },

  watch: {
    pageType: {
      handler(newValue, oldValue) {
        console.log('页面类型变化', newValue)
        if (newValue == 'gate') {
          this.searchProps.formModel.deviceType = 'gate'
          this.queryParam.deviceType = 'gate'
          this.tableProps.columns[1].title = '闸机名称'
          this.searchProps.formItems = [
            { key: 'name', label: '闸机名称', placeholder: '请输入' },
            { key: 'installPosition', label: '安装位置', placeholder: '请输入' },
          ]
          this.tableProps.columns.push({
            title: '设备明细',
            align: 'center',
            dataIndex: 'baseGateEquipmentList',
            scopedSlots: { customRender: 'baseGateEquipmentList' },
          })
        } else {
          this.searchProps.formModel.deviceType = 'monitor'
          this.queryParam.deviceType = 'monitor'
          this.tableProps.columns[1].title = '监控名称'
          this.searchProps.formItems = [
            { key: 'name', label: '监控名称', placeholder: '请输入' },
            { key: 'installPosition', label: '安装位置', placeholder: '请输入' },
          ]
          this.tableProps.columns.splice(5, 1)
        }
        this.handleSearchSubmit()
      },
      deep: true,
      immediate: false,
    },
  },
  methods: {
    // 初始化树
    async getareaList() {
      const resp = await getAction('/base/baseArea/list')
      if (resp.success) {
        const records = resp.result.records
        this.areaList = records || []
        console.log('区域', records)
      }
    },
    getInstallArea(key) {
      return this.areaList.find((x) => x.id == key).name || ''
    },
    getEquipmentSerials(baseGateEquipmentList) {
      console.log('baseGateEquipmentList', baseGateEquipmentList)
      if (baseGateEquipmentList && Array.isArray(baseGateEquipmentList)) {
        return baseGateEquipmentList.map((item) => item.equipmentSerial).toString()
      }
      return ''
    },
    submitOk() {
      console.log('onSearchSubmitonSearchSubmit')
      const formModel = { ...this.searchProps.formModel }
      this.loadData(formModel)
    },

    changeCurrent(index, item) {
      this.current = index
      this.pageType = item.code
      this.tableProps.ipagination.current = 1
    },
    handleSearchSubmit(searchData) {
      console.error('handleSearchSubmit', searchData)
      this.searchData = searchData || {}
      const formModel = { ...this.searchProps.formModel }
      formModel.deviceType = this.pageType
      this.loadData(formModel)
    },

    handleAdd() {
      this.$refs.modalForm.title = this.pageType == 'gate' ? '新增闸机' : '新增监控'
      this.$refs.modalForm.add({})
      this.$refs.modalForm.disableSubmit = false
    },
  },
}
</script>
<style lang="scss" scoped>
.topActive {
  background-color: #274afb;
  color: #fff;
  transition: background-color 0.3s ease, color 0.3s ease;
}
.topDefault {
  background-color: #c7d0ff;
  color: #294cfc;
  transition: background-color 0.3s ease, color 0.3s ease;
}
</style>
