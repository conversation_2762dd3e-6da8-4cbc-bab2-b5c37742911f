<template>
  <div class="pageMain">
    <Map
      @clickMap="clickMap"
      ref="Map"
      @initMap="initMap"
      :defaultFan="false"
      :defaultZoom="10"
      :defaultCable="false"
      :defaultBoosterStation="false"
      :defaultLegend="false"
      :defaultTypeChange="false"
      :defaultShip="false"
    >
      <!-- 搜索栏 -->
      <template slot="top-left"> </template>
      <template slot="right-top"> </template>
    </Map>
    <div class="tip-ship-box w-520">
      <div class="flex align-center justify-between">
        <span class="font-20 font-w-900">避风落实情况</span>
        <span>
          <img
            class="w-12"
            @click="changeVisible"
            style="cursor: pointer; float: right; margin: 4px 12px 0 0"
            :src="require(`@/assets/${visible ? 'icon-closecontent.png' : 'icon-opencontent.png'}`)"
          />
          <span
            @click="changeVisible"
            style="cursor: pointer; float: right; margin: 0 0 0 0; font-size: 14px; color: #3254ff"
            >{{ visible ? '收起' : '展开' }}</span
          >
        </span>
      </div>
      <!-- 内容 -->
      <div v-show="visible">
        <div class="line"></div>
        <div class="ship-box-content font-16">
          <div class="notice position-relative px-22 pb-16 pt-30 flex flex-column">
            <div class="position-absolute notice-title ellipsis px-5">
              {{ notify.name }}
              <!-- <j-ellipsis :value="notify.name" :length="11"/> -->
            </div>
            <div class="mb-16 flex-1 ellipsis">
              {{ notify.content }}
              <!-- <j-ellipsis :value="notify.content" :length="183"/> -->
            </div>
            <div style="color: #f53f3f">
              <span>计划避风时间:</span>
              <span class="ship-box-row-data">{{ notify.planStartDate }} - {{ notify.planEndDate }}</span>
            </div>
          </div>
        </div>
        <!-- reportDepart -->
        <div v-for="(item, index) in reportDepart" class="ship-box-row font-14" :key="index">
          <div class="line"></div>
          <div class="mb-10">{{ item.name }}</div>
          <div v-if="item.ships" class="flex px-10 flex-wrap">
            <div v-for="(ship, index) in item.ships" style="width: 20%" class="mb-10 text-center" :key="index">
              <img
                class="w-42"
                :src="require(`@/assets/${ship.status == 1 ? 'icon-ship-in.png' : 'icon-ship-out.png'}`)"
              />
              <div>{{ ship.shipName }}</div>
            </div>
          </div>
          <div v-else>
            <span>暂未报备</span>
          </div>
        </div>
      </div>
    </div>
    <div class="botton-tip">
      <img class="w-15" :src="require(`@/assets/icon-ship-out.png`)" />
      <span>未进入避风港</span>
      <img class="w-15" :src="require(`@/assets/icon-ship-in.png`)" />
      <span>已进入避风港</span>
    </div>
  </div>
</template>
<script>
import Map from '@/components/Map'
import MapService from '@/utils/MapService'
import { getAction } from '@/api/manage'
import shipWindow from '@views/modules/safety/components/windowInfo.vue'
import JEllipsis from '@/components/jeecg/JEllipsis'
import moment from 'moment'
export default {
  components: { shipWindow, Map, JEllipsis },
  data() {
    return {
      map: '',
      mapDefaultZoom: 8,
      mapTool: ['alert', 'menu', 'position', 'big', 'xiao'],
      type: '1',
      fenceList: [],
      reportDepart: [],
      notify: {},
      visible: true,
      shipWindow: false,
      infoWindow: null,
      areashipList: [],
      windowInfo: {},
      alertTable: [],
      showTable: false,
      shipData: {},
      weather: {},
    }
  },
  mounted() {},
  methods: {
    moment,
    clickMap(event, type) {
      console.log('🚀 ~ clickMap ~ event,type:', event.type, type)
    },
    handleChange(value) {
      console.log('🚀 ~ handleChange:', value)
      // let currFence = this.fenceList.filter(item => item.id == value);
      // this.setFence(currFence.points)
    },
    changeVisible() {
      this.visible = !this.visible
    },
    /**
     * @description 地图初始化完成
     */
    initMap(map, shipData) {
      this.shipData = shipData
      this.map = map
      this.loadNotify()
    },
    /**
     * @description 定位船舶
     */
    locatingShip(ship) {
      this.$refs.Map.setCenter(ship.lon * Math.pow(10, -6), ship.lat * Math.pow(10, -6))
    },
    setFence(points) {
      this.$refs.Map.setFence(points)
    },

    // ===============地图工具类操作===================
    handleMapToolClick(eventName) {
      if (!eventName) {
        return
      }
      this[eventName]()
    },
    alert() {
      console.log('alert')
    },
    menu() {
      console.log('menu')
    },
    position() {
      const { mapCenter } = mapData
      this.map.panTo(new T.LngLat(mapCenter[0], mapCenter[1]), this.mapDefaultZoom)
    },
    big() {
      this.map.zoomIn()
      console.log('big')
    },
    xiao() {
      this.map.zoomOut()
      console.log('xiao')
    },
    closeOrder() {
      this.visible = false
    },
    //避风通知详情
    loadNotify() {
      const that = this
      let url = '/wind/windNotification/monitor'
      getAction(url).then((res) => {
        const { success, result } = res
        if (!success) {
          this.$message.error('获取避风数据失败')
          return
        }
        if (result.notify) {
          console.log('避风通知', result)
          that.notify = result.notify
          if (result.fences) {
            that.fenceList = result.fences
            result.fences.forEach((item) => {
              console.log(that.map)
              console.log(MapService)
              // that.map
              MapService.setFence(that.map, JSON.parse(item.fenceRadius))
            })
          }
          if (result.reportDetail) {
            that.reportDepart = result.reportDetail
            let shipMarks = []
            result.reportDetail.forEach((item) => {
              item.ships &&
                item.ships.forEach((ship) => {
                  // let shipMark = {name:ship.shipName, lat: 21513980, lon:108373950};
                  shipMarks.push(ship)
                })
            })
            MapService.setShipMarks(that.map, shipMarks, 'InternalShip', this.markerClick)
          }
        }
      })
    },
    markerClick(data) {
      let fence = this.fenceList.filter((t) => t.id == data.data.fenceId)
      const htmlContent =
        '<div>' +
        '<div> 船名：' +
        data.data.name +
        '</div>' +
        '<div> 避风港：' +
        fence.name +
        '</div>' +
        '<div> 所属单位：' +
        data.data.department +
        '</div></div>'
      let infoWindow = MapService.showTip(data.item, htmlContent)
      this.infoWindow = infoWindow
    },
  },
}
</script>

<style lang="scss" scoped>
.pageMain {
  --index: 500;
  --left: 54px;
  --left1: 24px;
  --top: 50px;
  --top16: 16px;
  --right: 54px;
  --bottom: 16px;
  width: 100%;
  height: 100%;
  position: relative;

  #map {
    width: 100%;
    /* 先这样定义页面高度 后面需要动态计算 */
    // height: 100%; //有个bug  第一次进来地图会展示不全  但是再次进来就好了
    height: calc(88vh - 5px);
  }

  .ship-info-window {
    position: absolute;
    top: var(--top);
    left: var(--left);
    z-index: var(--index);
    width: 500px;
    height: 48px;
  }

  .alert-info-window {
    position: absolute;
    top: 80px;
    right: var(--right);
    z-index: var(--index);
    width: 500px;
    height: 48px;
  }

  .map-tool {
    position: absolute;
    bottom: 128px;
    right: var(--top);
    z-index: var(--index);

    .tool-item {
      width: 36px;
      height: 36px;
      border-radius: 4px;
      margin-bottom: 10px;
      cursor: pointer;
    }

    .position {
      background: url('../../../../assets/map/img/3.png') no-repeat;
      background-size: 100%;
    }

    .position:active {
      background: url('../../../../assets/map/img/3-active.png') no-repeat;
      background-size: 100%;
    }

    .big {
      background: url('../../../../assets/map/img/4.png') no-repeat;
      background-size: 100%;
    }

    .big:active {
      background: url('../../../../assets/map/img/4-active.png') no-repeat;
      background-size: 100%;
    }

    .xiao {
      background: url('../../../../assets/map/img/5.png') no-repeat;
      background-size: 100%;
    }

    .xiao:active {
      background: url('../../../../assets/map/img/5-active.png') no-repeat;
      background-size: 100%;
    }
  }

  .botton-tip {
    position: absolute;
    bottom: 15px;
    left: 410px;
    z-index: 500;
    img {
      margin-left: 16px;
    }
    span {
      margin-left: 8px;
    }
  }

  .tip-ship-box {
    margin-bottom: 20px;
    padding: 18px 16px;
    background: #ffffff;
    position: absolute;
    top: 20px;
    right: 30px;
    z-index: 500;
    box-shadow: 0px 3px 8px 0px rgba(31, 49, 141, 0.15);
    border-radius: 5px;
  }
}

.ellipsis {
  overflow: auto; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
  cursor: default; /* 鼠标样式 */
}

.ellipsis::-webkit-scrollbar {
  display: none;
}

// .ellipsis:hover {
//   overflow: visible; /* 鼠标悬停时显示全文 */
//   white-space: normal; /* 允许换行 */
// }

.scrollBar::-webkit-scrollbar {
  width: 8px;
  height: 5px !important;
  /**/
}

.scrollBar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}

.scrollBar::-webkit-scrollbar-thumb {
  background: #399df0;
  border-radius: 10px;
}

.scrollBar::-webkit-scrollbar-thumb:hover {
  background: #399df0;
}

.scrollBar::-webkit-scrollbar-corner {
  background: #179a16;
}

// ============
.notice {
  background: linear-gradient(360deg, rgba(255, 141, 141, 0.2) 0%, #f25252 100%);
  background: linear-gradient(360deg, rgba(255, 141, 141, 0.2) 0%, #f25252 100%);
  min-height: 50px;
}

.notice-title {
  top: -10px;
  left: 145px;
  width: 190px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  background: #f53f3f;
  border-radius: 16px 16px 16px 16px;
  color: #fff;
  font-size: 16px;
}
.line {
  margin-bottom: 15px;
  margin-top: 15px;
  background-color: #bebebe;
  width: 100%;
  height: 2px;
}
</style>
