<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="通知编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['code', validatorRules.code]"
                placeholder="请输入通知编号"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="通知名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['name', validatorRules.name]"
                placeholder="请输入通知名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="计划避风开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                placeholder="请选择计划避风开始时间"
                format="YYYY-MM-DD HH:mm"
                :showTime="true"
                v-decorator="['planStartDate', validatorRules.planStartDate]"
                :trigger-change="true"
                @change="changePlanStartDate"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="计划避风结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                :disabled="!startDate"
                :format="'YYYY-MM-DD HH:mm'"
                :showTime="true"
                placeholder="请选择计划避风结束时间"
                v-decorator="['planEndDate', validatorRules.planEndDate]"
                :trigger-change="true"
                :showToday="true"
                style="width: 100%"
                :disabled-date="disabledDateEnd"
                @change="changeEndDate"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="截止上报时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!--              <j-date-->
              <!--                placeholder='请选择截止上报时间'-->
              <!--                :dateFormat="'YYYY-MM-DD HH:mm'"-->
              <!--                :showTime='true'-->
              <!--                v-decorator="['deadline', validatorRules.deadline]"-->
              <!--                :trigger-change='true'-->
              <!--                style='width: 100%'-->
              <!--              />-->
              <a-date-picker
                ref="deadline"
                :disabled="!startDate"
                placeholder="请选择截止上报时间"
                format="YYYY-MM-DD HH:mm"
                v-decorator="['deadline', validatorRules.deadline]"
                :showTime="true"
                :showToday="true"
                :disabled-date="disabledDate"
                @change="changeDeadline"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-select-user-by-corp v-decorator="['contactInfo', validatorRules.contactInfo]" :multi="false"/>
<!--              <a-form :form="form">-->
<!--                <a-form-item label="用户选择v-decorator" style="width: 500px">-->
<!--                  <j-select-user-by-dep v-decorator="['contactInfo', validatorRules.contactInfo]"/>-->
<!--                  {{ getFormFieldValue('users') }}-->
<!--                </a-form-item>-->

<!--                <a-form-item label="用户选择v-model" style="width: 500px">-->
<!--                  <j-select-user-by-dep v-model="users" ></j-select-user-by-dep>-->
<!--                  {{ users }}-->
<!--                </a-form-item>-->

<!--              </a-form >-->
<!--              <a-select-->
<!--                show-search-->
<!--                :filter-option="filterOption"-->
<!--                :allowClear="true"-->
<!--                style="width: 100%"-->
<!--                @change="changeContact"-->
<!--                :value="contactInfo"-->
<!--                v-decorator="['contactInfo', validatorRules.contactInfo]"-->
<!--              >-->
<!--                <a-select-option v-for="(item, id) in personList" :key="id" :value="item.id"-->
<!--                  >{{ item.name + ' ' + item.phone }}-->
<!--                </a-select-option>-->
<!--              </a-select>-->
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item
              label="通知人员"
              :labelCol="{
                xs: { span: 24 },
                sm: { span: 4 },
              }"
              :wrapperCol="{
                xs: { span: 24 },
                sm: { span: 20 },
              }"
            >
              <j-select-user-by-corp v-decorator="['receiverInfo', validatorRules.receiverInfo]"/>
<!--              <a-select-->
<!--                :value="receiverInfo"-->
<!--                v-decorator="['receiverInfo', validatorRules.receiverInfo]"-->
<!--                mode="multiple"-->
<!--                show-search-->
<!--                placeholder="请选择通知人员"-->
<!--                option-filter-prop="children"-->
<!--                style="width: 100%"-->
<!--                :filter-option="filterOption"-->
<!--                @change="changeReceiver"-->
<!--              >-->
<!--                <a-select-option v-for="(item, id) in personList" :key="id" :value="item.id"-->
<!--                  >{{ item.name + ' ' + item.phone }}-->
<!--                </a-select-option>-->
<!--              </a-select>-->
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item
              label="避风内容"
              :labelCol="{
                xs: { span: 24 },
                sm: { span: 4 },
              }"
              :wrapperCol="{
                xs: { span: 24 },
                sm: { span: 20 },
              }"
            >
              <a-textarea
                rows="4"
                autocomplete="off"
                v-decorator="['content', validatorRules.content]"
                placeholder="请输入避风内容"
              ></a-textarea>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="附件"
              :labelCol="{
                xs: { span: 24 },
                sm: { span: 4 },
              }"
              :wrapperCol="{
                xs: { span: 24 },
                sm: { span: 20 },
              }"
            >
              <j-upload v-decorator="['file']" :isMultiple="true" :limit="1"></j-upload>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { cloneDeep, pick } from 'lodash'
import { validateDuplicateValue } from '@/utils/util'
import { JUpload } from '@/components/jeecg/JUpload'
import moment from 'dayjs'
import monitor from '@views/dashboard/Monitor.vue'
import JSelectUserByCorp from '@comp/jeecgbiz/JSelectUserByCorp.vue'

export default {
  name: 'WindNotificationForm',
  components: {JSelectUserByCorp},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      startDate: null,
      endDate: null,
      deadline: null,
      contactInfo: null,
      receiverInfo: null,
      personList: [],
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        code: {
          rules: [
            { required: true, message: '请输入通知编号!' },
            {
              validator: (rule, value, callback) =>
                validateDuplicateValue('wind_notification', 'code', value, this.model.id, callback),
            },
          ],
        },
        receiverInfo: {
          rules: [{ required: true, message: '请选择通知人员!' }],
        },
        contactInfo: {
          rules: [{ required: true, message: '请选择联系人!' }],
        },
        name: {
          rules: [{ required: true, message: '请输入通知名称!' }],
        },
        planStartDate: {
          rules: [{ required: true, message: '请输入计划避风开始时间!' }],
        },
        planEndDate: {
          rules: [
            { required: true, message: '请输入计划避风结束时间!' },
            { validator: (rule, value, callback) => this.endDateValidate(value, callback) },
          ],
        },
        deadline: {
          rules: [
            { required: true, message: '请输入截止上报时间!' },
            { validator: (rule, value, callback) => this.deadlineValidate(value, callback) },
          ],
        },
        content: {},
      },
      url: {
        add: '/wind/windNotification/add',
        edit: '/wind/windNotification/edit',
        personList: '/person/personInfo/listNoPage',
        queryById: '/wind/windNotification/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
    this.getPersonList()
  },
  methods: {
    deadlineValidate(timeStr, callback) {
      //将timeStr转为时间
      let deadline = new Date(timeStr)
      deadline.setSeconds(0)
      deadline.setMilliseconds(0)
      let now = new Date()
      now.setSeconds(0)
      now.setMilliseconds(0)
      if (deadline <= now) {
        console.log('校验不通过')
        callback(new Error('截止时间应当大于当前时间'))
      }
      if (this.endDate) {
        const endDate = new Date(this.endDate)
        endDate.setSeconds(0)
        endDate.setMilliseconds(0)
        if (deadline > endDate) {
          callback(new Error('截止时间应当小于计划避风结束时间'))
        } else {
          callback()
        }
      }

      callback()
    },
    endDateValidate(timeStr, callback) {
      //将timeStr转为时间
      let endDate = new Date(timeStr)
      if (this.startDate) {
        const startDate = new Date(this.startDate)
        if (endDate < startDate) {
          callback(new Error('计划避风结束时间应当大于开始时间'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    changeDeadline(m, dateStr) {
      console.log('changeDeadline', m, dateStr)
      //手动校验时间
      this.deadline = dateStr
      this.form.validateFields(['deadline'], (errors, values) => {})
    },

    changeEndDate(m, dateStr) {
      this.endDate = dateStr
      this.form.validateFields(['planEndDate'], (errors, values) => {})
    },
    getDedaultDate() {
      let tmp = moment().startOf('day')
      console.log('getDedaultDate', tmp)
      return tmp
    },
    disabledDate(current) {
      //
      //改为大于当前时间 小于 结束时间
      if (this.startDate) {
        const deadline = new Date(current)
        const now = new Date()
        const endDate = new Date(this.endDate)
        now.setHours(0, 0, 0, 0)
        deadline.setHours(0, 0, 0, 0)
        endDate.setHours(0, 0, 0, 0)
        return deadline < now || deadline > endDate
      }
      return false
    },
    disabledDateEnd(current) {
      if (this.startDate) {
        const startDate = new Date(this.startDate)
        const currentDate = new Date(current)
        startDate.setHours(0, 0, 0, 0)
        currentDate.setHours(0, 0, 0, 0)
        return currentDate && currentDate < startDate
      }
      return false
    },
    disabledTime() {
      if (!this.startDate) {
        return {}
      }
      //禁止时段设置为 从现在开始 到this.startDate对应的小时以外的时间
      //禁止分段设置为 从现在开始 到this.startDate对应的分钟以外的时间
      //禁止秒段设置为 从现在开始 到this.startDate对应的秒以外的时间
      const nowHour = moment().hour()
      const nowMin = moment().minute()
      const nowSec = moment().second()
      const startHour = moment(this.startDate).hour()
      const startMin = moment(this.startDate).minute()
      const startSec = moment(this.startDate).second()
      const disabledHours = [
        0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23,
      ].filter((hour) => hour <= startHour && hour >= nowHour)
      const disabledMinutes = []
      for (let i = 0; i < 60; i++) {
        if (i <= startMin && i >= nowMin) {
          continue
        }
        disabledMinutes.push(i)
      }

      const disabledSeconds = []
      for (let i = 0; i < 60; i++) {
        if (i <= startSec && i >= nowSec) {
          continue
        }
        disabledSeconds.push(i)
      }
      return {
        disabledHours: () => disabledHours,
        disabledMinutes: () => disabledMinutes,
        disabledSeconds: () => disabledSeconds,
      }
    },
    changePlanStartDate(value, dateString) {
      this.startDate = dateString
      console.log('changePlanStartDate', value, dateString)
      let validateList = []
      if (this.endDate) {
        validateList.push('planEndDate')
        this.form.validateFields(['planEndDate'], (errors, values) => {
          if (errors) {
          }
        })
      }
      if (this.deadline) {
        validateList.push('deadline')
        this.form.validateFields(['deadline'], (errors, values) => {
          if (errors) {
          }
        })
      }
    },
    changeContact(value) {
      this.contactInfo = value
      console.log('changeContact', value)
    },
    changeReceiver(value) {
      this.receiverInfo = value
      console.log(' changeReceiver', value)
    },
    getPersonList() {
      getAction(this.url.personList).then((res) => {
        if (res.success) {
          this.personList = res.result.records
          // console.log('personList', JSON.stringify(this.personList))
        }
      })
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    add() {
      console.log('formAdd')
      this.receiverInfo = null
      this.contactInfo = null
      this.edit({})
    },
    edit(oldRecord) {
      let record = cloneDeep(oldRecord)
      // this.receiverInfo = record.receiverInfo
      // if (record.receiverInfo != null) {
      //   let obj = JSON.parse(record.receiverInfo)
      //   console.log('contactInfo', obj)
      //   let arr = []
      //   for (let i = 0; i < obj.length; i++) {
      //     arr.push(obj[i].id)
      //   }
      //   this.receiverInfo = arr
      //   record.receiverInfo = this.receiverInfo
      // }
      // if (record.contactInfo != null) {
      //   let obj = JSON.parse(record.contactInfo)
      //   console.log('contactInfo', obj)
      //   this.contactInfo = obj.id
      //   record.contactInfo = this.contactInfo
      // }

      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.startDate = this.model.planStartDate
      this.endDate = this.model.planEndDate
      this.deadline = this.model.deadline
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'code',
            'name',
            'planStartDate',
            'planEndDate',
            'deadline',
            'contactInfo',
            'receiverInfo',
            'content',
            'file'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      console.log('开始验证!')
      this.form.validateFields((err, values) => {
        if (!err) {
          console.log('校验通过', values)
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          if (typeof formData.planStartDate === 'object') {
            formData.planStartDate = formData.planStartDate.format('YYYY-MM-DD HH:mm')
          }
          if (typeof formData.planEndDate === 'object') {
            formData.planEndDate = formData.planEndDate.format('YYYY-MM-DD HH:mm')
          }
          if (typeof formData.deadline === 'object') {
            formData.deadline = formData.deadline.format('YYYY-MM-DD HH:mm')
          }
          console.log('转换格式完毕')
          // let receiverObj = []
          // this.receiverInfo.forEach((item) => {
          //   //每个id
          //   this.personList.forEach((person) => {
          //     if (item == person.id) {
          //       receiverObj.push({
          //         id: person.id,
          //         name: person.name,
          //         phone: person.phone,
          //         userId: person.userId,
          //       })
          //     }
          //   })
          // })
          // formData.receiverInfo = JSON.stringify(receiverObj)
          // console.log('this.contactInfo', this.contactInfo)
          // let contactObj = {}
          // if (this.contactInfo != null) {
          //   this.personList.forEach((item) => {
          //     if (this.contactInfo == item.id) {
          //       contactObj = {
          //         id: item.id,
          //         name: item.name,
          //         phone: item.phone,
          //       }
          //       formData.contactInfo = JSON.stringify(contactObj)
          //       console.log('formData.contactInfo', contactObj)
          //       return
          //     }
          //   })
          // }

          console.log('表单提交数据', formData, this.personList)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        } else {
          console.log(err, values, '验证不通过')
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'code',
          'name',
          'planStartDate',
          'planEndDate',
          'deadline',
          'contactInfo',
          'receiverInfo',
          'file',
          'content'
        )
      )
    },
  },
}
</script>
