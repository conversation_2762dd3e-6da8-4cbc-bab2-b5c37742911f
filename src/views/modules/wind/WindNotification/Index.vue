<template>
  <div class='list-page wind-notification-list'>
    <table-layout
      :search-props='searchProps'
      :table-props='tableProps'
      :rightTitle='rightTitle'
      @init-params='initParams'
      @search-submit='searchQuery'
      @table-change='onTableChange'
    >
      <template #level='{ record ,text}'>
        <a-badge :count='getTitle(record)' class="w-60" :number-style='{ backgroundColor: getColor(record) }' />

        <!--        <a-tag color='#adadad' v-if="record.state == '0'"> 草稿</a-tag>-->
        <!--        <a-tag color='#f49d2a' v-if="record.state == '1'"> 已发布</a-tag>-->
        <!--        <a-tag color='#03b915' v-if="record.state == '2'"> 已结束</a-tag>-->

      </template>
      <template #action='{record}'>
        <a :disabled='record.state != 0' @click='handleEdit(record)'
           :style="getActionButtonColor(record, '编辑')">编辑</a>
        <a-divider type='vertical' />
        <a :disabled='record.state != 0' @click='handleRelease(record)'
           :style="getActionButtonColor(record, '发布')">发布</a>
        <a-divider type='vertical' />
        <a :disabled='record.state != 1' @click='handleSetEnd(record)'
           :style="getActionButtonColor(record, '避风结束')">避风结束</a>
        <a-divider type='vertical' />
        <a @click='handleDetail(record)' :style="getActionButtonColor(record, '详情')">详情</a>
        <a-divider type='vertical' />
        <a type='danger' :disabled='record.state != 0' @click='handleDelete(record.id)'
           :style="getActionButtonColor(record, '删除')">删除</a>
      </template>
    </table-layout>
    <modal ref='modalForm' @ok='modalFormOk'></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { putAction } from '@/api/manage'

export default {
  name: 'WindNotificationList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal
  },
  data() {
    return {
      isorter: {
        //后端处理
        column: '',
        order: ''
      },

      rightTitle: '避风发布',
      // 搜索组件的props
      searchProps: {
        formModel: {
          type: null,
          name: null
        },
        formItems: [
          { key: 'code', label: '通知编号', type: 'text' },
          { key: 'name', label: '通知名称', type: 'text' }
        ]
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0
        },

        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            }
          },
          {
            title: '通知编号',
            align: 'center',
            dataIndex: 'code'
          },
          {
            title: '通知名称',
            align: 'center',
            dataIndex: 'name'
          },
          {
            title: '计划避风开始时间',
            align: 'center',
            dataIndex: 'planStartDate',
            customRender: (text) => {
              return !text ? '' : text.length > 10 ? text.substr(0, 16) : text
            }
          },
          {
            title: '计划避风结束时间',
            align: 'center',
            dataIndex: 'planEndDate',
            customRender: (text) => {
              return !text ? '' : text.length > 10 ? text.substr(0, 16) : text
            }
          },
          {
            title: '截止上报时间',
            align: 'center',
            dataIndex: 'deadline',
            customRender: (text) => {
              return !text ? '' : text.length > 10 ? text.substr(0, 16) : text
            }
          },
          {
            title: '通知状态',
            align: 'center',
            dataIndex: 'state',
            scopedSlots: { customRender: 'level' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 280,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons1: [
          {
            text: '编辑',
            handler: this.handleEdit,
            disabled: (record) => {
              return record.state != 0 //草稿才能编辑
            }
          },
          {
            text: '发布',
            type: 'warning',
            handler: this.handleRelease,
            disabled: (record) => {
              return record.state != 0 //草稿才能发布
            }
          },
          {
            text: '避风结束',
            type: 'success',
            handler: this.handleSetEnd,
            disabled: (record) => {
              //改为当前时间超过计划结束时间也算结束
              return record.state != 1
            }
          },
          {
            text: '查看',
            handler: this.handleDetail
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
            disabled: (record) => {
              return record.state != 0 //草稿才能删除
            }
          }
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd
          }
        ]
      },

      url: {
        list: '/wind/windNotification/list',
        delete: '/wind/windNotification/delete',
        setEnd: '/wind/windNotification/setEnd',
        release: '/wind/windNotification/release',
        deleteBatch: '/wind/windNotification/deleteBatch',
        exportXlsUrl: '/wind/windNotification/exportXls',
        importExcelUrl: 'wind/windNotification/importExcel',
        noitifyList: 'wind/windNotification/noitifyList'
      }
    }
  },
  created() {
  },
  computed: {},
  watch: {},
  methods: {
    onTableChange(params) {
      console.log('onTableChange', params)
      this.loadData(params)
    },


    getActionButtonColor(record, title) {
      let color = '#adadad'
      if (title === '编辑') {
        color = record.state != 0 ? '#adadad' : '#758bfb'
      }
      if (title === '发布') {
        color = record.state != 0 ? '#adadad' : '#f7af50'
      }
      if (title === '避风结束') {
        color = record.state != 1 ? '#adadad' : '#86be26'
      }
      if (title === '详情') {
        color = '#4e6afa'
      }
      if (title === '删除') {
        color = record.state != 0 ? '#adadad' : '#db0e2f'
      }
      return {
        color: color
      }
    },
    getColor(record) {
      let title = this.getTitle(record)
      console.log('title', title)
      let state = record.state
      if (state == '0') {
        return '#adadad' //草稿
      }
      if (title == '已发布') {
        return '#f49d2a' //已发布
      }
      if (title == '已结束') {
        return '#03b915' //已结束
      }

    },
    getTitle(record) {
      let state = record.state
      if (state == '0') {
        return '草稿'
      }
      let now = new Date().getTime()
      let endDate = new Date(record.planEndDate).getTime()
      if (record.state !== 1 || now > endDate) {
        return '已结束'
      }
      if (state == '1') {
        return '已发布'
      }
      if (state == '2') {
        return '已结束'
      }

    },
    handleSetEnd(record) {
      putAction(this.url.setEnd, { id: record.id }).then((res) => {
        if (res.success) {
          this.$message.success('操作成功')
          this.loadData()
        }
      })
    },
    initParams(mergeParams) {
      console.log('initParams-mergeParams', mergeParams)
      this.queryParam = mergeParams.params
    },
    handleRelease(record) {
      putAction(this.url.release, record).then((res) => {
        if (res.success) {
          this.$message.success('操作成功')
          this.loadData()
        } else {
          this.$message.warning(res.message)
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>

.right-card {
  ::v-deep .ant-badge-multiple-words {
    padding: 0 20px;
  }
}

</style>