<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item
              label="避风通知"
              :labelCol="{
                xs: { span: 24 },
                sm: { span: 3 },
              }"
              :wrapperCol="{
                xs: { span: 24 },
                sm: { span: 20 },
              }"
            >
              <a-input
                v-if="!isAddMode"
                autocomplete="false"
                :value="editData.windNotification.name"
                disabled
              ></a-input>

              <a-select
                v-if="isAddMode"
                :allowClear="true"
                style="width: 100%"
                placeholder="请选择避风通知"
                v-decorator="['notifyId', validatorRules.notifyId]"
                @change="changeNotify"
              >
                <a-select-option v-for="(item, id) in notifyList" :key="id" :value="item.notificationId"
                  >{{ item.windNotification.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <!-- <a-col :span="12">
            <a-form-item label="计划开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择计划开始日期"
                v-decorator="['planStartDate', validatorRules.planStartDate]"
                :trigger-change="true"
                :show-time="true"
                date-format="YYYY-MM-DD HH:mm"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="计划结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择计划结束日期"
                v-decorator="['planEndDate', validatorRules.planEndDate]"
                :trigger-change="true"
                :show-time="true"
                date-format="YYYY-MM-DD HH:mm"
                style="width: 100%"
              />
            </a-form-item>
          </a-col> -->
          <a-col :span="12">
            <a-form-item label="截止上报时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                disabled
                class="warningInput"
                placeholder="请选择"
                v-decorator="['deadline', validatorRules.deadline]"
                :trigger-change="true"
                :show-time="true"
                date-format="YYYY-MM-DD HH:mm"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系人信息" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-select-user-by-corp v-decorator="['contactInfo', validatorRules.contactInfo]" :multi="false"/>
              <!-- <a-select
                :allowClear="true"
                style="width: 100%"
                placeholder="请选择联系人信息"
                v-decorator="['contactInfo', validatorRules.contactInfo]"
              >
                <a-select-option v-for="(item, id) in personList" :key="id" :value="item.id"
                  >{{ item.name + ' ' + item.phone }}
                </a-select-option>
              </a-select> -->
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="报备人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['reporter', {}]" autocomplete="off" disabled placeholder="请输入报备人"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="报备单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['unitName', {}]"
                autocomplete="off"
                disabled
                placeholder="请输入报备单位"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="备注"
              :labelCol="{
                xs: { span: 24 },
                sm: { span: 3 },
              }"
              :wrapperCol="{
                xs: { span: 24 },
                sm: { span: 20 },
              }"
            >
              <a-textarea
                autocomplete="false"
                v-decorator="['remark', validatorRules.remark]"
                rows="4"
                placeholder="请输入备注"
              ></a-textarea>
            </a-form-item>
          </a-col>

          <a-col v-if="showFlowSubmitButton" :span="12" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <table-layout style="height: 350px" :table-props="tableProps" @init-params="initParams">
              <!--                新增按钮插槽-->
              <template #header="{ record }">
                <a-button :disabled="formDisabled" type="primary" @click="addRow" icon="plus-circle">新增 </a-button>
              </template>

              <template #ship="{ record }">
                <a-select
                  :allowClear="true"
                  style="width: 100%"
                  v-model="record.shipId"
                  @change="handleChangeShip(record)"
                  placeholder="请选择船舶"
                >
                  <a-select-option v-for="item in shipList" :key="item.name" :value="item.shipDataId">
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </template>
              <template #fence="{ record }">
                <a-select placeholder="请选择避风港" :allowClear="true" style="width: 100%" v-model="record.fenceId">
                  <a-select-option v-for="(item, id) in fenceList" :key="id" :value="item.id">
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </template>

              <template #remark="{ record }">
                <a-textarea
                  autocomplete="false"
                  v-model="record.remark"
                  :auto-size="{ minRows: 1 }"
                  placeholder="请输入备注"
                ></a-textarea>
              </template>
              <template #personTotal="{ record }">
                <a-input
                  v-model="record.personTotal"
                  autocomplete="false"
                  placeholder="请输入"
                  @blur="validatePersonTotal(record.personTotal)"
                ></a-input>
              </template>
              <template #shipContact="{ record }">
                <a-input
                  v-model="record.shipContact"
                  autocomplete="false"
                  placeholder="请输入"
                  @blur="validateShipContact(record.shipContact)"
                ></a-input>
              </template>
            </table-layout>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction, postAction, deleteAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import { mapGetters } from 'vuex'
import { filterMultiDictText, getDictItems } from '@/components/dict/JDictSelectUtil'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import JSelectUserByCorp from '@comp/jeecgbiz/JSelectUserByCorp.vue'
import TableLayout from '@/components/tableLayout/TableLayout.vue'
import { readonly } from 'vue'
import Antd from 'ant-design-vue'

export default {
  name: 'WindReportForm',
  mixins: [JeecgTreeListMixin],
  components: {
    TableLayout,
    JSelectUserByCorp,
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      detailMode: false,
      isAddMode: true, //是否新增模式
      editData: {},
      disableMixinCreated: true,
      unitName: '',
      notifyId: '',
      shipList: [],
      fenceList: [], //围栏列表
      notifyList: [], //避风通知数据列表
      personList: [], //人员
      contactInfo: {}, //联系人信息
      tableProps: {
        dataSource: [],
        ipagination: false,
        rowKey: 'index',
        columns: [
          {
            title: '序号',
            dataIndex: '',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '船名',
            align: 'center',
            dateIndex: 'shipId',
            width: 170,
            scopedSlots: { customRender: 'ship' },
          },
          {
            title: 'MMSI',
            dataIndex: 'mmsi',
            align: 'center',
            width: 100,
          },
          {
            title: '船上紧急联系电话',
            align: 'center',
            dataIndex: 'shipContact',
            width: 130,
            scopedSlots: { customRender: 'shipContact' },
          },
          {
            title: '在船人数',
            width: 80,
            align: 'center',
            scopedSlots: { customRender: 'personTotal' },
            dateIndex: 'peronTotal',
          },
          {
            title: '避风港',
            align: 'center',
            dateIndex: 'fenceId',
            width: 140,
            scopedSlots: { customRender: 'fence' },
          },
          // {
          //   title: '备注',
          //   align: 'center',
          //   dataIndex: 'remark',
          //   width: 160,
          //   scopedSlots: { customRender: 'remark' }
          // },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            // fixed: 'right',
            width: 65,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '删除',
            type: 'danger',
            disabled: (record) => {
              return this.formDisabled
            },
            handler: this.removeRow,
          },
        ],

        // headerButtons1: [
        //   {
        //     text: '新增',
        //     icon: 'plus-circle',
        //     handler: this.addRow,
        //     disabled: (record) => {
        //       console.log('headerButton,add', record)
        //     }
        //   }
        // ]
      },

      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        // planStartDate: {
        //   rules: [{ required: true, message: '请输入计划开始日期!' }],
        // },
        // planEndDate: {
        //   rules: [{ required: true, message: '请输入计划结束日期!' }],
        // },
        deadline: {
          rules: [{ required: true, message: '请输入截止上报时间!' }],
        },
        contactInfo: {
          rules: [{ required: true, message: '请输入联系人信息!' }],
        },
        notifyId: {
          rules: [{ required: true, message: '请选择避风通知!' }],
        },
        remark: {
          rules: [{ max: 500, message: '备注不能超过500个字' }],
        },

        shipContact: {
          //校验为手机号码
          rules: [{ pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码' }],
        },
        personTotal: {
          rules: [
            { required: true, message: '请输入在船人数' },
            { pattern: /^\+?[1-9][0-9]*$/, message: '请输入正整数' },
          ],
        },
        shipId: {
          rules: [{ required: true, message: '请选择船舶' }],
        },
        fenceId: {
          rules: [{ required: true, message: '请选择避风港' }],
        },
      },
      url: {
        list: '/wind/windReportDetail/list',
        delete: '/wind/windReportDetail/delete',
        add: '/wind/windReport/add',
        addDetail: '/wind/windReportDetail/add',
        edit: '/wind/windReport/edit',
        queryById: '/wind/windReport/queryById',
        personList: '/person/personInfo/listNoPage',
        notifyList: '/wind/windNotifRecord/getListByLoginUser',
        fenList: '/project/projectFence/list',
        shipList: '/wind/windReport/getShipList', //编辑的时候用这个 获取的是同单位的船进退场记录
        shipList1: '/departureWarning/shipInfo/list', //查看的时候获取的是所有的ship_info记录
        unitUrl: '/person/personInfo/queryByUsername',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    console.log('this.created')
    this.showFlowData()
    this.getPersonList()
    this.getFenceList()
    this.username = this.userInfo().username
  },

  methods: {
    validatePhone(value) {
      if (!value) {
        this.$message.error('手机号码不能为空')
        return false
      }
      const reg = /^1[3|4|5|7|8|9][0-9]\d{8}$/
      if (!reg.test(shipContact)) {
        this.$message.error('手机号码格式不正确')
        return false
      }
      return true
    },

    validateShipContact(value) {
      if (!value) {
        this.$message.error('联系人不能为空')
        return false
      }
      return true
    },
    validatePersonTotal(value) {
      console.log('校验人数', value)
      if (!value) {
        this.$message.error('人数不能为空')
        return false
      }
      if (isNaN(value)) {
        this.$message.warning('人数必须是数字')
      }
      if (parseInt(value) !== parseFloat(value) || value <= 0) {
        this.$message.warning('人数必须是正整数')
        return false
      }
      return true
    },

    validateRecord() {
      console.log('校验表格数据')
      for (let index = 0; index < this.tableProps.dataSource.length; index++) {
        const record = this.tableProps.dataSource[index]
        let fenceId = record.fenceId
        let personTotal = record.personTotal
        let shipContact = record.shipContact
        let shipId = record.shipId
        if (!shipId) {
          this.$message.error('请选择船舶')
          return false
        }
        if (!personTotal) {
          this.$message.error('请输入人数')
          return false
        }
        if (isNaN(personTotal) || parseInt(personTotal) < 0) {
          this.$message.error('人数不合法')
          return false
        }
        if (!shipContact) {
          this.$message.error('请输入联系电话!')
          return false
        }
        const reg = /^1[3|4|5|7|8|9][0-9]\d{8}$/
        if (!reg.test(shipContact)) {
          this.$message.error('手机号码格式不正确')
          return false
        }
        if (!fenceId) {
          this.$message.error('请选择避风港')
          return false
        }
      }
      return true
    },

    removeRow(record) {
      console.log('removeRow', record)
      if (record.id) {
        deleteAction(this.url.delete, { id: record.id }).then((res) => {
          if (res.success) {
            this.$message.success('操作成功')
            const index = this.tableProps.dataSource.findIndex((item) => item.id === record.id)
            if (index > -1) {
              this.tableProps.dataSource.splice(index, 1)
            }
          } else {
            this.$message.warning(res.message)
          }
        })
      } else {
        const index = this.tableProps.dataSource.findIndex((item) => item.index === record.index)
        if (index > -1) {
          this.tableProps.dataSource.splice(index, 1)
        }
      }
    },
    searchQuery(obj) {
      console.log('searchQuery', obj)
    },

    handleChangeShip(obj) {
      console.log(obj)
      this.shipList.forEach((item) => {
        if (item.shipDataId === obj.shipId) {
          obj.name = item.name
          obj.mmsi = item.mmsi
        }
      })
    },
    initParams(mergeParams) {
      console.log('initParams-mergeParams', mergeParams)
      this.queryParam = mergeParams.params
    },

    loadShipList() {
      console.log('this.formDisabled', this.formDisabled, this.detailMode)
      if (!this.detailMode) {
        getAction(this.url.shipList).then((res) => {
          if (res.success) {
            this.shipList = res.result
          }
        })
      } else {
        getAction(this.url.shipList1).then((res) => {
          if (res.success) {
            this.shipList = res.result.records
            this.shipList.forEach((item) => {
              item.shipDataId = item.id
            })
            console.log('this.shipList', this.shipList)
          }
        })
      }
    },
    changeShip(record) {
      console.log(record)
    },
    ...mapGetters(['userInfo']),
    getFenceList() {
      getAction(this.url.fenList, { fenceType: '0' }).then((res) => {
        if (res.success) {
          this.fenceList = res.result.records
        }
      })
    },
    getNotifyList() {
      let username = this.userInfo().username
      getAction(this.url.notifyList, { username: username }).then((res) => {
        if (res.success) {
          this.notifyList = []
          let now = new Date()
          res.result.forEach((item) => {
            let deadline = new Date(item.windNotification.deadline)
            if (deadline.getTime() > now.getTime()) {
              this.notifyList.push(item)
            }
          })
        }
      })
      getAction(this.url.unitUrl, {}).then((res) => {
        if (res.success) {
          this.form.setFieldsValue({ reporter: res.result.name })
          this.form.setFieldsValue({ unitName: res.result.department })
        }
      })
    },
    addRow() {
      if (!this.validateRecord()) {
        return
      }
      this.tableProps.dataSource.push({
        id: undefined,
        index: new Date().getTime().toString(),
        shipId: undefined,
        mmsi: undefined,
        shipContact: undefined,
        peronTotal: undefined,
        fenceId: undefined,
        remark: undefined,
        personTotal: undefined,
      })
    },

    getPersonList() {
      getAction(this.url.personList).then((res) => {
        if (res.success) {
          this.personList = res.result.records
        }
      })
    },

    add() {
      this.getNotifyList()
      this.edit({ isAddMode: this.isAddMode })
    },
    edit(record) {
      console.log('edit', record)
      if (record.hasOwnProperty('detailMode')) {
        this.detailMode = record.detailMode
        console.log(this.detailMode)
      } else {
        this.detailMode = false
      }
      this.loadShipList()
      let recordNew = JSON.parse(JSON.stringify(record))
      if (recordNew && recordNew.details) {
        this.tableProps.dataSource = recordNew.details
      } else {
        this.tableProps.dataSource = []
      }
      this.isAddMode = recordNew.hasOwnProperty('isAddMode')
      this.editData = recordNew
      this.form.resetFields()
      this.model = Object.assign({}, recordNew)
      this.visible = true

      if (recordNew.contactInfo != null) {
        let obj = JSON.parse(recordNew.contactInfo)
        this.contactInfo = obj.id
      }

      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'createTime',
            'sysOrgCode',
            // 'planStartDate',
            // 'planEndDate',
            'deadline',
            'contactInfo',
            'unitName',
            'reporter',
            'remark'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let shipIds = this.tableProps.dataSource.map((obj) => obj.shipId)
          let uniqueShipIds = [...new Set(shipIds)]
          if (!this.validateRecord()) {
            that.confirmLoading = false
            return
          }
          if (shipIds.length !== uniqueShipIds.length) {
            that.$message.warning('不能选择重复的船舶')
            that.confirmLoading = false
            return
          }

          let formData = Object.assign(this.model, values)
          let details = this.tableProps.dataSource
          formData.details = details
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                this.tableProps.dataSource = []
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'createTime',
          'sysOrgCode',
          // 'planStartDate',
          // 'planEndDate',
          'deadline',
          'contactInfo',
          'unitName',
          'reporter',
          'remark'
        )
      )
    },
    changeNotify(notifyId) {
      console.log('notifyId', notifyId)
      let notify = this.notifyList.find((obj) => obj.windNotification.id === notifyId)
      if (notify) {
        this.form.setFieldsValue({ deadline: notify.windNotification.deadline })
      }
    },
  },
}
</script>
<style lang="scss">
.warningInput {
  input {
    color: red;
  }
}
</style>
