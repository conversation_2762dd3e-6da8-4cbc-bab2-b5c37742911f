<template>
  <div class="list-page wind-report-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="loadData"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk" @cancel="modalFormOk" @close="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { getAction } from '@/api/manage'
import { mapGetters } from 'vuex'

export default {
  name: 'WindReportList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {
      notifyList: [],
      rightTitle: '报备列表',
      // 搜索组件的props

      searchProps: {
        formModel: {
          type: null,
          name: null,
        },
        formItems: [{ key: 'name', label: '避风通知' }],
      },

      // 表格组件的props
      tableProps: {
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '避风通知',
            align: 'center',
            dataIndex: 'windNotification',
            customRender: (text, r, index) => {
              return text ? text.name : ''
            },
          },
          {
            title: '报备单位',
            align: 'center',
            dataIndex: 'unitName',
          },
          {
            title: '截止上报时间',
            align: 'center',
            dataIndex: 'deadline',
            customRender: (text, r, index) => {
              return text ? text.substr(0, 16) : ''
            },
          },
          {
            title: '创建时间',
            align: 'center',
            dataIndex: 'createTime',
            customRender: (text, r, index) => {
              return text ? text.substr(0, 16) : ''
            },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 175,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
            disabled: this.checkDisabled,
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
            disabled: this.checkDisabled,
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],
      },

      url: {
        list: '/wind/windReport/list',
        delete: '/wind/windReport/delete',
        deleteBatch: '/wind/windReport/deleteBatch',
        exportXlsUrl: '/wind/windReport/exportXls',
        importExcelUrl: 'wind/windReport/importExcel',
        notifyList: '/wind/windNotifRecord/getListByLoginUser',
      },
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    onTableChange(params) {
      this.loadData(params)
    },
    checkDisabled(record) {
      if (record.windNotification.state == 2) {
        return true
      }
      let deadline = new Date(record.windNotification.deadline)
      return deadline < new Date()
    },
    initParams(mergeParams) {
      console.error('mergeParams', mergeParams)
      this.queryParam = mergeParams.params

    },
    ...mapGetters(['userInfo']),
    handleAdd() {
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.disableSubmit = false
      this.$refs.modalForm.add()
    },
    // handleEdit(record) {
    //   this.$refs.modalForm.edit(record)
    //   this.$refs.modalForm.title = '编辑'
    // },
    // handleDelete(record) {
    //   this.$refs.modalForm.remove(record)
    // },
    handleDetail(record) {
      record['detailMode'] = true
      this.$refs.modalForm.disableSubmit = true
      this.$refs.modalForm.title = '详情'
      this.$refs.modalForm.edit(record)
    },
  },
}
</script>
