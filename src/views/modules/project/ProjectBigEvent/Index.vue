<template>
  <div class="list-page wind-notification-list">
    <table-layout
      :search-props="searchProps"
      :table-props="tableProps"
      :rightTitle="rightTitle"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
      <template #content='{record}'>
        <a-tooltip>
          <template slot="title">
            {{record.content}}
          </template>
          <div class="text-container">{{record.content}}</div>
        </a-tooltip>
      </template>
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { putAction } from '@/api/manage'

export default {
  name: 'ProjectBigEventList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {
      isorter: {
        column: 'happenTime',
        order: 'desc',
      },
      rightTitle: '工程大事记',
      // 搜索组件的props
      searchProps: {
        formModel: {
          name: null,
        },
        formItems: [
          {
            label: '大事记类型',
            key: 'bigEventType',
            classType: 'list',
            dictCode: 'big_event_type',
            placeholder: '请选择大事记类型'
          }
        ],
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },

        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '大事记标题',
            align: 'center',
            dataIndex: 'title',
          },
          {
            title: '大事记类型',
            align: 'center',
            dataIndex: 'bigEventType_dictText',
          },
          {
            title: '大事记内容',
            align: 'center',
            dataIndex: 'content',
            scopedSlots: { customRender: 'content' },
          },
          {
            title: '发生时间',
            align: 'center',
            dataIndex: 'happenTime',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 175,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],
      },
      url: {
        list: '/project/projectBigEvent/list',
        delete: '/project/projectBigEvent/delete',
        deleteBatch: '/project/projectBigEvent/deleteBatch'
      },
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    initParams(mergeParams) {
      console.log('initParams-mergeParams', mergeParams)
      this.queryParam = mergeParams.params
    },
    onTableChange(params) {
      console.log('onTableChange', params)
      this.loadData(params)
    },
  },
}
</script>
<style lang="scss">
  .text-container {
    overflow: hidden; /* 隐藏超出容器的内容 */
    text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
    display: -webkit-box; /* 作为弹性伸缩盒子模型显示 */
    -webkit-box-orient: vertical; /* 垂直排列子元素 */
    -webkit-line-clamp: 2; /* 限制在两行 */
  }
</style>