<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="大事记标题" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['title', validatorRules.title]"
                placeholder="请输入大事记标题"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="大事记类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['bigEventType', validatorRules.bigEventType]"
                :trigger-change="true"
                dictCode="big_event_type"
                placeholder="请选择大事记类型"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="发生时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择发生时间"
                v-decorator="['happenTime',validatorRules.happenTime]"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="大事记内容" :labelCol="{
    xs: { span: 24 },
    sm: { span: 3 },
  }" :wrapperCol="{
    xs: { span: 24 },
    sm: { span: 21 },
  }">
              <a-textarea rows="4" autocomplete="off" v-decorator="['content', validatorRules.content]"
                          placeholder="请输入"></a-textarea>
            </a-form-item>
          </a-col>
<!--          <a-col :span="12">-->
<!--            <a-form-item label="图片资料" :labelCol="labelCol" :wrapperCol="wrapperCol">-->
<!--              <j-image-upload isMultiple v-decorator="['file']"></j-image-upload>-->
<!--            </a-form-item>-->
<!--          </a-col>-->
          <a-col :span="24">
            <a-form-item label="图片资料" :labelCol="{
    xs: { span: 24 },
    sm: { span: 3 },
  }" :wrapperCol="{
    xs: { span: 24 },
    sm: { span: 21 },
  }">
              <j-upload text="图片资料" :fileType="'image'" v-decorator="['file', validatorRules.file]" size="'mine'">
                <template slot="icon">
                  <a-icon type="plus-circle" theme="filled" :style="{ fontSize: '24px', color: '#0096ff' }" />
                </template>
              </j-upload>
            </a-form-item>
          </a-col>
        </a-row>
<!--        <a-row>-->
<!--          <div class="line"> 图片资料 </div>-->
<!--        </a-row>-->

      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction } from '@/api/manage'
import pick from 'lodash.pick'
import _ from 'lodash'
import { JUpload } from '@/components/jeecg/JUpload'
export default {
  name: 'ProjectBigEventForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => { },
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
      },
      confirmLoading: false,
      validatorRules: {
        title: {
          rules: [{ required: true, message: '请输入大事记标题!' }],
        },
        bigEventType: {
          rules: [{ required: true, message: '请选择大事记类型!' }],
        },
        happenTime: {
          rules: [{ required: true, message: '请输入发生时间!' }],
        },
        content: {
          rules: [{ max: 200, message: '大事记内容不能超过200个字' }],
        },
        file: {
          rules: [{ required: true, message: '请上传图片资料!' }],
        },
      },
      url: {
        add: '/project/projectBigEvent/add',
        edit: '/project/projectBigEvent/edit',
        queryById: '/project/projectBigEvent/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {

  },
  methods: {
    add() {
      this.edit({})
    },
    edit(oldRecord) {
      let record = _.cloneDeep(oldRecord)

      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(this.model, 'title', 'bigEventType', 'happenTime', 'content', 'file')
        )
      })
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values);

          console.log('表单提交数据', formData, this.personList)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(row, 'title', 'bigEventType', 'happenTime', 'content', 'file')
      )
    },
  },
}
</script>
