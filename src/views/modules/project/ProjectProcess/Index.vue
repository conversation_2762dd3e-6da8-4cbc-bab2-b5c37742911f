<template>
  <div class="list-page project-process-list">
    <table-layout
      ref="tableLayout1"
      :table-props="tableProps"
      :rightTitle="rightTitle"
      :tree-props="treeProps"
      @tree-init="onTreeInit"
      @tree-select="handleTreeSelect"
      @table-change="handleSearchSubmit"
      @search-submit="handleSearchSubmit"
    >
    <template #icon='{ record }'>
        <img
          v-if='record.icon'
          :src='getImageUrl(record.icon)'
          style='width: 30px; height: 30px'
          @click='clickImage(record.icon)'
        />
      </template>
    </table-layout>
    <!-- <a-modal v-model="visable" title="预览" ok-text="确定" cancel-text="取消" @ok="visable = false">
      <img :src="previewImageUrl" style="max-width: 100%; max-height: 100%" />
    </a-modal> -->
    <!-- 图标太小产品不要预览了 -->
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import { deleteAction } from '@/api/manage'
import Modal from './components/Modal'
import { getFileAccessHttpUrl } from '@/api/manage'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import extension from './extension'

export default {
  name: 'ProjectProcessList',
  mixins: [JeecgTreeListMixin, extension],
  components: {
    Modal,
  },
  data() {
    return {
      previewImageUrl: '',
      visable: false,
      disableMixinCreated: true,
      isorter: {
        column: 'processCode',
        order: 'asc',
      },
      rightTitle: '工序列表',
      searchData: {},
      type: '',
      // 树组件的props
      treeProps: {
        dictCode: 'B04',
        isSelectParentNodes: false,
      },
      // 搜索组件的props
      // searchProps: {
      //   formModel: {},
      //   formItems: []
      // },
      // 表格组件的props

      tableProps: {
        loading: false,
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 80,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '工序编号',
            align: 'center',
            width: 80,
            dataIndex: 'processCode',
          },
          {
            title: '工序名称',
            align: 'center',
            width: 200,
            dataIndex: 'name',
          },
          {
            title: '工作内容',
            width: 200,
            align: 'center',
            dataIndex: 'workContent',
          },
          {
            title: '理论耗时(小时)',
            align: 'center',
            width: 120,
            dataIndex: 'theoryHours',
          },
          {
            title: '开启预警',
            dataIndex: 'warningEnable',
            align: 'center',
            width: 80,
            customRender: (val) => {
              return val==1?"是":"否"
            },
          },
          {
            title: '图标',
            dataIndex: 'icon',
            align: 'center',
            width: 80,
            scopedSlots: { customRender: 'icon' },
          },
          // {
          //   title: '进度权重（%）',
          //   align: 'center',
          //   dataIndex: 'weight',
          // },
          // {
          //   title: '完工标志', //'是否为形象进度工序',
          //   align: 'center',
          //   dataIndex: 'isProgress_dictText',
          //   customRender: (text) => {
          //     return text === '是' ? '√' : ''
          //   },
          // },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 175,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],
      },

      url: {
        list: '/project/projectProcess/list',
        delete: '/project/projectProcess/delete',
        deleteBatch: '/project/projectProcess/deleteBatch',
        exportXlsUrl: '/project/projectProcess/exportXls',
        importExcelUrl: 'project/projectProcess/importExcel',
      },
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    clickImage(url) {
      this.previewImageUrl = this.getImageUrl(url)
      this.visable = true
    },
    getImageUrl(url) {
      return getFileAccessHttpUrl(url)
    },
    handleAdd() {
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.edit({ type: this.getCurrentTreeId(), weight: 0, isProgress: 0, warningEnable: 0 })
      this.$refs.modalForm.disableSubmit = false
    },
    handleEdit(record) {
      this.$refs.modalForm.title = '编辑'

      record['type'] = this.getCurrentTreeId()
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.disableSubmit = false
    },

    handleDelete: function (id) {
      if (typeof id === 'object') {
        id = id.id
      }
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      let that = this
      this.$confirm({
        title: '提醒',
        content: '确认要删除吗?',
        okText: '确认',
        cancelText: '取消',
        onOk() {
          deleteAction(that.url.delete, { id: id }).then((res) => {
            if (res.success) {
              that.$message.success('删除成功！')
              that.loadData({ type: that.getCurrentTreeId() })
            } else {
              that.$message.warning(res.message)
            }
          })
        },
        onCancel() {},
      })
    },

    modalFormOk() {
      this.loadData({ type: this.getCurrentTreeId() })
    },

    handleSearchSubmit(params) {
      this.loadData({ type: this.getCurrentTreeId() })
    },
    handleTreeSelect({ selectedKeys, nodeData }) {
      // 处理树节点选择事件
      if (!selectedKeys) {
        return
      }
      this.type = selectedKeys[0]
      this.tableProps.ipagination.current = 1
      this.loadData({ type: this.type })
    },
    getCurrentTreeId() {
      let selectedKeys = this.$refs.tableLayout1.mergeParams.selectedKeys
      let treeId = selectedKeys ? selectedKeys[0] : undefined
      return treeId
    },
    onTreeInit(params) {
      this.type = params.params.treeId
      this.loadData({ type: this.type })
    },
  },
}
</script>
