<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" :slot="'detail'" label-wrap>
        <a-row>
          <a-col :span="12" style="display: none">
            <a-form-item label="工序类型（关联分类字典process_type）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-category-select
                v-decorator="['type', validatorRules.type]"
                pcode="B04"
                placeholder="请选择工序类型（关联分类字典process_type）"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="工序编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['processCode', validatorRules.processCode]"
                placeholder="请输入工序编码"
                autocomplete="off"
              ></a-input>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="工序名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['name', validatorRules.name]"
                placeholder="请输入工序名称"
                autocomplete="off"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="工作内容" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['workContent', validatorRules.workContent]"
                placeholder="请输入工作内容"
                autocomplete="off"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="理论耗时" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-decorator="['theoryHours', validatorRules.theoryHours]"
                placeholder="请输入理论耗时"
                autocomplete="off"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="进度权重" :labelCol="labelCol" :wrapperCol="wrapperCol" :help="null" class="weightItem">
              <!-- 此处加入 help属性设置为null 或者undefined 强制不显示校验失败的信息提示,改用自定义的提示 -->
              <a-input-number
                id="weight"
                v-decorator="['weight', validatorRules.weight]"
                placeholder="请输入权重"
                style="width: 100%"
                autocomplete="off"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="完工标志" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['isProgress', validatorRules.isProgress]"
                :trigger-change="true"
                dictCode="yn"
                placeholder="请选择形象进度完成标志"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="是否开启预警" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['warningEnable', validatorRules.warningEnable]"
                :trigger-change="true"
                dictCode="yn"
                @change="
                  (val) => {
                    showTable = val == 1
                  }
                "
                placeholder="请选择是否开启预警"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="图标上传" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-image-upload v-decorator="['icon']" :isMultiple="true" :limit="1"></j-image-upload>
            </a-form-item>
          </a-col>

          <a-col :span="12" v-show="showModelSign">
            <a-form-item label="模型标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                v-decorator="['modelSign', validatorRules.modelSign]"
                dictCode="model_sign"
                :trigger-change="true"
                placeholder="请选择模型标识"
              ></j-dict-select-tag>
            </a-form-item>
          </a-col>

          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
    <conditionTable
      :data="model.information"
      :disabled="formDisabled"
      :title="'适宜施工天气'"
      ref="conditionTable"
      :visible="showTable"
    >
    </conditionTable>
  </a-spin>
</template>

<script>
import { httpAction, getAction, postAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue ,validateDuplicateValue2} from '@/utils/util'
import conditionTable from '@/views/modules/ship/shipInfo/components/conditionTable.vue'

export default {
  name: 'ProjectProcessForm',
  components: { conditionTable },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      showModelSign: false,
      fileList1: [],
      weightTips: undefined,
      type: '',
      maxWeight: 0,
      weightMsg: '',
      form: this.$form.createForm(this, {
        // 监听icon的变化
        onFieldsChange: (_, changedFields) => {
          //
          if (changedFields.icon) {
            console.log('changedFields', changedFields)
            this.showModelSign = !!changedFields.icon.value
            // 消失的时候让 modelSign 为空
            if (!changedFields.icon.value) {
              // 设置form 的 modelSign 为空
              this.form.setFieldsValue({ modelSign: '' })
            }
          }
        },
      }),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        type: {
          rules: [{ required: true, message: '请选择工序类型' }],
        },
        isProgress: {
          rules: [{ required: true, message: '请选择是否为设为形象进度完成标志!' }],
        },
        modelSign: {
          rules: [{ required: this.showModelSign, message: '请选择模型标识!' }],
        },
        theoryHours: {
          rules: [{ pattern: /^\d*\.?\d*$/, message: '请输入数字!' }],
        },
        workContent: {
          rules: [
            // { required: true, message: '请输入工作内容!' },
            { max: 50, message: '工作内容不能超过50个字' },

            {
              validator: (rule, value, callback) =>
              validateDuplicateValue2('project_process', 'work_content', value,'type',this.model.type, this.model.id, callback),
            },
          ],
        },
        name: {
          rules: [
            { required: true, message: '工序名称!' },
            { max: 50, message: '工序名称不能超过50个字' },
            {
              validator: (rule, value, callback) =>
              validateDuplicateValue2('project_process', 'name', value,'type',this.model.type, this.model.id, callback),
            },
          ],
        },
        processCode: {
          rules: [
            { required: true, message: '请输入工序编号!' },
            { max: 50, message: '工序编号不能超过50个字' },
            {
              validator: (rule, value, callback) =>
              validateDuplicateValue2('project_process', 'process_code', value,'type',this.model.type, this.model.id, callback),
            },
          ],
        },
        weight: {
          rules: [
            { required: true, message: '请输入权重!' },
            {
              validator: (rule, value, callback) => {
                let numberValue = Number(value)
                if (isNaN(numberValue)) {
                  this.weightMsg = '请输入数字'
                  this.showErrorMsg(this.weightMsg)
                  callback(new Error(this.weightMsg))

                  return
                }
                if (numberValue < 0) {
                  this.weightMsg = '权重不能小于0'
                  this.showErrorMsg(this.weightMsg)
                  callback(new Error(this.weightMsg))
                  return
                }
                this.weightMsg = '当前可分配权重为' + (this.maxWeight - value) + '%'

                if (value && Number(value) <= this.maxWeight) {
                  this.showTips(this.weightMsg)
                  callback()
                } else {
                  this.showErrorMsg(this.weightMsg)
                  callback(new Error(this.weightMsg))
                }
              },
            },
          ],
        },
        warningEnable: {
          rules: [{ required: true, message: '请输入是否开启预警!' }],
        },
      },
      url: {
        add: '/project/projectProcess/add',
        edit: '/project/projectProcess/edit',
        queryById: '/project/projectProcess/queryById',
        queryMaxWeight: '/project/projectProcess/queryMaxWeight',
      },
      // json
      showTable: false,
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    updateSelectedKeys({ selectedKeys, e }) {
      console.log('选中节点：', selectedKeys, '节点数据：', e)
      // 在这里你可以处理选中节点的逻辑
    },

    add() {
      console.log('add操作', obj)
      this.type = obj.type
      let selectedKeys, e
      this.updateSelectedKeys({ selectedKeys, e })
      console.log('add', selectedKeys)
      this.edit({ isProgress: '0', warningEnable: '0', weight: '0' })
    },
    showErrorMsg(msg) {
      this.weightTips && (this.weightTips.innerHTML = msg)
      this.weightTips.style.color = 'red'
    },
    showTips(msg) {
      this.weightTips && (this.weightTips.innerHTML = msg)
      this.weightTips.style.color = '#999'
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      let element = document.querySelector('.weightItem')
      element && element.classList.add('ant-form-item-with-help')
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'isProgress',
            'type',
            'name',
            'processCode',
            'weight',
            'warningEnable',
            'icon',
            'workContent',
            'information',
            'remark',
            'parentId',
            'theoryHours',
            'modelSign'
          )
        )
      })
      console.log('form******', this.form.getFieldsValue())

      if (!document.querySelector('#weight-tips')) {
        let weightDiv = document.querySelector('#weight').parentElement.parentElement
        let weightTips = document.createElement('div')
        weightTips.setAttribute('class', 'ant-form-explain')
        weightTips.setAttribute('id', 'weight-tips')
        weightDiv.appendChild(weightTips)
        weightTips.innerHTML = this.weightMsg
        this.weightTips = weightTips
      }

      this.showTable = record.warningEnable == 1
      console.log('record', record)
      postAction(this.url.queryMaxWeight, { id: record.id, type: record.type }).then((res) => {
        if (res.success) {
          this.maxWeight = res.result
          let value = record.weight
          if (+value <= this.maxWeight) {
            this.weightMsg = '当前可分配权重为' + (this.maxWeight - value) + '%'
            let weightTips = document.querySelector('#weight-tips')
            if (weightTips) {
              weightTips.innerHTML = this.weightMsg
            }
          }
          console.log(res.result)
        }
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        console.log(err, values)
        if (!err) {
          console.log('提交表单')
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            //没有带id 表明是添加
            httpurl += this.url.add
            method = 'post'
          } else {
            //带上id 表明是修改
            httpurl += this.url.edit
            method = 'put'
          }
          console.log('httpurl', httpurl)
          let formData = Object.assign(this.model, values)
          formData.information = this.$refs.conditionTable.getData()
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        } else {
          console.log('表单错误', err)
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          'isProgress',
          'type',
          'name',
          'processCode',
          'weight',
          'workContent',
          'warningEnable',
          'theoryHours',
          'icon',
          'information',
          'remark',
          'parentId',
          'modelSign',
          row
        )
      )
    },
  },
}
</script>
<style lang="less" scoped></style>
