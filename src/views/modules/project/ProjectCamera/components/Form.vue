<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="摄像头类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-search-select-tag v-decorator="['type', validatorRules.type]" dict="camera_type" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="摄像头名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['name', validatorRules.name]" placeholder="请输入摄像头名称" autocomplete="off"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备序列号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['serial', validatorRules.serial]" placeholder="请输入设备序列号" autocomplete="off"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="安装位置" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['position',validatorRules.position]" placeholder="请输入安装位置" autocomplete="off"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="安装日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择安装日期" v-decorator="['setDate']" :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'ProjectCameraForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        type: {
          rules: [{ required: true, message: '请输入摄像头类型!' }],
        },
        name: {
          rules: [
            { required: true, message: '请输入摄像头名称!' },
            { max: 20, message: '摄像头名称不能超过20个字符' },
          ],
        },
        serial: {
          rules: [
            { required: true, message: '请输入设备序列号!' },
            { max: 50, message: '设备序列号不能超过50个字符' },
          ],
        },
        position: {
          rules: [
            { max: 50, message: '安装位置不能超过50个字符' },
          ],
        },
      },
      url: {
        add: '/project/projectCamera/add',
        edit: '/project/projectCamera/edit',
        queryById: '/project/projectCamera/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'type', 'name', 'position', 'setDate', 'serial'))
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'type', 'name', 'position', 'setDate', 'serial'))
    },
  },
}
</script>
