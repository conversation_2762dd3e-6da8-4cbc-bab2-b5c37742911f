<template>
  <div class="list-page project-camera-list">
    <table-layout
      :search-props="searchProps"
      :rightTitle="rightTitle"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'

export default {
  name: 'ProjectCameraList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {
      rightTitle: '摄像头列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          type: null,
          name: null,
        },
        formItems: [
          { key: 'name', label: '摄像头名称' },
          { key: 'type', label: '摄像头类型', dictCode: 'camera_type' },
        ],
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '摄像头类型',
            align: 'center',
            dataIndex: 'type_dictText',
          },
          {
            title: '摄像头名称',
            align: 'center',
            dataIndex: 'name',
          },
          {
            title: '安装位置',
            align: 'center',
            dataIndex: 'position',
          },
          {
            title: '安装日期',
            align: 'center',
            dataIndex: 'setDate',
            customRender: (text) => {
              return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
            },
          },
          {
            title: '设备序列号',
            align: 'center',
            dataIndex: 'serial',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 175,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],
      },

      url: {
        list: '/project/projectCamera/list',
        delete: '/project/projectCamera/delete',
        deleteBatch: '/project/projectCamera/deleteBatch',
        exportXlsUrl: '/project/projectCamera/exportXls',
        importExcelUrl: 'project/projectCamera/importExcel',
      },
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    initParams(mergeParams) {
      console.log('initParams-mergeParams', mergeParams)
      this.queryParam = mergeParams.params
    },
  },
}
</script>
