<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    :destroyOnClose="true"
    cancelText="关闭"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
  >
    <a-spin :spinning="confirmLoading">
      <j-form-container :disabled="disableSubmit">
        <a-form :form="form" slot="detail">
          <a-row>
            <!-- <a-col :span="12" v-if="isShowParent === 1">
              <a-form-item label="父级PBS编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  disabled
                  v-decorator="['parentCode', validatorRules.parentCode]"
                  placeholder="请输入父级PBS编号"
                  autocomplete="off"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="isShowParent === 1">
              <a-form-item label="父级PBS名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  disabled
                  v-decorator="['parentName', validatorRules.parentName]"
                  placeholder="请输入父级PBS名称"
                  autocomplete="off"
                ></a-input>
              </a-form-item>
            </a-col> -->

            <a-col :span="12">
              <a-form-item label="PBS编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  v-decorator="['code', validatorRules.code]"
                  placeholder="请输入PBS编码"
                  :disabled="this.record.id != null"
                  autocomplete="off"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="PBS名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  v-decorator="['name', validatorRules.name]"
                  placeholder="请输入pbs名称"
                  autocomplete="off"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="equipType === 'B06A01A01'">
              <a-form-item label="风机型号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <j-dict-select-tag
                  type="list"
                  v-decorator="['specificationModel', validatorRules.specificationModel]"
                  :trigger-change="true"
                  dictCode="data_fan_info,fan_type,id"
                  placeholder="请选择风机型号"
                />
              </a-form-item>
            </a-col>

            <a-col :span="12" v-if="equipType === 'B06A01A02'">
              <a-form-item label="海缆规格" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <j-dict-select-tag
                  type="list"
                  v-decorator="['specificationModel', validatorRules.specificationModel]"
                  :trigger-change="true"
                  dictCode="data_cable_info,cable_type,id"
                  placeholder="请选择海缆规格"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="['B06A01A01', 'B06A01A02'].includes(equipType)">
              <a-form-item label="适用工序" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-tree-select
                  v-model="applicableProcess"
                  allowClear
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="treeData"
                  placeholder="请选择适用工序"
                  tree-default-expand-all
                  :replaceFields="{
                    children: 'children',
                    value: 'code',
                    key: 'id',
                    label: 'name',
                  }"
                  tree-node-filter-prop="title"
                >
                </a-tree-select>
                <!-- <j-category-select
                treeData="treeData"
                loadData="asyncLoadTreeData"
                v-decorator="['applicableProcess', validatorRules.applicableProcess]"
                pcode="B04"
                placeholder="请选择适用工序"
                @change="applicableProcessChange"
              /> -->
              </a-form-item>

              <!-- <span v-if="key === '0-0-1'" slot="title" slot-scope="{ key, value }" style="color: #08c">
                Child Node1 {{ value }}
              </span> -->
              <!-- </a-tree-select> -->
            </a-col>
            <a-col :span="12" v-if="['B06A01A01', 'B06A01A02'].includes(equipType)">
              <a-form-item label="路由" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <j-dict-select-tag
                  v-decorator="['route', validatorRules.route]"
                  dictCode="B12"
                  type="list"
                  :trigger-change="true"
                  placeholder="请选择路由"
                />
              </a-form-item>
            </a-col>
            <!-- 暂时隐藏此处 -->
            <!-- <a-col :span="12">
            <a-form-item label="图例上传" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-image-upload v-decorator="['picture']" :isMultiple="true" :limit="1"></j-image-upload>
            </a-form-item>
          </a-col> -->
            <a-col :span="12" v-if="equipType === 'B06A01A01' || equipType === 'B06A01A02'">
              <a-form-item label="所属标段" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <j-dict-select-tag
                  type="list"
                  v-decorator="['fanSection', validatorRules.fanSection]"
                  :trigger-change="true"
                  dictCode="fan_section"
                  placeholder="请选择所属标段"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="equipType === 'B06A01A02'">
              <a-form-item label="起点段次" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-tree-select
                  v-decorator="['startSection', validatorRules.startSection]"
                  show-search
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="sectionData"
                  placeholder="请选择起点段次"
                  tree-default-expand-all
                  :replaceFields="{
                    children: 'children',
                    value: 'id',
                    key: 'id',
                    label: 'name',
                  }"
                  tree-node-filter-prop="title"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>

            <a-col :span="12" v-if="equipType === 'B06A01A02'">
              <a-form-item label="终点段次" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-tree-select
                  v-decorator="['endSection', validatorRules.endSection]"
                  show-search
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="sectionData"
                  placeholder="请选择终点段次"
                  tree-default-expand-all
                  :replaceFields="{
                    children: 'children',
                    value: 'id',
                    key: 'id',
                    label: 'name',
                  }"
                  tree-node-filter-prop="title"
                >
                </a-tree-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="模型是否显示" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-select
                  placeholder="请选择是否显示"
                  v-decorator="['isDisplay']"
                  style="width: 100%"
                  @change="handleChange"
                >
                  <a-select-option :value="1"> 显示 </a-select-option>
                  <a-select-option :value="2"> 不显示 </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row v-if="isShowParent === 0">
            <a-col :span="24">
              <a-form-item
                label="坐标信息"
                :labelCol="{
                  xs: { span: 24 },
                  sm: { span: 3 },
                }"
                :wrapperCol="{ xs: { span: 24 }, sm: { span: 20 } }"
              >
                <a-input
                  v-decorator="['point', validatorRules.point]"
                  :placeholder="getPlaceholder()"
                  autocomplete="off"
                ></a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import { message } from 'ant-design-vue'
import JDictSelectTag from '../../../../../components/dict/JDictSelectTag.vue'

export default {
  name: 'ProjectPbsModal',
  components: { JDictSelectTag },
  data() {
    return {
      disableSubmit: false,
      sectionData: [],
      applicableProcess: '',
      record: {},
      treeData: {},
      equipType: 0, //风机 海缆  风机0,海缆1
      isShowParent: 0, //默认不显示父级
      form: this.$form.createForm(this),
      title: '操作',
      width: 800,
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },

      confirmLoading: false,
      validatorRules: {
        code: {
          rules: [
            { required: true, message: '请输入PBS编码!' },
            { max: 20, message: 'PBS编码不能超过20个字符' },
            {
              validator: (rule, value, callback) =>
                validateDuplicateValue('project_pbs', 'code', value, this.model.id, callback),
            },
          ],
        },
        applicableProcess: {
          rules: [{ required: true, message: '请选择适用工序' }],
        },
        route: {
          rules: [{ required: true, message: '请选择适用工序' }],
        },
        startSection: {
          rules: [{ required: true, message: '请选择起点段次' }],
        },
        endSection: {
          rules: [{ required: true, message: '请选择终点段次' }],
        },
        specificationModel: {
          rules: [{ required: true, message: '请选择' + this.equipType == 0 ? '风机型号' : '海缆规格' }],
        },

        name: {
          rules: [
            { required: true, message: '请输入PBS名称!' },
            { max: 20, message: 'PBS名称不能超过20个字符' },
          ],
        },
        // type: {
        //   rules: [{ required: true, message: '请输入pbs类型（）关联分类字典!' }],
        // },
        point: {
          rules: [
            { required: true, message: '请输入坐标信息!' },
            { max: 100, message: '坐标信息不能超过100个字' },
          ],
        },
      },
      url: {
        add: '/project/projectPbs/add',
        edit: '/project/projectPbs/edit',
        queryTreeList: '/project/myCategory/queryTreeList', //查询树结构 传code
        fanStationList: '/project/projectPbs/getFanStationList', //获取风机/升压站的列表
      },
      expandedRowKeys: [],
      pidField: '',
    }
  },
  created() {},
  mounted() {
    this.getTreeData('B04')
    this.getSectionData()
  },
  methods: {
    getPlaceholder() {
      if (this.equipType === 'B06A01A01' || this.equipType == 'B06A01A03') {
        return '请输入坐标信息,示例[108.788633,21.401796]'
      } else if (this.equipType === 'B06A01A02') {
        return '请输入坐标信息,示例[[108.788633,21.401796],[108.788633,21.401796]]'
      }
    },
    getSectionData() {
      getAction(this.url.fanStationList, {}).then((res) => {
        if (res.success) {
          this.sectionData = res.result
        }
      })
    },
    getTreeData(code) {
      httpAction(this.url.queryTreeList + '?code=' + code, '', 'get').then((res) => {
        if (res.success) {
          this.treeData = res.result
          this.treeData.unshift({
            title: '请选择',
            value: undefined,
            key: undefined,
            disabled: false,
            children: [],
          })
          this.treeData.map((item) => {
            item.disabled = item.children && item.children.length > 0
          })
          console.log('treeData', JSON.stringify(this.treeData))
        }
      })
    },
    findCodeById(id) {
      const travel = (data) => {
        for (let i = 0; i < data.length; i++) {
          if (data[i].id === id) {
            return data[i].code
          }
          if (data[i].children) {
            const code = travel(data[i].children)
            if (code !== undefined) {
              return code
            }
          }
        }
      }
      console.log('id查code', this.treeData)
      return travel(this.treeData)
    },
    edit(record) {
      console.log('record', record)
      this.record = record
      if (record.id != null) {
      }
      this.applicableProcess = record.applicableProcess //更新树的当前选中项
      this.equipType = record.type
      console.log('编辑', record)
      this.isShowParent = record.isShowParent
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'parentCode',
            'applicableProcess',
            'specificationModel',
            'point',
            'name',
            'type',
            'route',
            'parentName',
            'code',
            'name',
            'fanSection',
            'startSection',
            'endSection',
            'isDisplay'
          )
        )
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let old_pid = this.model[this.pidField]
          let formData = Object.assign(this.model, values)
          let new_pid = this.model[this.pidField]
          if (this.model.id && this.model.id === new_pid) {
            that.$message.warning('父级节点不能选择自己')
            that.confirmLoading = false
            return
          }
          console.log('表单提交数据', formData)
          let tmpFormData = JSON.parse(JSON.stringify(formData))
          delete tmpFormData.isShowParent
          console.log('提交表单咯', tmpFormData)
          tmpFormData.applicableProcess = this.applicableProcess //this.findCodeById(tmpFormData.applicableProcess)
          console.log('提交表单查找结果', tmpFormData)
          httpAction(httpurl, tmpFormData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                this.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
              that.close()
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'parentCode',
          'applicableProcess',
          'specificationModel',
          'point',
          'name',
          'type',
          'route',
          'parentName',
          'code',
          'name',
          'fanSection',
          'startSection',
          'endSection',
          'isDisplay'
        )
      )
    },
    submitSuccess(formData, flag) {
      if (!formData.id) {
        let treeData = this.$refs.treeSelect.getCurrTreeData()
        this.expandedRowKeys = []
        this.getExpandKeysByPid(formData[this.pidField], treeData, treeData)
        this.$emit('ok', formData, this.expandedRowKeys.reverse())
      } else {
        this.$emit('ok', formData, flag)
      }
    },
    getExpandKeysByPid(pid, arr, all) {
      if (pid && arr && arr.length > 0) {
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].key == pid) {
            this.expandedRowKeys.push(arr[i].key)
            this.getExpandKeysByPid(arr[i]['parentId'], all, all)
          } else {
            this.getExpandKeysByPid(pid, arr[i].children, all)
          }
        }
      }
    },
  },
}
</script>
