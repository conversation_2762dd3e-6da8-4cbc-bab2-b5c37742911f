<template>
  <div id='demo-page'>
    <table-layout
      ref='tableLayout'
      :rightTitle='rightTitle'
      :tree-props='treeProps'
      :search-props='searchProps'
      :table-props='tableProps'
      @tree-init='onTreeInit'
      @tree-select='onTreeSelect'
      @search-submit='onSearchSubmit'
      @table-change='onTableChange'
      @table-expand='handleExpand'
    >
    </table-layout>
    <ProjectPbsModal ref='modalForm' @ok='modalFormOk'></ProjectPbsModal>
  </div>
</template>

<script>
import TableLayout from '@/components/tableLayout/TableLayout.vue'

import { getAction, postAction, deleteAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin' //
import { filterMultiDictText, getDictItems } from '@/components/dict/JDictSelectUtil'
import ProjectPbsModal from './components/Modal.vue'
import { filterObj } from '@/utils/util'
import { nextTick } from 'vue'

export default {
  name: 'PBS',
  components: {
    TableLayout,
    ProjectPbsModal
  },
  mixins: [JeecgListMixin],
  data() {
    return {
      dictInfo: {},
      disableMixinCreated: true,
      url: {
        list: '/project/projectPbs/rootList',
        childList: '/project/projectPbs/childList',
        getChildListBatch: '/project/projectPbs/getChildListBatch',
        delete: '/project/projectPbs/delete',
        deleteBatch: '/project/projectPbs/deleteBatch',
        moveUp: 'project/projectPbs/moveUp',
        moveDown: 'project/projectPbs/moveDown'
      },
      hasChildrenField: 'hasChild',
      pidField: 'pid',
      loadParent: false,
      mergeParams: {},

      // 右侧头部
      rightTitle: 'PBS列表',
      // 树组件的props
      treeProps: {
        dictCode: 'B06',
        isSelectFirstChild: true,
        selectedKeys: [],
        isSelectParentNodes: false
      },
      // 搜索组件的props
      searchProps: {
        formModel: {
          name: ''
        },
        formItems: [{ key: 'name', label: 'PBS名称', placeholder: '请输入PBS名称' }]
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: false,
        columns: [
          {
            title: '序号',
            dataIndex: 'path',
            align: 'center',
            width: 55
          },
          {
            title: 'PBS编码',
            align: 'center',
            dataIndex: 'code',
            width: 120
          },
          {
            title: 'PBS名称',
            align: 'center',
            dataIndex: 'name',
            width: 120
          },
          {
            title: '所属标段',
            align: 'center',
            dataIndex: 'fanSection_dictText',
            width: 120
          },
          {
            title: '风机型号',
            align: 'center',
            dataIndex: 'specificationModel',
            customRender: (text, record, index) => {
              if (text in this.dictInfo) {
                return this.dictInfo[text]
              }
            }
          },
          {
            title: '适用工序',
            align: 'center',
            dataIndex: 'applicableProcess_dictText'
          },
          {
            title: '路由',
            align: 'center',
            dataIndex: 'route_dictText'
          }

        ],
        headerButtons: [
          {
            text: '新增',
            handler: this.handleAdd,
            icon: 'plus'
          }
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail
          },
          {
            text: '编辑',
            handler: this.handleEdit
          },
          {
            text: '删除',
            handler: this.handleDelete,
            type: 'danger'
          }

        ],
        expandedRowKeys: [], // 用于控制哪些行展开
        // 勾选相关的 props 将统一放在 tableDataProps 内
        rowSelection: {
          selectedRowKeys: [], // 用于控制哪些行被勾选
          onChange: this.onSelectChange // 这个处理函数应该在 methods 中定义
        }
      }
    }
  },
  mounted() {
    console.error('test-mounted=========>')
  },
  methods: {
    // 右侧列表
    loadData(arg) {
      this.tableProps.loading = true
      let params = this.getQueryParams()
      console.error('loadData-params===>', params)
      getAction(this.url.list, params)
        .then((res) => {
          if (res.success) {
            let result = res.result.records
            //此处根据type字段的类型 如果是风机,显示风机规格, 如果是海缆 显示海缆型号,集电海缆路由
            if (result && result.length > 0) {
              this.tableProps.dataSource = this.getDataByResult(res.result.records)
              this.calcPath(this.tableProps.dataSource)
              return this.loadDataByExpandedRows(this.tableProps.dataSource)
            } else {
              // this.tableProps.ipagination.total = 0;
              this.tableProps.dataSource = []
            }
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.tableProps.loading = false
        })
    },
    // 设置序号
    calcPath(data, parentPath = '') {
      data.forEach((node, index) => {
        const currentPath = parentPath === '' ? String(index + 1) : `${parentPath}-${index + 1}`
        node.path = currentPath
        if (node.children && node.children.length > 0) {
          this.calcPath(node.children, currentPath)
        }
      })
    },
    // 根据已展开的行查询数据（用于保存后刷新时异步加载子级的数据）
    loadDataByExpandedRows(dataList) {
      if (this.tableProps.expandedRowKeys.length > 0) {
        return getAction(this.url.getChildListBatch, { parentIds: this.tableProps.expandedRowKeys.join(',') }).then(
          (res) => {
            console.log('待查询子级的id数组', this.tableProps.expandedRowKeys)
            if (res.success && res.result.records.length > 0) {
              // 已展开的数据批量子节点
              let records = res.result.records
              const listMap = new Map()
              for (let item of records) {
                let pid = item[this.pidField]
                if (this.tableProps.expandedRowKeys.join(',').includes(pid)) {
                  let mapList = listMap.get(pid)
                  if (mapList == null) {
                    mapList = []
                  }
                  mapList.push(item)
                  listMap.set(pid, mapList)
                }
              }
              let childrenMap = listMap
              let fn = (list) => {
                if (list) {
                  list.forEach((data) => {
                    if (this.tableProps.expandedRowKeys.includes(data.id)) {
                      data.children = this.getDataByResult(childrenMap.get(data.id))
                      fn(data.children)
                    }
                  })
                }
              }
              fn(dataList)
              this.calcPath(dataList, '')
            }
          }
        )
      } else {
        return Promise.resolve()
      }
    },

    onSearchReset(params) {
      // 重置
      // this.tableProps.expandedRowKeys = []
      // this.searchProps.formModel = {}
      console.error('test-onSearchReset', params)
      this.loadData(1)
    },
    getDataByResult(result) {
      if (result) {
        return result.map((item) => {
          // 判断是否标记了带有子节点
          if (item[this.hasChildrenField] == '1') {
            let loadChild = { id: item.id + '_loadChild', name: 'loading...', isLoading: true }
            item.children = [loadChild]
          }
          return item
        })
      }
    },
    handleExpand(expanded, record) {
      console.log('test-handleExpand===>', record)
      // 判断是否是展开状态
      if (expanded) {
        this.tableProps.expandedRowKeys.push(record.id)
        if (record.children.length > 0 && record.children[0].isLoading === true) {
          let params = this.getQueryParams(1) // 查询条件
          params[this.pidField] = record.id
          params.hasQuery = 'false'
          getAction(this.url.childList, params).then((res) => {
            if (res.success) {
              if (res.result) {
                record.children = this.getDataByResult(res.result.records)
                this.tableProps.dataSource = [...this.tableProps.dataSource]
                this.calcPath(this.tableProps.dataSource, '')
              } else {
                record.children = ''
                record.hasChildrenField = '0'
              }
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      } else {
        let keyIndex = this.tableProps.expandedRowKeys.indexOf(record.id)
        if (keyIndex >= 0) {
          this.tableProps.expandedRowKeys.splice(keyIndex, 1)
        }
      }
    },
    handleDetail(record) {
      console.log('test-handleEdit===>', record)
      if (record.pid == null || record.pid == '0') {
        record.isShowParent = 0
      }
      this.$refs.modalForm.title = '查看'
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.disableSubmit = true
    },
    handleEdit(record) {
      console.log('test-handleEdit===>', record)
      if (record.pid == null || record.pid == '0') {
        record.isShowParent = 0
      }
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.disableSubmit = false
    },
    handleAdd() {
      let record = { isShowParent: 0, type: this.mergeParams.selectedKeys[0] }
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.disableSubmit = false
    },
    handleAddChild(record) {
      let obj = {}
      obj['pid'] = record['id']
      obj['parentCode'] = record['code']
      obj['type'] = this.mergeParams.selectedKeys[0]
      obj['parentName'] = record['name']
      obj['isShowParent'] = 1
      this.$refs.modalForm.edit(obj)
      this.$refs.modalForm.disableSubmit = false
      this.$refs.modalForm.title = '添加下级'
    },

    findTarget(data, targetId) {
      if (targetId == '0') {
        return { id: '0', name: '', code: '' }
      }

      for (let j = 0; j < data.length; j++) {
        let item = data[j]
        if (item.id === targetId) {
          return { id: item.id, name: item.name, code: item.code }
        }
        if (item.children) {
          let obj = this.findTarget(item.children, targetId)
          if (obj) {
            return obj
          }
        }
      }
      return null // 如果遍历了所有元素也没有找到，返回 null
    },
    handleAddBrother(record) {
      //判断是否为顶级节点,如果是顶级则调用新增接口
      console.log('新增同级', record.pid)
      this.$refs.modalForm.title = '添加同级'
      let obj = {}
      obj.isShowParent = record.pid === '0' ? 0 : 1
      obj['type'] = this.mergeParams.selectedKeys[0]
      if (record.pid !== '0') {
        let parent = this.findTarget(this.tableProps.dataSource, record.pid)
        obj['pid'] = parent['id']
        obj['parentCode'] = parent['code']
        obj['parentName'] = parent['name']
      }
      console.log('同级数据', obj)
      this.$refs.modalForm.edit(obj)
    },

    handleMoveUp(row) {
      if (!this.url.delete) {
        this.$message.error('请设置url.moveUp!')
        return
      }
      let that = this
      postAction(that.url.moveUp, { id: row.id }).then((res) => {
        if (res.success) {
          that.loadData(1)
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    handleMoveDown(row) {
      let that = this
      postAction(that.url.moveDown, { id: row.id }).then((res) => {
        if (res.success) {
          that.loadData(1)
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    handleDeleteNode(row) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete, { id: row.id }).then((res) => {
        if (res.success) {
          that.loadData(1)
        } else {
          that.$message.warning(res.message)
        }
      })
    },

    // 左侧树初始化
    onTreeInit(params) {
      getDictItems('data_cable_info,cable_type,id').then((resp) => {
        resp.forEach((data) => {
          this.dictInfo[data.value] = data.text
        })
      })

      getDictItems('B10').then((resp) => {
        resp.forEach((data) => {
          this.dictInfo[data.value] = data.text
        })
      })

      getDictItems('data_fan_info,fan_type,id').then((resp) => {
        resp.forEach((data) => {
          this.dictInfo[data.value] = data.text
        })
      })

      console.log('dictinfo', this.dictInfo)
      this.mergeParams = params
      console.error('test-onTreeInit===>', params)
      nextTick(() => {
        this.onSearchSubmit(params)
      })
    },

    onSelectChange(selectedRowKeys, selectedRows) {
      this.tableProps.rowSelection.selectedRowKeys = selectedRowKeys
      // 可能还需要其他处理逻辑...
    },

    onTreeSelect(params) {
      this.mergeParams = params
      console.error('test-onTreeSelect', params)
      if (!params.selectedKeys) {
        return
      }
      let type = params.selectedKeys[0]
      console.log('type', type)
      if (type == 'B06A01A01') {
        this.tableProps.columns = [
          {
            title: '序号',
            dataIndex: 'path',
            align: 'center',
            width: 55
          },
          {
            title: 'PBS编码',
            align: 'center',
            dataIndex: 'code',
            width: 120
          },
          {
            title: 'PBS名称',
            align: 'center',
            dataIndex: 'name',
            width: 120
          },
          {
            title: '所属标段',
            align: 'center',
            dataIndex: 'fanSection_dictText',
            width: 120
          },
          {
            title: '风机型号',
            align: 'center',
            dataIndex: 'specificationModel',
            customRender: (text, record, index) => {
              if (text in this.dictInfo) {
                return this.dictInfo[text]
              }
            }
          },
          {
            title: '适用工序',
            align: 'center',
            dataIndex: 'applicableProcess_dictText'
          },
          {
            title: '路由',
            align: 'center',
            dataIndex: 'route_dictText'
          }
        ]
      } else if (type == 'B06A01A02') {
        this.tableProps.columns = [
          {
            title: '序号',
            dataIndex: 'path',
            align: 'center',
            width: 55
          },
          {
            title: 'PBS编码',
            align: 'center',
            dataIndex: 'code',
            width: 120
          },
          {
            title: 'PBS名称',
            align: 'center',
            dataIndex: 'name',
            width: 120
          },
          {
            title: '所属标段',
            align: 'center',
            dataIndex: 'fanSection_dictText',
            width: 120
          },
          {
            title: '海缆型号',
            align: 'center',
            dataIndex: 'specificationModel',
            customRender: (text, record, index) => {
              if (text in this.dictInfo) {
                return this.dictInfo[text]
              }
            }
          },
          {
            title: '适用工序',
            align: 'center',
            dataIndex: 'applicableProcess_dictText'
          },
          {
            title: '路由',
            align: 'center',
            dataIndex: 'route_dictText'
          }
        ]
      } else {
        this.tableProps.columns = [
          {
            title: '序号',
            dataIndex: 'path',
            align: 'center',
            width: 55
          },
          {
            title: 'PBS编码',
            align: 'center',
            dataIndex: 'code',
            width: 120
          },
          {
            title: 'PBS名称',
            align: 'center',
            dataIndex: 'name',
          },
        ]
      }
      this.onSearchSubmit(params)

      // 树形结构选择响应事件
    },
    onTableChange(params) {
      console.error('test-onTableChange', params)
      this.onSearchSubmit(params)
    },

    // onBeforeSearch(params, { resolve, reject }) {
    //   console.error('test-handleBeforeSelect-params==========>', params)
    //   // 在这里实现检查逻辑，例如权限验证，可以是异步的
    //   if (true) {
    //     resolve();
    //   } else {
    //     reject(new Error("Check not permitted"));
    //   }
    // },
    onSearchSubmit(params) {
      console.log('test-onSearchSubmit', params)
      this.loadData(params)
      // if (this.$refs.tableLayout.$listeners['before-search']) {
      //   // 手动触发before-search事件
      //   this.$refs.tableLayout.beforeSearch(params).then(() => {
      //     this.loadData()
      //   }).catch((error) => {
      //     console.error("was not allowed", error);
      //   });
      // } else {
      //   // 直接进行搜索
      //   this.loadData()
      // }
    },
    getQueryParams(arg) {
      // 获取查询条件
      let param = {}
      if (arg) {
        param = Object.assign({}, {}, this.filters, {
          type: this.mergeParams.selectedKeys && this.mergeParams.selectedKeys[0]
        })
      } else {
        param = Object.assign({}, this.mergeParams.formModel, {}, this.filters, {
          type: this.mergeParams.selectedKeys && this.mergeParams.selectedKeys[0]
        })
      }
      param.hasQuery = 'false'
      // param.field = this.getQueryField();
      return filterObj(param)
    },

    handleMergeParams() {
      let that = this
      const params = {
        ...that.internalFormModel,
        pageNo: that.internalPagination.current,
        pageSize: that.internalPagination.pageSize
      }
      if (that.internalSelectedKeys && that.internalSelectedKeys.length > 0) {
        Object.assign(params, { [this.treeProps.treeField || 'treeId']: that.internalSelectedKeys.join() })
      }
      return params
    }
  }
}
</script>

<style scoped>
#demo-page {
  /* 页面样式 */
  height: 100%;
}
</style>
