<template>
  <div class="list-page project-unit-list">
    <table-layout
      :search-props="searchProps"
      :table-props="tableProps"
      :rightTitle="rightTitle"
      @search-submit="handleSearchSubmit"
      @table-change="loadData"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import extension from './extension'

export default {
  name: 'ProjectUnitList',
  mixins: [JeecgTreeListMixin, extension],
  components: {
    Modal,
  },
  data() {
    return {
      /* 排序参数 */
      isorter: {
        column: '',
        order: '',
      },
      // 搜索组件的props
      rightTitle: '单位列表',
      searchProps: {
        formModel: {
          name: null,
          unitType: null,
        },
        formItems: [
          { key: 'name', label: '单位名称' },
          { key: 'unitType', label: '单位类型', classType: 'list', dictCode: 'unit_type' },
        ],
      },
      // 表格组件的props
      tableProps: {
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },

        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '单位名称',
            align: 'center',
            dataIndex: 'name',
          },
          {
            title: '单位类型',
            align: 'center',
            dataIndex: 'unitType_dictText',
          },
          {
            title: '单位简称',
            align: 'center',
            dataIndex: 'abbreviation',
          },
          {
            title: '联系人',
            align: 'center',
            dataIndex: 'contact',
          },
          {
            title: '联系方式',
            align: 'center',
            dataIndex: 'phone',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 175,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],
      },

      url: {
        list: '/project/projectUnit/list',
        delete: '/project/projectUnit/delete',
        deleteBatch: '/project/projectUnit/deleteBatch',
        exportXlsUrl: '/project/projectUnit/exportXls',
        importExcelUrl: 'project/projectUnit/importExcel',
      },
      searchData: {},
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    handleTreeSelect({ selectedKeys, nodeData }) {
      // 处理树节点选择事件
      console.log('handleTreeSelect', selectedKeys, nodeData)
      let params = {}
      params[this.tableProps.treeField] = selectedKeys.join(',')
      this.loadData(params)
    },
    handleSearchSubmit(searchData) {
      this.searchData = searchData
      this.loadData(searchData.params)
    },

    modalFormOk() {
      this.loadData(this.searchData.params)
    },
  },
}
</script>
