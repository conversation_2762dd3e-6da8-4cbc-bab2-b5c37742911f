<template>
  <div class="list-page project-equipment-list">
    <table-layout
      :tree-props="treeProps"
      :search-props="searchProps"
      :rightTitle="rightTitle"
      :table-props="tableProps"
      @init-params="initParams"
      @tree-init="searchQuery"
      @tree-select="searchQuery"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'

export default {
  name: 'ProjectEquipmentList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {
      disableMixinCreated: true, // 带树的需要树请求后自己执行表格查询
      rightTitle: '设备列表',
      // 树组件的props
      treeProps: {
        dictCode: 'B09',
        isSelectParentNodes: false, // 父节点不可以被选中
      },
      // 搜索组件的props
      searchProps: {
        formModel: {
          name: null,
        },
        formItems: [{ key: 'name', label: '设备名称' }],
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '设备名称',
            align: 'center',
            dataIndex: 'name',
          },
          {
            title: '设备型号',
            align: 'center',
            dataIndex: 'modelNumber',
          },
          {
            title: '规格',
            align: 'center',
            dataIndex: 'specification',
          },
          {
            title: '厂家名称',
            align: 'center',
            dataIndex: 'factoryName',
          },
          {
            title: '联系人',
            align: 'center',
            dataIndex: 'factoryContact',
          },
          {
            title: '联系电话',
            align: 'center',
            dataIndex: 'factoryPhone',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 175,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],
      },

      url: {
        list: '/project/projectEquipment/list',
        delete: '/project/projectEquipment/delete',
        deleteBatch: '/project/projectEquipment/deleteBatch',
        exportXlsUrl: '/project/projectEquipment/exportXls',
        importExcelUrl: 'project/projectEquipment/importExcel',
      },
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
      this.queryParam.type = mergeParams.params.treeId
    },
    handleAdd: function () {
      this.handleEdit({})
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.disableSubmit = false
    },
    handleEdit(record) {
      record['type'] = this.queryParam.type
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.disableSubmit = false
    },
  },
}
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>
