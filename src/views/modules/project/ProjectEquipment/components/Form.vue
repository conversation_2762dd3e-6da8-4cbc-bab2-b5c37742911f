<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="设备编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['code', validatorRules.code]"
                placeholder="请输入设备编码"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['name', validatorRules.name]"
                placeholder="请输入名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备型号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['modelNumber', validatorRules.modelNumber]"
                placeholder="请输入设备型号"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备规格" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['specification', validatorRules.specification]"
                placeholder="请输入规格"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="图片上传" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-image-upload isMultiple v-decorator="['file']"></j-image-upload>
            </a-form-item>
          </a-col>

      </a-row>
      <a-row>
          <div class="line"> 厂家信息 </div>

          <a-col :span="12">
            <a-form-item label="厂家名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['factoryName', validatorRules.factoryName]"
                placeholder="请输入厂家名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="厂家地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['factoryAddress', validatorRules.factoryAddress]"
                placeholder="请输入厂家地址"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['factoryContact', validatorRules.factoryContact]"
                placeholder="请输入厂家联系人"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['factoryPhone', validatorRules.factoryPhone]"
                placeholder="请输入厂家电话"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'ProjectEquipmentForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        code: {
          rules: [
            { required: true, message: '请输入设备编码!' },
            { max: 50, message: '设备编码不能超过50个字' },
          ],
        },
        specification: {
          rules: [{ max: 20, message: '设备规格不能超过20个字' }],
        },
        modelNumber: {
          rules: [{ max: 20, message: '设备型号不能超过20个字' }],
        },
        factoryName: {
          rules: [{ max: 50, message: '厂家名称不能超过50个字' }],
        },
        factoryContact: {
          rules: [{ max: 10, message: '厂家联系人不能超过10个字' }],
        },
        factoryAddress: {
          rules: [{ max: 50, message: '厂家地址不能超过50个字' }],
        },
        factoryPhone: {
          rules: [{ max: 11, message: '厂家电话不能超过11个字' }],
        },
        type: {
          rules: [{ required: true, message: '请输入设备类型!' }],
        },
        name: {
          rules: [
            { required: true, message: '请输入名称!' },
            { max: 20, message: '名称不能超过20个字' },
          ],
        },
      },
      url: {
        add: '/project/projectEquipment/add',
        edit: '/project/projectEquipment/edit',
        queryById: '/project/projectEquipment/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      console.log('edit========', record)
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'code',
            'type',
            'name',
            'modelNumber',
            'specification',
            'file',
            'factoryName',
            'factoryAddress',
            'factoryContact',
            'factoryPhone'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'code',
          'type',
          'name',
          'modelNumber',
          'specification',
          'file',
          'factoryName',
          'factoryAddress',
          'factoryContact',
          'factoryPhone'
        )
      )
    },
    handleCategoryChange(value, backObj) {
      this.form.setFieldsValue(backObj)
    },
  },
}
</script>
<style lang="scss" scoped>
.line {
  font-size: 16px;
  color: #212121;
  padding-bottom: 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid #e5e6eb;
}
</style>
