<template>
  <div>
    <CardLayoutNew title="项目信息" logo="https://gw.alipayobjects.com/zos/rmsportal/nxkuOJlFJuAUhzlMTCEe.png">
      <template slot="action">
        <a-button v-if="!isEmpty" type="primary" @click="handleAdd">添加</a-button>
        <a-button v-else type="primary" @click="handleEdit(itemDetail)">编辑</a-button>
      </template>

      <div slot="headerContent">
        <div class="content-main">
          <div class="main" v-html="itemDetail.remark"></div>
        </div>
      </div>

      <template slot="content">
        <CardNew title="基础信息">
          <detail-list>
            <detail-list-item term="项目名称">
              {{ itemDetail.name }}
            </detail-list-item>
            <detail-list-item term="项目编码">
              {{ itemDetail.code }}
            </detail-list-item>
            <detail-list-item term="业主单位">
              {{ itemDetail.ownerUnits }}
            </detail-list-item>
            <detail-list-item term="设计单位">
              {{ itemDetail.designUnits }}
            </detail-list-item>
            <detail-list-item term="监理单位">
              {{ itemDetail.supervisorUnits }}
            </detail-list-item>
            <detail-list-item term="总承包单位">
              {{ itemDetail.contractUnits }}
            </detail-list-item>
          </detail-list>
        </CardNew>
        <CardNew title="时间计划">
          <detail-list>
            <detail-list-item term="计划开工日期">
              {{ itemDetail.startDate }}
            </detail-list-item>
            <detail-list-item term="计划竣工日期">
              {{ itemDetail.endDate }}
            </detail-list-item>
          </detail-list>
          <detail-list>
            <detail-list-item term="实际开工日期">
              {{ itemDetail.actualStartDate }}
            </detail-list-item>
            <detail-list-item term="实际竣工日期">
              {{ itemDetail.actualEndDate }}
            </detail-list-item>
          </detail-list>
        </CardNew>
        <CardNew title="位置信息">
          <detail-list>
            <detail-list-item term="项目地址">
              {{ itemDetail.projectAddress }}
            </detail-list-item>
            <detail-list-item term="项目位置坐标">
              {{ itemDetail.point }}
            </detail-list-item>
            <detail-list-item term="风场控制区域">
              {{ itemDetail.siteRange }}
            </detail-list-item>
          </detail-list>
        </CardNew>
        <CardNew title="安装信息">
          <detail-list>
            <detail-list-item term="风机数量">
              {{ itemDetail.totalFun }}
            </detail-list-item>
            <detail-list-item term="装机容量">
              {{ itemDetail.capacity }}
            </detail-list-item>
            <detail-list-item term="AIS定位区域">
              {{ itemDetail.shipApiRegion }}
            </detail-list-item>
          </detail-list>
        </CardNew>
      </template>
    </CardLayoutNew>

    <CardLayoutNew
      title="宣传资料"
      logo="https://gw.alipayobjects.com/zos/rmsportal/nxkuOJlFJuAUhzlMTCEe.png"
      style="margin-top: 8px"
    >
      <div slot="headerContent" class="upload">
        <div class="upload-content">
          <div class="content-item">
            <div class="title">
              <img src="@/assets/custom/image-icon.svg" class="image" />
              <span class="text">图片资料：</span>
            </div>
            <j-upload text="添加附件" :number="3" :fileType="'image'" v-model="fileList1" size="'mine'" disabled>
              <template slot="icon">
                <a-icon type="plus-circle" theme="filled" :style="{ fontSize: '24px', color: '#0096ff' }" />
              </template>
            </j-upload>
          </div>

          <div class="content-item">
            <div class="title">
              <img src="@/assets/custom/video-icon.svg" class="image" />
              <span class="text">宣传视频：</span>
            </div>
            <j-video-upload
              text="上传"
              v-model="fileList2"
              :isMultiple="true"
              :isView="true"
              limit="1"
              disabled
            ></j-video-upload>
          </div>
          <div class="content-item">
            <div class="title">
              <img src="@/assets/custom/video-icon.svg" class="image" />
              <span class="text">工艺视频：</span>
            </div>
            <j-video-upload
              text="上传"
              v-model="processVideoList"
              :isMultiple="true"
              :isView="true"
              limit="1"
              disabled
            ></j-video-upload>
          </div>
        </div>
      </div>
    </CardLayoutNew>

    <project-info-modal ref="modalForm" @ok="modalFormOk"></project-info-modal>
  </div>
</template>

<script>
import CardLayoutNew from '@/components/page/CardLayoutNew'
import CardNew from '@/components/page/CardNew'
import DetailList from '@/components/tools/DetailList'
import { getAction } from '@/api/manage'

// import { mixinDevice } from '@/mixins/DeviceMixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ProjectInfoModal from './components/Modal'
const DetailListItem = DetailList.Item

export default {
  name: 'ProjectInfoList',
  mixins: [JeecgListMixin],
  components: {
    CardLayoutNew,
    CardNew,
    DetailList,
    DetailListItem,
    ProjectInfoModal,
  },
  data() {
    return {
      fileList1: [],
      fileList2: [],
      processVideoList: [],
      itemDetail: {},
      columns: [],
      url: {
        list: '/project/projectInfo/list',
      },
    }
  },
  created() {
    this.handleGetItem()
  },
  computed: {
    isEmpty() {
      return Object.keys(this.itemDetail).length
    },
  },
  methods: {
    modalFormOk() {
      this.handleGetItem()
    },
    handleGetItem() {
      getAction(this.url.list)
        .then((res) => {
          if (res.success) {
            let result = res.result
            if (Number(result.total) > 0) {
              this.itemDetail = result.records[0] || {}
              console.error('handleGetItem-this.itemDetail===>', this.itemDetail)
              this.fileList1 = this.itemDetail.images || []
              this.fileList2 = this.itemDetail.video || []
              this.processVideoList = this.itemDetail.processVideo || []
            }
          } else {
            this.$message.warning(res.message)
          }
        })
        .catch(() => {
          this.$message.error('请求失败')
        })
    },
  },
}
</script>

<style lang="less" scoped>
.content-main {
  padding: 16px 24px;
  background: #e9e9f0;
  position: relative;
  overflow: hidden;
  .main {
    position: relative;
    overflow: auto;
    background: #e9e9f0;
    font-size: 16px;
    color: #1d2129;
    line-height: 24px;
    width: 100%;
    height: 100%;
  }
}
.upload {
  margin-left: 40px;
  .upload-content {
  }
  .title {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 16px;
    color: #4e5969;
  }
  .image {
    width: 22px;
    height: 22px;
    margin-right: 6px;
  }
}
</style>
