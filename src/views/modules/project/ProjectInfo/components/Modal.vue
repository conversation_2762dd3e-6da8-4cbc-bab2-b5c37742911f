<template>
    <j-modal
      :title="title"
      :width="width"
      :visible="visible"
      :maskClosable="false"

      switchFullscreen
      @ok="handleOk"
      :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
      @cancel="handleCancel"
      cancelText="关闭">
      <project-info-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></project-info-form>
    </j-modal>
  </template>
  
  <script>
  
    import ProjectInfoForm from './Form.vue'
    export default {
      name: 'ProjectInfoModal',
      components: {
        ProjectInfoForm
      },
      data () {
        return {
          title: '',
          width: 915,
          visible: false,
          disableSubmit: false
        }
      },
      methods: {
        add () {
          this.visible = true
          this.$nextTick(() => {
            this.$refs.realForm.add();
          })
        },
        edit (record) {
          this.visible = true
          this.$nextTick(() => {
            this.$refs.realForm.edit(record);
          })
        },
        close () {
          this.$emit('close');
          this.visible = false;
        },
        handleOk () {
          this.$refs.realForm.submitForm();
        },
        submitCallback() {
          this.$emit('ok');
          this.visible = false;
        },
        handleCancel () {
          this.close()
        }
      }
    }
  </script>
  