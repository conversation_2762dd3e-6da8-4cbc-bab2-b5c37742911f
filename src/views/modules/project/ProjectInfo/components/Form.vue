<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <CardLayoutNew title="项目信息" logo="https://gw.alipayobjects.com/zos/rmsportal/nxkuOJlFJuAUhzlMTCEe.png">
            <div slot="headerContent" class="form-editer">
              <a-col>
                <a-form-item label="" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <j-editor v-decorator="['remark', {}]" triggerChange placeholder="请输入简介" />
                </a-form-item>
              </a-col>
            </div>
            <template slot="content">
              <CardNew title="基础信息">
                <a-col :span="12">
                  <a-form-item label="项目名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input v-decorator="['name', validatorRules.name]" placeholder="请输入项目名称"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="项目编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input v-decorator="['code']" placeholder="请输入项目编码"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="业主单位 " :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input
                      v-decorator="['ownerUnits', validatorRules.ownerUnits]"
                      placeholder="请输入业主单位 "
                    ></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="设计单位 " :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input v-decorator="['designUnits']" placeholder="请输入设计单位 "></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="监理单位 " :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input v-decorator="['supervisorUnits']" placeholder="请输入监理单位 "></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="总承包单位 " :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input
                      v-decorator="['contractUnits', validatorRules.contractUnits]"
                      placeholder="请输入总承包单位 "
                    ></a-input>
                  </a-form-item>
                </a-col>
              </CardNew>
              <CardNew title="时间计划">
                <a-col :span="12">
                  <a-form-item label="计划开工日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <j-date
                      placeholder="请选择计划开工日期"
                      v-decorator="['startDate', validatorRules.startDate]"
                      :trigger-change="true"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="实际开工日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <j-date
                      placeholder="请选择实际开工日期"
                      v-decorator="['actualStartDate']"
                      :trigger-change="true"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="计划竣工日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <j-date
                      placeholder="请选择计划竣工日期"
                      v-decorator="['endDate', validatorRules.endDate]"
                      :trigger-change="true"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="实际竣工日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <j-date
                      placeholder="请选择实际竣工日期"
                      v-decorator="['actualEndDate']"
                      :trigger-change="true"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </CardNew>
              <CardNew title="位置信息">
                <a-col :span="12">
                  <a-form-item label="项目地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input v-decorator="['projectAddress']" placeholder="请输入项目地址"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="项目位置坐标" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input v-decorator="['point']" placeholder="请输入项目位置坐标"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item
                    label="风场控制范围"
                    :labelCol="{
                      xs: { span: 24 },
                      sm: { span: 3 },
                    }"
                    :wrapperCol="{ xs: { span: 24 }, sm: { span: 20 } }"
                  >
                    <a-input v-decorator="['siteRange']" placeholder="请输入风场控制范围"></a-input>
                  </a-form-item>
                </a-col>
              </CardNew>
              <CardNew title="安装信息">
                <a-col :span="12">
                  <a-form-item label="风机数量" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input-number
                      v-decorator="['totalFun', validatorRules.totalFun]"
                      placeholder="请输入风机数量"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="装机容量" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input-number
                      v-decorator="['capacity', validatorRules.capacity]"
                      placeholder="请输入装机容量"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="AIS定位区域" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input v-decorator="['shipApiRegion']" placeholder="请输入AIS定位区域"></a-input>
                  </a-form-item>
                </a-col>
              </CardNew>
            </template>
          </CardLayoutNew>

          <CardLayoutNew
            title="宣传资料"
            logo="https://gw.alipayobjects.com/zos/rmsportal/nxkuOJlFJuAUhzlMTCEe.png"
            style="margin-top: 8px"
          >
            <div slot="headerContent" class="upload">
              <div class="upload-content">
                <div class="content-item">
                  <div class="title">
                    <img src="@/assets/custom/image-icon.svg" class="image" />
                    <span class="text">图片资料：</span>
                  </div>
                  <!-- <j-image-upload text="上传" v-model="fileList1" :isMultiple="true" limit="10"></j-image-upload> -->
                  <j-upload text="添加附件" :number="3" :fileType="'image'" v-model="fileList1" size="'mine'">
                    <template slot="icon">
                      <a-icon type="plus-circle" theme="filled" :style="{ fontSize: '24px', color: '#0096ff' }" />
                    </template>
                  </j-upload>
                </div>

                <div class="content-item">
                  <div class="title">
                    <img src="@/assets/custom/video-icon.svg" class="image" />
                    <span class="text">宣传视频：</span>
                  </div>
                  <div class="content">
                    <span>视频简介</span>
                    <a-textarea
                      v-model="model.videoDescription"
                      :maxLength="500"
                      placeholder="请输入"
                      :auto-size="{ minRows: 4, maxRows: 5 }"
                    />
                    <j-video-upload text="上传" v-model="fileList2" :isMultiple="true" limit="1"></j-video-upload>
                  </div>
                </div>
                <div class="content-item">
                  <div class="title">
                    <img src="@/assets/custom/video-icon.svg" class="image" />
                    <span class="text">工艺视频：</span>
                  </div>
                  <div class="content">
                    <span>视频简介</span>
                    <a-textarea
                      v-model="model.processVideoDescription"
                      :maxLength="500"
                      placeholder="请输入"
                      :auto-size="{ minRows: 4, maxRows: 5 }"
                    />
                    <j-video-upload
                      text="上传"
                      v-model="processVideoList"
                      :isMultiple="true"
                      limit="1"
                    ></j-video-upload>
                  </div>
                </div>
              </div>
            </div>
          </CardLayoutNew>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import CardLayoutNew from '@/components/page/CardLayoutNew'
import CardNew from '@/components/page/CardNew'
import JEditor from '@/components/jeecg/JEditor'

import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'

export default {
  name: 'ProjectInfoForm',
  components: {
    CardLayoutNew,
    CardNew,
    JEditor,
  },
  props: {
    // 流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    // 表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    // 表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      fileList1: [],
      fileList2: [],
      processVideoList: [],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        name: {
          rules: [{ required: true, message: '请输入项目名称!' }],
        },
        ownerUnits: {
          rules: [{ required: true, message: '请输入业主单位 !' }],
        },
        contractUnits: {
          rules: [{ required: true, message: '请输入总承包单位 !' }],
        },
        startDate: {
          rules: [{ required: true, message: '请输入计划开工时间!' }],
        },
        endDate: {
          rules: [{ required: true, message: '请输入计划竣工时间!' }],
        },
        totalFun: {
          rules: [{ required: true, message: '请输入风机数量!' }],
        },
        capacity: {
          rules: [{ required: true, message: '请输入装机容量!' }],
        },
      },
      url: {
        add: '/project/projectInfo/add',
        edit: '/project/projectInfo/edit',
        queryById: '/project/projectInfo/queryById',
      },
    }
  },
  computed: {
    publicPath() {
      return window.location.origin + process.env.BASE_URL
    },
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    // 如果是流程中表单，则需要加载流程表单data
    this.showData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      console.log('edit===>', record)
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'code',
            'name',
            'ownerUnits',
            'designUnits',
            'supervisorUnits',
            'contractUnits',
            'startDate',
            'actualStartDate',
            'siteRange',
            'endDate',
            'actualEndDate',
            'projectAddress',
            'point',
            'totalFun',
            'capacity',
            'shipApiRegion',
            'remark',
            'images',
            'video',
            'processVideo'
          )
        )
        this.fileList1 = this.model.images
        this.fileList2 = this.model.video
        this.processVideoList = this.model.processVideo
      })
    },
    // 渲染表单数据
    showData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)

          this.fileList1 !== '' ? (formData.images = this.fileList1) : (formData.images = '')
          this.fileList2 !== '' ? (formData.video = this.fileList2) : (formData.video = '')
          this.processVideoList !== '' ? (formData.processVideo = this.processVideoList) : (formData.processVideo = '')
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'code',
          'name',
          'ownerUnits',
          'designUnits',
          'supervisorUnits',
          'contractUnits',
          'startDate',
          'actualStartDate',
          'siteRange',
          'endDate',
          'actualEndDate',
          'projectAddress',
          'point',
          'totalFun',
          'capacity',
          'shipApiRegion',
          'remark',
          'images',
          'video',
          'processVideo'
        )
      )
    },
  },
}
</script>
<style lang="less" scoped>
.form-editer {
  ::v-deep .ant-col-sm-16 {
    width: 100%;
  }
}
.content-item {
  .content {
    display: flex;
    gap: 16px;
    align-items: center;
    > span {
      width: 100px;
    }
  }
}
.upload {
  margin-left: 40px;
  .upload-content {
  }
  .title {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 16px;
    color: #4e5969;
  }
  .image {
    width: 22px;
    height: 22px;
    margin-right: 6px;
  }
}
</style>
