<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    :destroyOnClose="true"
    cancelText="关闭"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
  >
    <a-spin :spinning="confirmLoading">
      <j-form-container :disabled="disableSubmit">
        <a-form :form="form" slot="detail">
          <a-row>
            <a-col :span="12" v-if="isShowParent === 1">
              <a-form-item label="父级WBS编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  disabled
                  v-decorator="['parentCode', validatorRules.parentCode]"
                  placeholder="请输入父级WBS编号"
                  autocomplete="off"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="isShowParent === 1">
              <a-form-item label="父级WBS名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  disabled
                  v-decorator="['parentName', validatorRules.parentName]"
                  placeholder="请输入父级WBS名称"
                  autocomplete="off"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="WBS编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  v-decorator="['code', validatorRules.code]"
                  placeholder="请输入WBS编号"
                  autocomplete="off"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="WBS名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  v-decorator="['name', validatorRules.name]"
                  placeholder="请输入WBS名称"
                  autocomplete="off"
                ></a-input>
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item label="任务类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <j-dict-select-tag
                  type="list"
                  v-decorator="['type', validatorRules.type]"
                  :trigger-change="true"
                  dictCode="WBS_type"
                  placeholder="请选择任务类型"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>
import { httpAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'
import pick from 'lodash.pick'
export default {
  name: 'QzhfProjectWbsModal',
  components: {},
  data() {
    return {
      disableSubmit: false,
      form: this.$form.createForm(this),
      title: '操作',
      width: 800,
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },

      confirmLoading: false,
      validatorRules: {
        parentCode: {
          rules: [{ required: true, message: '请输入父级WBS编号!' }],
        },
        parentName: {
          rules: [{ required: true, message: '请输入父级WBS名称!' }],
        },
        code: {
          rules: [
            { required: true, message: '请输入WBS编号!' },
            { max: 20, message: 'WBS编号不能超过20个字符' },
            {
              validator: (rule, value, callback) =>
                validateDuplicateValue('project_wbs', 'code', value, this.model.id, callback),
            },
          ],
        },
        name: {
          rules: [
            { required: true, message: '请输入WBS名称!' },
            { max: 20, message: 'WBS名称不能超过20个字符' },
          ],
        },
        type: {
          rules: [{ required: true, message: '请选择任务类型!' }],
        },
      },
      url: {
        add: '/project/projectWbs/add',
        edit: '/project/projectWbs/edit',
      },
      expandedRowKeys: [],
      pidField: '',
      isShowParent: 0,
    }
  },
  created() {},
  methods: {
    edit(record) {
      console.log('record', record)
      this.isShowParent = record.isShowParent
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'parentCode', 'parentName', 'code', 'name','type'))
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        console.log('handleOk')
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let old_pid = this.model[this.pidField]
          let formData = Object.assign(this.model, values)
          let new_pid = this.model[this.pidField]
          if (this.model.id && this.model.id === new_pid) {
            that.$message.warning('父级节点不能选择自己')
            that.confirmLoading = false
            return
          }
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                this.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
              that.close()
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'code', 'name','type'))
    },
    submitSuccess(formData, flag) {
      if (!formData.id) {
        let treeData = this.$refs.treeSelect.getCurrTreeData()
        this.expandedRowKeys = []
        this.getExpandKeysByPid(formData[this.pidField], treeData, treeData)
        this.$emit('ok', formData, this.expandedRowKeys.reverse())
      } else {
        this.$emit('ok', formData, flag)
      }
    },
    getExpandKeysByPid(pid, arr, all) {
      if (pid && arr && arr.length > 0) {
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].key == pid) {
            this.expandedRowKeys.push(arr[i].key)
            this.getExpandKeysByPid(arr[i]['parentId'], all, all)
          } else {
            this.getExpandKeysByPid(pid, arr[i].children, all)
          }
        }
      }
    },
  },
}
</script>
