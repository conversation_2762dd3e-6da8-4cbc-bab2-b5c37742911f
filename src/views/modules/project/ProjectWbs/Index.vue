<template>
  <div style="height: 100%">
    <table-layout
      ref="tableLayout"
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @search-submit="handleSearchSubmit"
      @table-expand="handleExpand"
    >
    </table-layout>
    <ProjectWbsModal ref="modalForm" @ok="modalFormOk"></ProjectWbsModal>
  </div>
</template>

<script>
import TableLayout from '@/components/tableLayout/TableLayout.vue'
import { getAction, postAction, deleteAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import ProjectWbsModal from './components/Modal'
import { filterObj } from '@/utils/util'

export default {
  name: 'ProjectWbsList',
  mixins: [JeecgListMixin],
  components: {
    TableLayout,
    ProjectWbsModal,
  },
  data() {
    return {
      rightTitle: 'WBS列表',
      mergeParams: {},
      searchData: {},
      searchProps: {
        formModel: {
          name: '',
        },
        formItems: [{ key: 'name', label: 'WBS名称', placeholder: '请输入WBS名称' }],
      },
      tableProps: {
        // 表头
        ipagination: false,
        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: 'path',
          },
          {
            title: 'WBS编码',
            align: 'center',
            dataIndex: 'code',
          },
          {
            title: 'WBS名称',
            align: 'center',
            dataIndex: 'name',
          },
          {
            title: '任务类型',
            align: 'center',
            dataIndex: 'type_dictText',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 175,
            scopedSlots: { customRender: 'action' },
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],

        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
          },
          {
            text: '上移',
            handler: this.handleMoveUp,
            disabled: (record) => {
              return !record.upEnable
            },
          },
          {
            text: '下移',
            handler: this.handleMoveDown,
            disabled: (record) => {
              return !record.downEnable
            },
          },
          {
            text: '新增下级',
            handler: this.handleAddChild,
          },
          {
            text: '新增同级',
            handler: this.handleAddBrother,
          },
          {
            text: '删除',
            handler: this.handleDelete,
            type: 'danger',
          },
        ],
        expandedRowKeys: [],
      },
      url: {
        list: '/project/projectWbs/rootList',
        childList: '/project/projectWbs/childList',
        getChildListBatch: '/project/projectWbs/getChildListBatch',
        delete: '/project/projectWbs/delete',
        deleteBatch: '/project/projectWbs/deleteBatch',
        exportXlsUrl: '/project/projectWbs/exportXls',
        importExcelUrl: 'project/projectWbs/importExcel',
        moveUp: 'project/projectWbs/moveUp',
        moveDown: 'project/projectWbs/moveDown',
      },

      hasChildrenField: 'hasChild',
      pidField: 'pid',
      dictOptions: {},
      loadParent: false,
      superFieldList: [],
    }
  },
  created() {},
  computed: {
    importExcelUrl() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
    // tableProps() {
    //   let _this = this
    //   return {
    //     // 列表项是否可选择
    //     rowSelection: {
    //       selectedRowKeys: _this.selectedRowKeys,
    //       onChange: (selectedRowKeys) => (_this.selectedRowKeys = selectedRowKeys),
    //     },
    //   }
    // },
  },
  methods: {
    handleDetail(record) {
      console.log('test-handleEdit===>', record)
      if (record.pid == null || record.pid == '0') {
        record.isShowParent = 0
      }
      this.$refs.modalForm.title = '查看'
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.disableSubmit = true
    },
    handleEdit(record) {
      console.log('test-handleEdit===>', record)
      if (record.pid == null || record.pid == '0') {
        record.isShowParent = 0
      }
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.disableSubmit = false
    },
    handleAdd() {
      let record = { isShowParent: 0 }
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.disableSubmit = false
    },
    handleAddBrother(record) {
      //判断是否为顶级节点,如果是顶级则调用新增接口
      console.log('新增同级', record.pid)
      this.$refs.modalForm.title = '添加同级'
      let obj = {}
      obj.isShowParent = record.pid === '0' ? 0 : 1
      if (record.pid !== '0') {
        let parent = this.findTarget(this.tableProps.dataSource, record.pid)
        obj['pid'] = parent['id']
        obj['parentCode'] = parent['code']
        obj['parentName'] = parent['name']
      }
      console.log('同级数据', obj)
      this.$refs.modalForm.edit(obj)
    },

    handleAddChild(record) {
      let obj = {}
      obj['pid'] = record['id']
      obj['parentCode'] = record['code']
      obj['parentName'] = record['name']
      obj['isShowParent'] = 1
      this.$refs.modalForm.edit(obj)
      this.$refs.modalForm.title = '添加下级'
    },

    handleSearchSubmit(searchData) {
      console.log('handleSearchSubmitxxxx', searchData)
      this.mergeParams = searchData
      this.loadData(searchData.params)
    },
    loadData(arg) {
      if (arg == 1) {
        this.ipagination.current = 1
      }
      this.loading = true
      let params = this.getQueryParams()
      params.hasQuery = 'true'
      getAction(this.url.list, params)
        .then((res) => {
          if (res.success) {
            let result = res.result.records
            if (result && result.length) {
              this.tableProps.dataSource = this.getDataByResult(result)
              this.calcPath(this.tableProps.dataSource)
              return this.loadDataByExpandedRows(this.tableProps.dataSource)
            } else {
              this.tableProps.dataSource = []
            }
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 根据已展开的行查询数据（用于保存后刷新时异步加载子级的数据）
    loadDataByExpandedRows(dataList) {
      if (this.tableProps.expandedRowKeys.length > 0) {
        return getAction(this.url.getChildListBatch, { parentIds: this.tableProps.expandedRowKeys.join(',') }).then(
          (res) => {
            console.log('待查询子级的id数组', this.tableProps.expandedRowKeys)
            if (res.success && res.result.records.length > 0) {
              // 已展开的数据批量子节点
              let records = res.result.records
              const listMap = new Map()
              for (let item of records) {
                let pid = item[this.pidField]
                if (this.tableProps.expandedRowKeys.join(',').includes(pid)) {
                  let mapList = listMap.get(pid)
                  if (mapList == null) {
                    mapList = []
                  }
                  mapList.push(item)
                  listMap.set(pid, mapList)
                }
              }
              let childrenMap = listMap
              let fn = (list) => {
                if (list) {
                  list.forEach((data) => {
                    if (this.tableProps.expandedRowKeys.includes(data.id)) {
                      data.children = this.getDataByResult(childrenMap.get(data.id))
                      fn(data.children)
                    }
                  })
                }
              }
              fn(dataList)
              this.calcPath(dataList, '')
            }
          }
        )
      } else {
        return Promise.resolve()
      }
    },
    getQueryParams(arg) {
      // 获取查询条件
      let param = {}
      if (arg) {
        param = Object.assign({}, this.isorter, this.filters)
      } else {
        param = Object.assign({}, this.mergeParams.formModel, this.isorter, this.filters)
      }
      param.hasQuery = 'false'
      // param.field = this.getQueryField();
      return filterObj(param)
    },
    // searchReset() {
    //   //重置
    //   this.expandedRowKeys = []
    //   this.queryParam = {}
    //   this.loadData(1)
    // },
    getDataByResult(result) {
      if (result) {
        return result.map((item) => {
          // 判断是否标记了带有子节点
          if (item[this.hasChildrenField] == '1') {
            let loadChild = { id: item.id + '_loadChild', name: 'loading...', isLoading: true }
            item.children = [loadChild]
          }
          return item
        })
      }
    },
    handleExpand(expanded, record) {
      console.log('test-handleExpand===>', record)
      // 判断是否是展开状态
      if (expanded) {
        this.tableProps.expandedRowKeys.push(record.id)
        if (record.children.length > 0 && record.children[0].isLoading === true) {
          let params = this.getQueryParams(1) // 查询条件
          params[this.pidField] = record.id
          params.hasQuery = 'false'
          getAction(this.url.childList, params).then((res) => {
            if (res.success) {
              if (res.result.records && res.result.records.length > 0) {
                record.children = this.getDataByResult(res.result.records)
                this.tableProps.dataSource = [...this.tableProps.dataSource]
                this.calcPath(this.tableProps.dataSource, '')
              } else {
                record.children = ''
                record.hasChildrenField = '0'
              }
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      } else {
        let keyIndex = this.tableProps.expandedRowKeys.indexOf(record.id)
        if (keyIndex >= 0) {
          this.tableProps.expandedRowKeys.splice(keyIndex, 1)
        }
      }
    },
    handleDeleteNode(id) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      deleteAction(that.url.delete, { id: id }).then((res) => {
        if (res.success) {
          that.loadData(1)
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    batchDel() {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return false
      } else {
        let ids = ''
        let that = this
        that.selectedRowKeys.forEach(function (val) {
          ids += val + ','
        })
        that.$confirm({
          title: '确认删除',
          content: '是否删除选中数据?',
          onOk: function () {
            that.handleDeleteNode(ids)
            that.onClearSelected()
          },
        })
      }
    },
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'code', text: 'WBS编码', dictCode: '' })
      fieldList.push({ type: 'string', value: 'name', text: 'Wbs名称', dictCode: '' })
      fieldList.push({ type: 'int', value: 'type', text: 'Wbs类型（）关联分类字典', dictCode: 'wbs_type' })
      this.superFieldList = fieldList
    },
    calcPath(data, parentPath = '') {
      data.forEach((node, index) => {
        const currentPath = parentPath === '' ? String(index + 1) : `${parentPath}-${index + 1}`
        node.path = currentPath
        if (node.children && node.children.length > 0) {
          this.calcPath(node.children, currentPath)
        }
      })
    },
    findTarget(data, targetId) {
      if (targetId == '0') {
        return { id: '0', name: '', code: '' }
      }

      for (let j = 0; j < data.length; j++) {
        let item = data[j]
        if (item.id === targetId) {
          return { id: item.id, name: item.name, code: item.code }
        }
        if (item.children) {
          let obj = this.findTarget(item.children, targetId)
          if (obj) {
            return obj
          }
        }
      }
      return null // 如果遍历了所有元素也没有找到，返回 null
    },

    handleMoveUp(row) {
      if (!this.url.delete) {
        this.$message.error('请设置url.moveUp!')
        return
      }
      let that = this
      postAction(that.url.moveUp, { id: row.id }).then((res) => {
        if (res.success) {
          that.loadData(1)
        } else {
          that.$message.warning(res.message)
        }
      })
    },
    handleMoveDown(row) {
      let that = this
      postAction(that.url.moveDown, { id: row.id }).then((res) => {
        if (res.success) {
          that.loadData(1)
        } else {
          that.$message.warning(res.message)
        }
      })
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
