<template>
  <div class="list-page project-fence-list">
    <table-layout
      :search-props="searchProps"
      :table-props="tableProps"
      :rightTitle="rightTitle"
      @search-submit="handleSearchSubmit"
      @table-change="loadData"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
// import extension from './extension'

export default {
  name: 'ProjectFenceList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {
      /* 排序参数 */
      isorter: {
        column: '',
        order: '',
      },
      // 搜索组件的props
      searchProps: {
        formModel: {
          name: null,
          fenceType: null,
        },
        formItems: [
          { key: 'name', label: '围栏名称'},
          { key: 'fenceType', label: '围栏类型', classType: 'list', dictCode: 'fence_type' },
        ],
      },
      // 表格组件的props
      tableProps: {
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },

        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '围栏编码',
            align: 'center',
            dataIndex: 'code',
          },
          {
            title: '围栏名称',
            align: 'center',
            dataIndex: 'name',
          },
          {
            title: '围栏类型',
            align: 'center',
            dataIndex: 'fenceType_dictText',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 175,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],
      },

      url: {
        list: '/project/projectFence/list',
        delete: '/project/projectFence/delete',
        deleteBatch: '/project/projectFence/deleteBatch',
        exportXlsUrl: '/project/projectFence/exportXls',
        importExcelUrl: 'project/projectFence/importExcel',
      },
      searchData: {},
      rightTitle: '电子围栏列表',
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    handleSearchSubmit(searchData) {
      this.searchData = searchData //备份搜索条件
      this.loadData(searchData.params)
    },
    modalFormOk() {
      this.loadData(this.searchData.params)
    },
  },
}
</script>
