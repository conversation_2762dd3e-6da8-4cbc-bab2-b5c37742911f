<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled" class="form-container">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="围栏名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['name', validatorRules.name]"
                placeholder="请输入名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="围栏编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['code', validatorRules.code]"
                placeholder="请输入编码"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="围栏类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['fenceType', validatorRules.fenceType]"
                :trigger-change="true"
                dictCode="fence_type"
                placeholder="请选择围栏类型"
              />
            </a-form-item>
          </a-col>
<!--          <a-col :span="24">-->
<!--            <a-form-item-->
<!--              label="备注"-->
<!--              :labelCol="{-->
<!--                xs: { span: 24 },-->
<!--                sm: { span: 3 },-->
<!--              }"-->
<!--              :wrapperCol="{-->
<!--                xs: { span: 24 },-->
<!--                sm: { span: 20 },-->
<!--              }"-->
<!--            >-->
<!--              <a-textarea-->
<!--                autocomplete="off"-->
<!--                v-decorator="['remark', validatorRules.remark]"-->
<!--                rows="4"-->
<!--                placeholder="请输入备注"-->
<!--              />-->
<!--            </a-form-item>-->
<!--          </a-col>-->

          <a-col :span="24">
            <a-form-item
              label="围栏范围"
              :labelCol="{
                xs: { span: 24 },
                sm: { span: 3 },
              }"
              :wrapperCol="{
                xs: { span: 24 },
                sm: { span: 20 },
              }"
            >
              <a-input
                autocomplete="off"
                v-decorator="['fenceRadius', validatorRules.fenceRadius]"
                placeholder="请输入围栏范围"
              ></a-input>
            </a-form-item>
          </a-col>

          

          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
    <div class="pageMain">
      <Map ref="Map" @initMap="initMapData" :defaultLegend="false" :defaultTypeChange="false"></Map>
    </div>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import Map from '@/components/Map'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'

import MapService from '@/utils/MapService'
import mapData from '../../../../../../public/static/map/mapData.json'

export default {
  name: 'ProjectFenceForm',
  components: { Map },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        code: {
          rules: [
            { required: true, message: '请输入围栏编码!' },
            { max: 20, message: '围栏编码不能超过20个字符' },
            {
              validator: (rule, value, callback) =>
                validateDuplicateValue('project_fence', 'code', value, this.model.id, callback),
            },
          ],
        },
        remark: {
          rules: [{ max: 500, message: '备注不能超过500个字符' }],
        },
        name: {
          rules: [
            { required: true, message: '请输入围栏名称!' },
            { max: 20, message: '围栏名称更不能超过20个字符' },
            {
              validator: (rule, value, callback) =>
                validateDuplicateValue('project_fence', 'name', value, this.model.id, callback),
            },
          ],
        },
        fenceType: {
          rules: [{ required: true, message: '请输入围栏类型!' }],
        },
        fenceRadius: {
          rules: [{ required: true, message: '请输入围栏范围!' }],
        },
      },
      url: {
        add: '/project/projectFence/add',
        edit: '/project/projectFence/edit',
        queryById: '/project/projectFence/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    roadMap() {
      let points = this.isValidJSON(this.model.fenceRadius) ? JSON.parse(this.model.fenceRadius) : []
      const { mapCenter } = mapData
      let mapConfig = {
        mapContainer: this.$refs.mapContainer,
        lng: mapCenter[0],
        lat: mapCenter[1],
        zoom: this.mapDefaultZoom,
      }
      if (points.length > 0) {
        mapConfig.lng = points[0][0]
        mapConfig.lat = points[0][1]
      }
      MapService.initMap(mapConfig)
        .then((map) => {
          this.map = map
          this.setFence({
            map,
            points,
            config: { disabled: this.formDisabled },
            callback: (polygonTool, event) => {
              const lnglats = event.currentLnglats || []
              const fenceRadius = []
              lnglats.forEach((item) => {
                fenceRadius.push([item.lng, item.lat])
              })
              fenceRadius.push([lnglats[0].lng, lnglats[0].lat])
              this.form.setFieldsValue({ fenceRadius: JSON.stringify(fenceRadius) })
            },
          })
        })
        .catch((error) => {
          console.error('地图初始化失败', error)
        })
    },
    /**
     * @description 绘制围栏
     * @param {*} param0
     */
    setFence({ points = [], config = {}, callback }) {
      const polygonConfig = {
        strokeColor: 'blue', // 线颜色
        fillOpacity: 0.1, // 填充的透明度
      }
      let mergeConfig = Object.assign(
        {},
        {
          // 是否可以绘制
          disabled: false,
          // 是否显示起点
          showStartPoint: false,
          // 是否显示每个点的坐标
          showPointCoordinates: false,
          // 是否显示每条线的长度
          showLineLengths: false,
        },
        config
      )

      let signs = [] // 存储所有标签的数组
      // 清除之前的标签和起点标记
      const clearOverlays = () => {
        signs.forEach((label) => this.map.removeOverLay(label))
        signs = []
      }
      // 每次开始绘制前清除之前的标签和多边形
      let polygonInstance
      const startDrawing = () => {
        if (polygonInstance) {
          this.map.removeOverLay(polygonInstance)
          polygonInstance = null
        }
        clearOverlays()
      }
      // 创建起点标记
      const createStartSign = (point) => {
        if (mergeConfig.showStartPoint) {
          // 起点标记样式自定义，例如使用图片
          const startPointMarker = new T.Marker(point)
          this.map.addOverLay(startPointMarker)
          signs.push(startPointMarker)
        }
      }
      // 创建节点坐标标签 和 线段长度标签
      const createLabelsSign = (points) => {
        points.forEach((point, index) => {
          if (index < points.length - 1) {
            if (mergeConfig.showPointCoordinates) {
              const label1 = new T.Label({
                offset: new T.Point(-20, 20), // 偏移量，使标签在标记上方显示
                text: `[${point.lng},${point.lat}]`, // 显示标记的名称
                position: point, // 标签的位置
              })
              this.map.addOverLay(label1)
              signs.push(label1)
            }

            if (mergeConfig.showLineLengths) {
              const nextPoint = points[index + 1]
              const distanceInMeters = map.getDistance(point, nextPoint)
              const distanceInKilometers = (distanceInMeters / 1000).toFixed(2) // 米转公里并保留两位小数
              const midPoint = new T.LngLat((point.lng + nextPoint.lng) / 2, (point.lat + nextPoint.lat) / 2)
              const label2 = new T.Label({
                text: `${distanceInKilometers}千米`,
                position: midPoint,
                offset: new T.Point(-25, 0),
              })
              this.map.addOverLay(label2)
              signs.push(label2)
            }
          }
        })
      }

      // 创建绘制实例
      let polygonTool = new T.PolygonTool(this.map, polygonConfig)
      // 传入数据绘制
      if (points.length > 0) {
        points.forEach((item, index) => {
          points[index] = new T.LngLat(item[0], item[1])
        })
        startDrawing()
        createStartSign(points[0])
        createLabelsSign(points)

        polygonInstance = new T.Polygon(points, polygonConfig)
        this.map.addOverLay(polygonInstance)
        // console.error('polygonInstance', polygonInstance)
      } else {
        // polygonTool && polygonTool.clear()
      }

      if (!mergeConfig.disabled) {
        polygonTool.addEventListener('draw', (event) => {
          startDrawing()
          const lnglats = event.currentLnglats
          // console.error('event======>', event)
          if (lnglats.length > 0) {
            createStartSign(lnglats[0])
            let lnglatsX = lnglats.concat([lnglats[0]])
            createLabelsSign(lnglatsX)
          }

          polygonInstance = event.currentPolygon
          this.map.addOverLay(polygonInstance)
          if (callback) {
            callback(polygonTool, event)
          }
        })
        this.map.addEventListener('click', () => {
          this.map.disableDoubleClickZoom()
          polygonTool.open() // 激活多边形绘制工具
        })
        this.map.addEventListener('dblclick', () => {
          // polygonTool.clear();
          polygonTool.endDraw()
          // polygonTool.close();
        })
      }

      return {
        openDrawing: () => polygonTool.open(),
        clearDrawing: () => polygonTool.clear(),
        endDrawing: () => polygonTool.endDraw(),
        closeDrawing: () => polygonTool.close(), // 停止绘制的接口
      }
    },
    isValidJSON(str) {
      try {
        // 尝试解析字符串
        JSON.parse(str)
        // 如果解析成功，返回true
        return true
      } catch (e) {
        // 解析失败，返回false
        return false
      }
    },
    // 开始加载地图数据
    initMapData(map) {
      this.map = map
      let points = this.isValidJSON(this.model.fenceRadius) ? JSON.parse(this.model.fenceRadius) : []
      this.setFence({
        points,
        config: { disabled: this.formDisabled },
        callback: (polygonTool, event) => {
          const lnglats = event.currentLnglats || []
          const fenceRadius = []
          lnglats.forEach((item) => {
            fenceRadius.push([item.lng, item.lat])
          })
          fenceRadius.push([lnglats[0].lng, lnglats[0].lat])
          this.form.setFieldsValue({ fenceRadius: JSON.stringify(fenceRadius) })
        },
      })
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'code', 'name', 'fenceType', 'fenceRadius', 'remark'))
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'code', 'name', 'fenceType', 'fenceRadius', 'remark'))
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .ant-spin-container {
  display: flex;
  height: 100%;
}
.form-container {
  width: 700px;
  flex-shrink: 0;
}

.pageMain {
  flex: 1;
  height: 100%;
  position: relative;
  overflow: hidden;
  #map {
    width: 100%;
    /* 先这样定义页面高度 后面需要动态计算 */
    height: calc(100% - 5px);
  }
}
</style>
