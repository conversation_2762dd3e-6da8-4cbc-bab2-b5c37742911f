<template>
  <div class="list-page watch-dolphin-log-list">
    <table-layout
      :rightTitle="rightTitle"
      ref="tableLayout"
      :search-props="searchProps"
      :table-props="tableProps"
      @search-submit="handleSearchSubmit"
      @table-change="handleSearchSubmit"
      @search-reset="handleReset"
    >
      <template slot="customButtons" slot-scope="formModel">
        <div>
          <a-button type="primary" @click="handleSync(formModel)" class="ml-10">同步</a-button>
        </div>
      </template>
      <template slot="photo" slot-scope="{ record, index }">
        <a-avatar shape="square" :src="record.preview" class="pointer" :size="48" @click="preview(index)" />
      </template>
      <template slot="introduce" slot-scope="{ text }">
        <div class="w-100pre text-left maxh-50 text-ellipsis-clamp" :title="text">{{ text }}</div>
      </template>
    </table-layout>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
export default {
  mixins: [JeecgTreeListMixin],
  data() {
    return {
      queryParam: {},
      current: 0,
      isorter: {
        //此处禁用排序
        column: '',
        order: '',
      },
      // 搜索组件的props
      searchProps: {
        resetTitle: '',
        formModel: {
          userName: null,
          subcontractorName: null,
          idCard: null,
          monthIn: null,
        },
        formItems: [
          {
            key: 'subcontractorName',
            label: '所属单位',
            type: 'select',
            options: [],
            colConfig: { sm: 24, md: 12, lg: 8, xl: 5 },
            labelCol: { style: { width: '80px' } },
          },
          {
            key: 'monthIn',
            label: '发放月份',
            type: 'month',
            format: 'YYYY-MM',
            colConfig: { sm: 24, md: 12, lg: 8, xl: 4 },
            labelCol: { style: { width: '80px' } },
          },
          {
            key: 'userName',
            label: '人员名称',
            type: 'text',
            colConfig: { sm: 24, md: 12, lg: 8, xl: 3 },
            labelCol: { style: { width: '80px' } },
          },
          {
            key: 'idCard',
            label: '证件号码',
            type: 'text',
            colConfig: { sm: 24, md: 12, lg: 8, xl: 4 },
            labelCol: { style: { width: '80px' } },
          },
        ],
      },
      // 表格组件的props
      tableProps: {
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        isort: {
          column: 'watchDate',
          order: 'desc',
        },
        dataSource: [],
        columns: [
          {
            title: '#',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            },
          },
          {
            title: '发放月份',
            align: 'center',
            dataIndex: 'monthIn',
            width: 120,
          },
          {
            title: '人员姓名',
            align: 'center',
            dataIndex: 'userName',
            width: 90,
          },
          {
            title: '银行卡号',
            align: 'center',
            dataIndex: 'bankAccount',
            width: 180,
          },
          {
            title: '分包商名称',
            align: 'center',
            dataIndex: 'subcontractorName',
            width: 220,
          },
          {
            title: '应发劳务费',
            align: 'center',
            dataIndex: 'laborAmount',
            width: 90,
          },
          {
            title: '社保金额',
            align: 'center',
            dataIndex: 'socialAmount',
            width: 90,
          },
          {
            title: '实发工资',
            align: 'center',
            dataIndex: 'actAmount',
            width: 90,
          },
          {
            title: '发放时间',
            align: 'center',
            dataIndex: 'payTime',
            width: 150,
          },
        ],
        bordered: false,
      },
      rightTitle: '人员工资',
      url: {
        list: '/staff/salaryPage',
      },
    }
  },
  created() {
    this.initSubInfo()
  },
  watch: {
    // 'tableProps.dataSource': {
    //   handler: function (newVal, oldVal) {
    //     if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
    //       this.getPreview()
    //     }
    //   },
    //   deep: true,
    // },
  },
  mounted() {
    // 获取字典数据
    // 请求预览照片 改成回调去调用
  },
  methods: {
    handleReset() {},
    initSubInfo() {
      getAction('/staff/subInfo').then((res) => {
        if (res.success) {
          this.searchProps.formItems[0].options = res.result.map((x) => {
            return {
              label: x.name,
              value: x.id,
            }
          })
        }
      })
    },
    async getPreview() {
      console.log('getPreview', this.tableProps.dataSource)
      if (this.tableProps.dataSource.length > 0) {
        let list = []
        this.tableProps.dataSource.forEach((item) => {
          list.push(getAction(`/sys/common/getMinioPath/pcksh/${item.photo}`))
        })
        const res = await Promise.all(list)
        console.log('res', res)
        this.tableProps.dataSource.forEach((item, index) => {
          item.preview = res[index].result
          // item.preview = res[index].result.replace('http://127.0.0.1:7069', '')
          this.$set(this.tableProps.dataSource, index, item)
        })
      }
    },
    preview(index) {
      console.log('preview', index)
      let urlData = this.tableProps.dataSource.map((x) => x.preview)
      this.$hevueImgPreview({
        multiple: true,
        nowImgIndex: index,
        imgList: urlData,
      })
      this.$previewRefresh()
    },
    submitOk() {
      console.log('onSearchSubmitonSearchSubmit')
      const formModel = { ...this.searchProps.formModel }
      this.loadData(formModel)
    },

    changeCurrent(index, item) {
      this.current = index
      this.tableProps.ipagination.current = 1
    },
    handleSearchSubmit(searchData) {
      this.searchData = searchData || {}
      const formModel = { ...this.searchProps.formModel }
      this.loadData(formModel)
    },
    handleSync(formModel) {
      console.log('同步', formModel)
      // 获取月份
      let monthIn = formModel.text.formModel.monthIn || null
      if (!monthIn) {
        this.$message.error('请选择发放月份')
        return
      }
      getAction(`/staff/salarySync?month=${monthIn}`)
        .then((res) => {
          if (res.success) {
            this.$message.success('同步完成 ')
          }
        })
    },
  },
}
</script>
<style lang="scss" scoped></style>
