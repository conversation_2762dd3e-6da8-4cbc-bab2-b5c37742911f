<template>
  <div class="list-page watch-dolphin-log-list">
    <table-layout
      :rightTitle="rightTitle"
      ref="tableLayout"
      :search-props="searchProps"
      :table-props="tableProps"
      @search-submit="handleSearchSubmit"
      @table-change="handleSearchSubmit"
      @search-reset="handleReset"
    >
      <template slot="photo" slot-scope="{ record }">
        <img
          v-if="record.pictureIdentity"
          :src="'data:image/png;base64,' + record.pictureIdentity"
          class="pointer w-50"
        />
      </template>
      <template slot="photo2" slot-scope="{ record }">
        <img v-if="record.pictureScene" :src="'data:image/png;base64,' + record.pictureScene" class="pointer w-50" />
      </template>
    </table-layout>
    <modal ref="modalForm" @ok="submitOk"></modal>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
export default {
  mixins: [JeecgTreeListMixin],
  components: { Modal },
  data() {
    return {
      queryParam: {},
      current: 0,
      isorter: {
        //此处禁用排序
        column: '',
        order: '',
      },
      // 搜索组件的props
      searchProps: {
        resetTitle: '同步',
        formModel: {
          name: null,
          subName: null,
          idCard: null,
        },
        formItems: [
          { key: 'subName', label: '所属单位', type: 'select', options: [] },
          { key: 'name', label: '人员名称', type: 'text' },
          { key: 'idCard', label: '证件号码', type: 'text' },
        ],
      },
      // 表格组件的props
      tableProps: {
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        isort: {
          column: 'watchDate',
          order: 'desc',
        },
        dataSource: [],
        columns: [
          {
            title: '#',
            dataIndex: '',
            key: 'rowIndex',
            width: 50,
            align: 'center',
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            },
          },
          {
            title: '姓名',
            align: 'center',
            dataIndex: 'name',
            width: 80,
          },
          {
            title: '证件号码',
            align: 'center',
            dataIndex: 'idCard',
            width: 180,
          },
          {
            title: '性别',
            align: 'center',
            dataIndex: 'sexName',
            width: 50,
          },
          {
            title: '所属单位',
            align: 'center',
            dataIndex: 'subName',
            width: 220,
          },
          {
            title: '人员状态',
            align: 'center',
            dataIndex: 'curState',
            width: 80,
          },
          {
            title: '进场时间',
            align: 'center',
            dataIndex: 'comingDate',
            width: 100,
          },
          {
            title: '工种',
            align: 'center',
            dataIndex: 'positionName',
            width: 80,
          },
          {
            title: '退场时间',
            align: 'center',
            dataIndex: 'outDate',
            width: 100,
          },
          {
            title: '身份证照片',
            align: 'center',
            dataIndex: 'pictureIdentity',
            width: 80,
            scopedSlots: { customRender: 'photo' },
          },
          {
            title: '现场采集照片',
            align: 'center',
            dataIndex: 'pictureScene',
            width: 80,
            scopedSlots: { customRender: 'photo2' },
          },
          {
            title: '民族',
            align: 'center',
            dataIndex: 'ethnicGroup',
            width: 80,
          },
          {
            title: '籍贯',
            align: 'center',
            dataIndex: 'province',
            width: 80,
          },
          {
            title: '住址',
            align: 'center',
            dataIndex: 'address',
            // width: 220,  
          },
        ],
        actionButtons: [
          {
            text: '合同信息',
            handler: this.handleContract,
          },
        ],
        bordered: false,
      },
      rightTitle: '实名制',
      url: {
        list: '/staff/staffPage',
      },
    }
  },
  created() {
    this.initSubInfo()
  },
  watch: {
    // 'tableProps.dataSource': {
    //   handler: function (newVal, oldVal) {
    //     if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
    //       this.getPreview()
    //     }
    //   },
    //   deep: true,
    // },
  },
  mounted() {
    // 获取字典数据
    // 请求预览照片 改成回调去调用
  },
  methods: {
    initSubInfo() {
      getAction('/staff/subInfo').then((res) => {
        if (res.success) {
          this.searchProps.formItems[0].options = res.result.map((x) => {
            return {
              label: x.name,
              value: x.id,
            }
          })
        }
      })
    },
    async getPreview() {
      console.log('getPreview', this.tableProps.dataSource)
      if (this.tableProps.dataSource.length > 0) {
        let list = []
        this.tableProps.dataSource.forEach((item) => {
          list.push(getAction(`/sys/common/getMinioPath/pcksh/${item.photo}`))
        })
        const res = await Promise.all(list)
        console.log('res', res)
        this.tableProps.dataSource.forEach((item, index) => {
          item.preview = res[index].result
          // item.preview = res[index].result.replace('http://127.0.0.1:7069', '')
          this.$set(this.tableProps.dataSource, index, item)
        })
      }
    },
    preview(index) {
      console.log('preview', index)
      let urlData = this.tableProps.dataSource.map((x) => x.preview)
      this.$hevueImgPreview({
        multiple: true,
        nowImgIndex: index,
        imgList: urlData,
      })
      this.$previewRefresh()
    },
    submitOk() {
      console.log('onSearchSubmitonSearchSubmit')
      const formModel = { ...this.searchProps.formModel }
      this.loadData(formModel)
    },

    changeCurrent(index, item) {
      this.current = index
      this.tableProps.ipagination.current = 1
    },
    handleSearchSubmit(searchData) {
      this.searchData = searchData || {}
      const formModel = { ...this.searchProps.formModel }
      this.loadData(formModel)
    },
    handleContract(record) {
      getAction(`/staff/staffContract?staffId=${record.staffId}`).then(res =>{
        if(res){
          let contract = res.content[0];
          if(contract){
            contract.contractStart = contract.contractStart?contract.contractStart.substring(0,10):""
            contract.contractEnd = contract.contractEnd?contract.contractEnd.substring(0,10):""
            this.$refs.modalForm.title = '合同签订详情'
            this.$refs.modalForm.edit(contract)
            this.$refs.modalForm.disableSubmit = true
          }else{
            this.$message.error('未查询到合同信息')
          }
        }else{
          this.$message.error('未查询到合同信息')
        }
      })
    },
    handleReset() {
      if (this.isLoading) {
        return
      }
      this.isLoading = true
      getAction('/staff/staffSync').then((res) => {
        if (res.success) {
          this.$message.success('同步完成')
          this.isLoading = false
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped></style>
