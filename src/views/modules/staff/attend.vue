<template>
  <div class="list-page watch-dolphin-log-list">
    <table-layout
      :rightTitle="rightTitle"
      ref="tableLayout"
      :search-props="searchProps"
      :table-props="tableProps"
      @search-submit="handleSearchSubmit"
      @table-change="handleSearchSubmit"
      @search-reset="handleReset"
    >
      <template slot="photo" slot-scope="{ record, index }">
        <img v-if="record.clockInPicture" shape="square" :src="'data:image/png;base64,'+record.clockInPicture" class="pointer w-50" />
      </template>
      <template slot="photo2" slot-scope="{ record }">
        <img v-if="record.clockOutPicture" :src="'data:image/png;base64,'+record.clockOutPicture" class="pointer w-50" />
      </template>
    </table-layout>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
export default {
  mixins: [JeecgTreeListMixin],
  data() {
    return {
      queryParam: {},
      current: 0,
      isorter: {
        //此处禁用排序
        column: '',
        order: '',
      },
      // 搜索组件的props
      searchProps: {
        resetTitle: "同步",
        formModel: {
          name: null,
          subcontractorName: null,
          identityCard: null

        },
        formItems: [
          { key: 'subcontractorName', label: '所属单位', type: 'select', options:[],colConfig: { sm: 24, md: 12, lg: 8, xl: 5 } },
          { key: 'name', label: '人员', type: 'text', colConfig: { sm: 24, md: 12, lg: 8, xl: 3 }, labelCol:{ style: { width: '50px' }}},
          {
            key: 'datetimerange',
            label: '考勤日期',
            type: 'datetime_range',
            keyParams: ['start', 'end'],
            format: 'YYYY-MM-DD',
          },
          { key: 'identityCard', label: '证件号码', type: 'text',colConfig: { sm: 24, md: 12, lg: 8, xl: 4 } },
        ],
      },
      // 表格组件的props
      tableProps: {
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        isort: {
          column: 'watchDate',
          order: 'desc',
        },
        dataSource: [],
        columns: [
          {
            title: '#',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            },
          },
          {
            title: '考勤日期',
            align: 'center',
            dataIndex: 'clockDate',
            width: 100,
          },
          {
            title: '人员名称',
            align: 'center',
            dataIndex: 'name',
            width: 100,
          },
          
          {
            title: '证件号码',
            align: 'center',
            dataIndex: 'identityCard',
            width: 140,
          },
          {
            title: '上班打卡时间',
            align: 'center',
            dataIndex: 'firstTime',
            width: 160,
          },
          {
            title: '上班考勤照片',
            align: 'center',
            dataIndex: 'clockInPicture',
            width: 200,
            scopedSlots: { customRender: 'photo' },
          },
          {
            title: '下班打卡时间',
            align: 'center',
            dataIndex: 'lastTime',
            width: 160,
          },
          {
            title: '下班打卡照片',
            align: 'center',
            dataIndex: 'clockOutPicture',
            scopedSlots: { customRender: 'photo2' },
            width: 200,
          },
        ],
        bordered: false,
      },
      rightTitle: '人员考勤',
      url: {
        list: '/staff/attendPage'
      },
      isLoading: false,
    }
  },
  created() {
    this.initSubInfo();
  },
  watch: {
    // 'tableProps.dataSource': {
    //   handler: function (newVal, oldVal) {
    //     if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
    //       this.getPreview()
    //     }
    //   },
    //   deep: true,
    // },
  },
  mounted() {
    // 获取字典数据
    // 请求预览照片 改成回调去调用
  },
  methods: {
    initSubInfo() {
      getAction("/staff/subInfo").then((res) => {
        if (res.success) {
          this.searchProps.formItems[0].options = res.result.map((x) => {
            return {
              label: x.name,
              value: x.id,
            }
          })
        }
      })
    },
    async getPreview() {
      console.log('getPreview', this.tableProps.dataSource)
      if (this.tableProps.dataSource.length > 0) {
        let list = []
        this.tableProps.dataSource.forEach((item) => {
          list.push(getAction(`/sys/common/getMinioPath/pcksh/${item.photo}`))
        })
        const res = await Promise.all(list)
        console.log('res', res)
        this.tableProps.dataSource.forEach((item, index) => {
          item.preview = res[index].result
          // item.preview = res[index].result.replace('http://127.0.0.1:7069', '')
          this.$set(this.tableProps.dataSource, index, item)
        })
      }
    },
    preview(index) {
      console.log('preview', index)
      let urlData = this.tableProps.dataSource.map((x) => x.preview)
      this.$hevueImgPreview({
        multiple: true,
        nowImgIndex: index,
        imgList: urlData,
      })
      this.$previewRefresh()
    },
    submitOk() {
      console.log('onSearchSubmitonSearchSubmit')
      const formModel = { ...this.searchProps.formModel }
      this.loadData(formModel)
    },

    changeCurrent(index, item) {
      this.current = index
      this.tableProps.ipagination.current = 1
    },
    handleSearchSubmit(searchData) {
      this.searchData = searchData || {}
      const formModel = { ...this.searchProps.formModel }
      this.loadData(formModel)
    },
    handleAdd() {
      this.$refs.modalForm.title = '新增党员'
      this.$refs.modalForm.add({})
      this.$refs.modalForm.disableSubmit = false
    },
    handleReset() {
      if(this.isLoading){
        return;
      }
      this.isLoading = true;
      getAction("/staff/attendSync").then((res) => {
        if (res.success) {
          this.$message.success('同步完成')
          this.isLoading = false;
        }
      })
    },

  },
}
</script>
<style lang="scss" scoped></style>
