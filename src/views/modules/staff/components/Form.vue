<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-row :gutter="8">
          <a-col :span="12">
            <a-form-item label="合同开始时间">
              <a-input v-decorator="['contractStart']" placeholder="请输入"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="合同结束时间">
              <a-input v-decorator="['contractEnd']" placeholder="请输入"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="约定薪酬标准">
              <a-input v-decorator="['salaryStandard']" placeholder="请输入"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="薪酬发放周期">
              <a-input v-decorator="['spanCode']" placeholder="请输入"></a-input>
            </a-form-item>
          </a-col>
          <!-- <a-col :span="24">
            <a-form-item label="介绍" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
              <a-textarea
                :maxLength="500"
                v-decorator="['introduce', validatorRules.introduce]"
                placeholder="请输入介绍"
                :rows="5"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="照片" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
              <j-upload
                :accept="'.png,.jpg,.jpeg,.gif'"
                :disabled="false"
                v-decorator="['photo', validatorRules.photo]"
                :isMultiple="false"
                :fileType="'image'"
                :number="1"
              ></j-upload>
            </a-form-item>
          </a-col> -->
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'

import { pick, cloneDeep } from 'lodash'

export default {
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      
      positionOption: [],
      folderId: '',
      form: this.$form.createForm(this),
      model: {},
      confirmLoading: false,
      url: {
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
  },
  methods: {
    onChange(date, dateString) {
      console.log(date, dateString);
    },
    add(record) {
      this.edit(record)
    },
    edit(oldRecord) {
      console.log('Formrecord', record)
      let record = cloneDeep(oldRecord)
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(this.model, 'contractStart', 'contractEnd', 'salaryStandard', 'spanCode')
        )
      })
    },

    getExtension(filename) {
      var index = filename.toLowerCase().lastIndexOf('.')
      return index < 0 ? '' : filename.substr(index)
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'contractStart', 'contractEnd', 'salaryStandard', 'spanCode'))
    },
  },
}
</script>
