<template>
  <div class="list-page data_shif_info_list">
    <table-layout
      ref="tableLayout1"
      :search-props="searchProps"
      :rightTitle="rightTitle"
      :table-props="tableProps"
      :tree-props="treeProps"
      @tree-init="onTreeInit"
      @tree-select="onTreeSelect"
      @search-submit="onSearchSubmit"
      @table-change="onSearchSubmit"
    >
      <template slot="action" slot-scope="{ record }">
        <span>{{ record.action == 'in' ? '入场' : '出场' }}</span>
      </template>
      <template slot="file" slot-scope="{ text }"
        ><img class="w-24 h-24" :src="text" alt="" @click="priview(text)"
      /></template>
    </table-layout>
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getDictItems } from '@/components/dict/JDictSelectUtil'
import { getAction } from '@/api/manage'

export default {
  mixins: [JeecgListMixin],
  components: {},
  data() {
    return {
      disableMixinCreated: true,
      rightTitle: '',
      treeProps: {
        treeField: 'deviceId',
        // dictCode: 'ship_typ_code', //此处是字典采用自定义函数获取treeData的方式来实现
        isSelectFirstChild: true,
        selectedKeys: ['-1'],
        isSelectParentNodes: false,
        replaceFields: { title: 'name', key: 'id' },
        treeData: [],
      },
      // 表头
      searchProps: {
        formModel: {
          realname: null,
        },
        formItems: [
          { key: 'realname', label: '人员名称', type: 'text' },
          {
            key: 'datetimerange',
            label: '选择时间',
            type: 'datetime_range',
            keyParams: ['startDate', 'endDate'],
            format: 'YYYY-MM-DD',
          },
        ],
      },
      tableProps: {
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '#',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            },
          },
          {
            title: '姓名',
            align: 'center',
            dataIndex: 'realname',
            width: 150,
          },
          {
            title: '手机号码',
            align: 'center',
            dataIndex: 'phone',
            width: 180,
          },
          {
            title: '所属单位',
            align: 'center',
            dataIndex: 'department',
          },
          {
            title: '进/出场时间',
            align: 'center',
            dataIndex: 'recordTime',
            width: 180,
          },
          {
            title: '状态',
            align: 'center',
            dataIndex: 'action',
            scopedSlots: { customRender: 'action' },
            width: 120,
          },
          {
            title: '抓拍',
            align: 'center',
            dataIndex: 'file',
            scopedSlots: { customRender: 'file' },
            width: 200,
          },
        ],
        actionButtons: [],
        headerButtons: [],
      },
      url: {
        list: '/monitor/person/list',
      },
      dictOptions: {},
      superFieldList: [],
      selectedKeys: [],
      iExpandedKeys: [],
      autoExpandParent: true,
      checkStrictly: true,
      extraData: {},
      areaList: [],
    }
  },
  async created() {
    await this.getareaList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    async getareaList() {
      this.loading = true
      const resp = await getAction('/base/baseArea/list')
      if (resp.success) {
        const records = resp.result.records
        this.areaList = records || []
        await this.loadTree()
      }
    },
    // 初始化树
    async loadTree() {
      const resp = await getAction('/base/points/list', { type: 'person' })
      if (resp.success) {
        const records = resp.result
        console.log('初始化树', records, this.areaList)

        this.areaList.forEach((item) => {
          if (records[item.id]) {
            item.children = records[item.id].filter((a) => a.deviceType == 'gate')
          }
        })
        // 处理数据 处理成树形结构
        let root = [
          {
            name: '额敏风电场',
            id: '0',
            children: this.areaList,
          },
        ]
        this.treeProps.treeData = root
      }
    },
    priview(url) {
      this.$hevueImgPreview(url)
    },
    getCurrentTreeId() {
      let selectedKeys = this.$refs.tableLayout1.mergeParams.selectedKeys
      return selectedKeys ? selectedKeys[0] : undefined
    },

    onTreeInit(params) {
      console.log('tree init', params)
      this.queryParam.deviceId = params.params.deviceId
      console.log('加载1')
      this.loadData()
    },
    onTreeSelect(params) {
      console.log('tree select', params)
      this.onSearchSubmit(params)
    },
    onSearchSubmit(params) {
      console.log('searchData', this.searchProps, params)
      this.queryParam = this.searchProps.formModel
      this.queryParam.deviceId = params.params.deviceId
      console.log('加载2', this.queryParam)
      this.loadData(this.queryParam)
    },
    handleCurrentPosition() {},
    handleAdd() {
      if (this.getCurrentTreeId() === '-1') {
        this.$warning({ title: '提示', content: '请选择船舶类型' })
        return
      }
      console.log('xxxxxxxxxxx', { shipType: this.getCurrentTreeId() })
      this.$refs.modalForm.edit({ shipType: this.getCurrentTreeId() })
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.disableSubmit = false
    },
    initDictConfig() {},
    onSelect(selectedKeys, e) {
      console.log('selected', selectedKeys, e)
      this.selectedKeys = selectedKeys
      if (e.selectedNodes.length > 0) {
        this.extraData.shipType = e.selectedNodes[0].data.props
        console.log('加载3')
        this.loadData(this.extraData)
      }
    },
    onExpand(expandedKeys) {},
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
