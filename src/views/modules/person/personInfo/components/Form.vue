<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <!-- <a-col :span="12">
            <a-form-item label="账号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['userId', validatorRules.userId]"
                :trigger-change="true"
                dictCode="sys_user,username,id"
                placeholder="请选择账号"
              />
            </a-form-item>
          </a-col> -->
          <a-col :span="12">
            <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['name', validatorRules.name]"
                placeholder="请输入姓名"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="登录账号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['userName', validatorRules.userName]"
                placeholder="请输入登录账号"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['sex']"
                :trigger-change="true"
                dictCode="sex"
                placeholder="请选择性别"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="年龄" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                autocomplete="off"
                v-decorator="['age', validatorRules.age]"
                placeholder="请输入年龄"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['idCard', validatorRules.idCard]"
                placeholder="请输入身份证号"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号码" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['phone', validatorRules.phone]"
                placeholder="请输入手机号码"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="人员类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['personType', validatorRules.personType]"
                :trigger-change="true"
                dictCode="person_type"
                placeholder="请选择人员类型"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="所属单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['department', validatorRules.department]"
                :trigger-change="true"
                dictCode="project_unit,name,id"
                placeholder="请选择所属单位"
                @change="handleDepartmentChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="单位类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                disabled
                type="list"
                v-decorator="['departmentType', validatorRules.departmentType]"
                :trigger-change="true"
                dictCode="unit_type"
                placeholder="请选择单位类型"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="岗位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['job', validatorRules.job]"
                :trigger-change="true"
                dictCode="job"
                placeholder="请选择岗位"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="人员编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                placeholder="请输入编号，示例：QZHF-0001"
                autocomplete="off"
                v-decorator="['code', validatorRules.code]"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="进场时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择进场时间"
                v-decorator="['enterDate', validatorRules.enterDate]"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select
                v-decorator="['deviceCode', validatorRules.deviceCode]"
                placeholder="请选择"
                allowClear
              >
                <a-select-option v-for="item in deviceHelmetList" :key="item.deviceCode" :value="item.deviceCode">
                  {{ item.deviceName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="安全教育" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['safeEducation', validatorRules.safeEducation]"
                :trigger-change="true"
                dictCode="safe_education"
                placeholder="请选择"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="退场时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择退场时间"
                v-decorator="['leaveDate']"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="照片" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload text="添加照片" :number="3" :fileType="'image'" v-decorator="['picture']" size="'mine'">
                <template slot="icon">
                  <a-icon type="plus-circle" theme="filled" :style="{ fontSize: '24px', color: '#0096ff' }" />
                </template>
              </j-upload>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction, postAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'PersonInfoForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      deviceHelmetList: [],
      fileList1: '',
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        deviceCode: {
          rules: [
            { required: true, message: '请选择安全帽' }
          ]
        },
        userId: {
          rules: [
            { required: true, message: '请选择账号!' },
            {
              validator: (rule, value, callback) =>
                validateDuplicateValue('person_info', 'user_id', value, this.model.id, callback)
            }
          ]
        },
        idCard: {
          rules: [
            {
              validator: (rule, value, callback) => {
                if (value && value.length !== 18) {
                  callback('身份证号码长度必须为18位')
                }
                callback()
              }
            }
          ]
        },
        userName: {
          rules: [{ required: true, message: '请输入用户账号!' }]
        },
        name: {
          rules: [{ required: true, message: '请输入人员姓名!' }]
        },
        age: {
          rules: [{ required: true, message: '请输入年龄!' }]
        },
        phone: {
          rules: [
            { required: true, message: '请输入手机号码!' },
            { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码!' },
            //唯一校验
            {
              validator: (rule, value, callback) =>
                validateDuplicateValue('person_info', 'phone', value, this.model.id, callback)
            }
          ]
        },
        personType: {
          rules: [{ required: true, message: '请输入人员类型!' }]
        },
        department: {
          rules: [{ required: true, message: '请输入所属单位!' }]
        },
        job: {
          rules: [{ required: true, message: '请输入岗位!' }]
        },
        enterDate: {
          rules: [{ required: true, message: '请选择' }]
        },
        code: {
          rules: [
            { required: true, message: '请输入编号!' },
            {
              validator: (rule, value, callback) =>
                validateDuplicateValue('person_info', 'code', value, this.model.id, callback)
            }
          ]
        }
      },
      url: {
        add: '/person/personInfo/add',
        edit: '/person/personInfo/edit',
        queryById: '/person/personInfo/queryById',
        queryUnitById: '/project/projectUnit/queryById',
        getCanSelectDeviceHelmetList: '/person/personInfo/getCanSelectDeviceHelmetList'
      }
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    loadAviableDeviceHelmetList(record) {
      postAction(this.url.getCanSelectDeviceHelmetList, record).then((res) => {
        if (res.success) {
          this.deviceHelmetList = res.result
        }
      })
    },
    // onChange(value){
    //     console.log("SSSSSSSSSSSSSSSSSSSSSS"+value)
    //     if(value=='0'||value==2){
    //       console.log("SSSSSSSSSSSSSSSSSSSSSS")
    //     }
    // },
    handleDepartmentChange(value) {
      console.log('handleDepartmentChange', value)
      this.shipId = value
      getAction(this.url.queryUnitById, { id: value }).then((res) => {
        if (res.success) {
          this.form.setFieldsValue({ departmentType: res.result.unitType })
        }
      })
    },

    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      // 加载可选择的设备列表
      this.loadAviableDeviceHelmetList(record)
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'createTime',
            'name',
            'sex',
            'age',
            'idCard',
            'phone',
            'personType',
            'department',
            'departmentType',
            'job',
            'enterDate',
            'leaveDate',
            'picture',
            'userId',
            'code',
            'userName',
            'deviceCode',
            'safeEducation'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'createTime',
          'name',
          'sex',
          'age',
          'idCard',
          'phone',
          'personType',
          'department',
          'departmentType',
          'job',
          'enterDate',
          'leaveDate',
          'picture',
          'userId',
          'code',
          'userName',
          'deviceCode',
          'safeEducation'
        )
      )
    }
  }
}
</script>
