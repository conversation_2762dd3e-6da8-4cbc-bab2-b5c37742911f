<template>
  <div class="list-page person-info-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @search-submit="handleSearchSubmit"
      @table-change="handleSearchSubmit"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
export default {
  name: 'PersonInfoList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {
      rightTitle: '人员信息登记',
      queryParam: {
        autoInput: 1,
      },
      searchData: {},
      // 搜索组件的props
      searchProps: {
        formModel: {
          name: null,
          personType: null,
        },
        formItems: [
          { key: 'name', label: '人员姓名', placeholder: '请输入人员姓名' },
          {
            key: 'personType',
            label: '人员类型',
            classType: 'list',
            dictCode: 'person_type',
            placeholder: '请选择人员类型',
          },
        ],
      },
      // 表格组件的props
      tableProps: {
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '姓名',
            dataIndex: 'name',
            align: 'center',
          },
          {
            title: '性别',
            dataIndex: 'sex_dictText',
            align: 'center',
          },
          {
            title: '人员类型',
            dataIndex: 'personType_dictText',
            align: 'center',
          },
          {
            title: '所属单位',
            dataIndex: 'department_dictText',
            align: 'center',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 175,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
          },
        ],
        // headerButtons: [
        //   {
        //     text: '新增',
        //     icon: 'plus',
        //     handler: this.handleAdd,
        //   },
        // ],
      },

      url: {
        list: '/person/personInfo/list',
        delete: '/person/personInfo/delete',
        deleteBatch: '/person/personInfo/deleteBatch',
        exportXlsUrl: '/person/personInfo/exportXls',
        importExcelUrl: 'person/personInfo/importExcel',
      },
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    modalFormOk() {
      const formModel = { ...this.searchData.formModel }
      delete formModel.date
      this.loadData(formModel)
    },

    handleSearchSubmit(searchData) {
      this.searchData = searchData || {}
      const formModel = { ...this.searchData.formModel }
      delete formModel.date
      this.loadData(formModel)
    },
  },
}
</script>
