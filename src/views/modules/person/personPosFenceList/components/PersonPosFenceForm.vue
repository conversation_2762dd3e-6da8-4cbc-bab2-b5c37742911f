<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <!-- 主表单区域 -->
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24" >
            <a-form-item label="电子围栏名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['fenceName']" placeholder="请输入电子围栏名称" ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="绘制区域类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['drawType']" :trigger-change="true" dictCode="draw_type" placeholder="请选择绘制区域类型" />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="启用状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['isUsed']" :trigger-change="true" dictCode="is_used" placeholder="请选择启用状态" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
      <!-- 子表单区域 -->
    <a-tabs v-model="activeKey" @change="handleChangeTabs">
      <a-tab-pane tab="人员定位电子围栏经纬度子表" :key="refKeys[0]" :forceRender="true">
        <j-editable-table
          :ref="refKeys[0]"
          :loading="personPosFenceDetailTable.loading"
          :columns="personPosFenceDetailTable.columns"
          :dataSource="personPosFenceDetailTable.dataSource"
          :maxHeight="300"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :actionButton="true"/>
      </a-tab-pane>
    </a-tabs>
    <a-row v-if="showFlowSubmitButton" style="text-align: center;width: 100%;margin-top: 16px;"><a-button @click="handleOk">提 交</a-button></a-row>
  </a-spin>
</template>

<script>

  import pick from 'lodash.pick'
  import { getAction } from '@/api/manage'
  import { FormTypes,getRefPromise } from '@/utils/JEditableTableUtil'
  import { JEditableTableMixin } from '@/mixins/JEditableTableMixin'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'PersonPosFenceForm',
    mixins: [JEditableTableMixin],
    components: {
    },
    data() {
      return {
        // 新增时子表默认添加几行空数据
        addDefaultRowNum: 1,
        validatorRules: {
        },
        refKeys: ['personPosFenceDetail', ],
        tableKeys:['personPosFenceDetail', ],
        activeKey: 'personPosFenceDetail',
        // 人员定位电子围栏经纬度子表
        personPosFenceDetailTable: {
          loading: false,
          dataSource: [],
          columns: [
            {
              title: '经度',
              key: 'longitude',
                  type: FormTypes.input,
                  width:"120px",
              placeholder: '请输入${title}',
              defaultValue:'',
            },
            {
              title: '纬度',
              key: 'latitude',
                  type: FormTypes.input,
                  width:"120px",
              placeholder: '请输入${title}',
              defaultValue:'',
            },
            {
              title: '序号',
              key: 'sortNo',
                  type: FormTypes.inputNumber,
                  width:"120px",
              placeholder: '请输入${title}',
              defaultValue:'',
            },
          ]
        },
        url: {
          add: "/person/personPosFence/add",
          edit: "/person/personPosFence/edit",
          queryById: "/person/personPosFence/queryById",
          personPosFenceDetail: {
            list: '/person/personPosFence/queryPersonPosFenceDetailByMainId'
          },
        }
      }
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：false流程表单 true普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    computed: {
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      }
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
    },
    methods: {
      addBefore(){
        this.form.resetFields()
        this.personPosFenceDetailTable.dataSource=[]
      },
      getAllTable() {
        let values = this.tableKeys.map(key => getRefPromise(this, key))
        return Promise.all(values)
      },
      /** 调用完edit()方法之后会自动调用此方法 */
      editAfter(record) {
        let fieldval = pick(this.model,'fenceName','drawType','isUsed')
        this.$nextTick(() => {
          this.form.setFieldsValue(fieldval)
        })
        // 加载子表数据
        if (this.model.id) {
          let params = { id: this.model.id }
          this.requestSubTableData(this.url.personPosFenceDetail.list, params, this.personPosFenceDetailTable)
        }
      },
      /** 整理成formData */
      classifyIntoFormData(allValues) {
        let main = Object.assign(this.model, allValues.formValue)
        return {
          ...main, // 展开
          personPosFenceDetailList: allValues.tablesValue[0].values,
        }
      },
      //渲染流程表单数据
      showFlowData(){
        if(this.formBpm === true){
          let params = {id:this.formData.dataId};
          getAction(this.url.queryById,params).then((res)=>{
            if(res.success){
              this.edit (res.result);
            }
          })
        }
      },
      validateError(msg){
        this.$message.error(msg)
      },
     popupCallback(row){
       this.form.setFieldsValue(pick(row,'fenceName','drawType','isUsed'))
     },

    }
  }
</script>

<style scoped>
</style>