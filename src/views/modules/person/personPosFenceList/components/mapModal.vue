<template>
  <a-modal :title="title" :width="1505" :visible="visible" :maskClosable="false" @cancel="cancel" :footer="false">
    <div class="position-relative">
      <div id="mymap" class="map"></div>
      <div class="search position-absolute">
        <input id="tipinput" placeholder="输入搜索位置" />
      </div>
      <!-- flipOutY  rollOut slideOutRight-->
      <div class="searchRail position-absolute w-330 bg-fff" :class="animated ? 'animated    lightSpeedOut' : ''">
        <div style="background-color: #02a7f0" class="px-10 w-100pre h-40 line-40">电子围栏新增</div>
        <div class="py-10 px-20 text-000">
          <div class="must font-12">电子围栏名称:</div>
          <div class="mt-10 mb-15">
            <a-input class="w-100pre" placeholder="请输入" v-model="params.fenceName" show-count :max-length="20" />
          </div>
          <div class="text-000 font-12">绘制区域</div>
          <div class="flex align-center justify-between mb-20">
            <div
              class="flex align-center"
              :class="index === drawCurrent ? 'drawActive' : ''"
              v-for="(item, index) in drawType"
              :key="index"
              @click="drawTypeChoose(index)"
            >
              <div
                class="iconfont"
                :class="index == 0 ? 'icon-squares' : index == 1 ? 'icon-circle' : 'icon-polygon'"
              ></div>
              <div class="font-10">{{ item }}</div>
            </div>
          </div>

          <div class="flex mb-15 align-center">
            <div class="must mr-20 font-12">状态:</div>
            <div>
              <a-radio-group size="small" @change="changeChose" v-model="params.isUsed" name="radioGroup">
                <a-radio value="1" class="text-000">启用</a-radio>
                <a-radio value="2" class="text-000">禁用</a-radio>
              </a-radio-group>
            </div>
          </div>
          <div class="flex justify-between">
            <a-button @click="cancelSaveClear('cancel')">取消</a-button>
            <a-button @click="cancelSaveClear('clear')">清空</a-button>
            <a-button @click="cancelSaveClear('save')" style="background-color: #02a7f0; color: #fff">保存 </a-button>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { getData, saveData } from '@/utils/geoJson.js'

import { getAction, putAction } from '@/api/manage.js'
export default {
  name: 'mapModal',
  data() {
    return {
      addOredit: 0, //0:add 1:edit
      animated: false,
      drawType: ['矩形区域', '圆形区域', '多边形区域'],
      drawCurrent: '', //画什么养的  围栏
      search: false,
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false,
      issubmit: false,
      TrackName: '', //新增围栏名称

      params: {
        fenceName: '',
        drawType: '',
        isUsed: '1',
        nautica: '',
      },

      // 地图变量
      map: '',
      mouseTool: '',
      geojson: '', //本地存储
      drawConfig: {
        strokeColor: '#02a7f0', //描边
        strokeWeight: 2, //线宽
        strokeOpacity: 1, //描边透明度
        fillColor: '#BFDDFD', //填充
        fillOpacity: 0.5,
        strokeStyle: 'dashed', // 线样式还支持  dotted solid double dashed
        // strokeDasharray: [30,10],
      },
      disabled: false, //是否禁止修改类型
      url: {
        addTrack: '/person/personPosFence/add', //新增围栏
        editTrack: '/person/personPosFence/edit', //新增围栏
      },
    }
  },
  methods: {
    // 初始化地图
    initMap() {
      console.log('我是地图初始化')
      //地图加载
      this.map = new AMap.Map('mymap', {
        resizeEnable: true,
        viewMode: '2D', //是否为3D地图模式
        zoom: 12, //初始化地图级别
        center: [109.919730180333, 19.4268904131667],
      })
      this.geojson = new AMap.GeoJSON({
        geoJSON: null, //要加载的标准GeoJSON对象
      })
      let autoOptions = {
        input: 'tipinput',
      }
      let auto = new AMap.Autocomplete(autoOptions)
      let placeSearch = new AMap.PlaceSearch({
        map: this.map,
      }) //构造地点查询类

      AMap.event.addListener(auto, 'select', (e) => {
        console.log('触发搜索')
        placeSearch.setCity(e.poi.adcode)
        placeSearch.search(e.poi.name) //关键字查询查询
      }) //注册监听，当选中某条记录时会触发
    },
    // 新增围栏
    add() {
      console.log('我是add', this.addOredit)
      this.params.fenceName = ''
      this.params.isUsed = '1'
      this.params.nautica = ''
      this.drawCurrent = ''
      this.disabled = false

      this.mouseTool = new AMap.MouseTool(this.map)
      this.mouseTool.on('draw', (event) => {
        this.geojson.addOverlay(event.obj) //把 对象实例 放在 geoJSON 之中
        if (this.params.drawType == 2) {
          let circle = {
            circleCenter: event.obj.getCenter(),
            circleRadius: event.obj.getRadius(),
          }
          this.params.nautica = JSON.stringify(circle)
          return
        }
        console.log('不是画圆')
        this.params.nautica = JSON.stringify(this.geojson.toGeoJSON(this.geojson))
      })
    },
    // 初始化覆盖物
    edit(item) {
      console.log('我是edit', this.addOredit)
      // 注意这里要做判断  分出类型  区分一下是 矩形 还是圆形  还是多边形
      this.params.fenceName = item.fenceName
      this.params.drawType = item.drawType
      this.disabled = true
      this.drawCurrent = item.drawType - 1
      this.params.isUsed = item.isUsed
      this.params.nautica = JSON.parse(item.nautica)
      this.params.id = item.id

      console.log('item', this.params.nautica)

      if (item.drawType != 2) {
        console.log('drawType', item.drawType)
        this.geojson.importData(JSON.parse(item.nautica))
      }
      let newcover,
        coverEditor,
        cover = this.geojson.getOverlays()[0] //覆盖物

      if (item.drawType == 1) {
        let opts = {
          ...this.drawConfig,
          bounds: cover.getBounds(),
        }
        newcover = new AMap.Rectangle(opts)
        newcover.setMap(this.map)
        this.map.setFitView([newcover])
        coverEditor = new AMap.RectangleEditor(this.map, newcover)
      } else if (item.drawType == 2) {
        console.log('圆', JSON.parse(item.nautica))
        let opts = {
          ...this.drawConfig,
          center: [this.params.nautica.circleCenter.R, this.params.nautica.circleCenter.Q],
          radius: this.params.nautica.circleRadius,
        }
        let circle = new AMap.Circle(opts)
        circle.setMap(this.map)
        // 缩放地图到合适的视野级别
        this.map.setFitView([circle])
        coverEditor = new AMap.CircleEditor(this.map, circle)
      } else {
        cover.setOptions(this.drawConfig)
        cover.setMap(this.map)
        this.map.setFitView([cover])
        coverEditor = new AMap.PolyEditor(this.map, cover)
      }

      coverEditor.on('adjust', (event) => {
        if (this.params.drawType == 2) {
          let circle = {
            circleCenter: event.target.getCenter(),
            circleRadius: event.target.getRadius(),
          }
          this.params.nautica = JSON.stringify(circle)
          return
        }
        this.geojson.clearOverlays()
        this.geojson.addOverlay(event.target)
        this.params.nautica = JSON.stringify(this.geojson.toGeoJSON(this.geojson))
      })
      coverEditor.open()
    },
    // 选择画什么样的围栏
    drawTypeChoose(index) {
      if (this.disabled) {
        this.$message.warning('编辑不可修改围栏类型')
        return
      }
      this.drawCurrent = index
      this.params.drawType = index + 1
      // 切换之前 清空 覆盖物 只保留一个
      this.geojson.clearOverlays()
      this.params.nautica = ''
      this.map.clearMap()

      // 这里根据选择的来对应画围栏区域 0 矩形 1 圆  2多边形 addLayer
      if (index == 0) {
        this.mouseTool.rectangle(this.drawConfig) // 画矩形
      } else if (index == 1) {
        this.mouseTool.circle(this.drawConfig) // 画圆形
      } else {
        this.mouseTool.polygon(this.drawConfig) // 画多边形
      }
    },
    // 数据校验 非空
    rule() {
      let flag = true
      for (let key in this.params) {
        if (!this.params[key]) {
          this.$message.warning('请填写完整信息')
          return false
        }
      }
      return flag
    },
    // 保存 发送 围栏 type 取消  保存 清空
    cancelSaveClear(type) {
      console.log('新增/编辑', 1)
      if (type == 'save') {
        if (!this.rule()) {
          return
        }
        this.animated = true
        this.visible = false
        console.log('params', this.params)

        let url = this.addOredit == 0 ? this.url.addTrack : this.url.editTrack
        getAction(url, this.params).then((res) => {
          // console.log('新增/编辑', res);
          this.$emit('ok')
        })
      } else if (type == 'cancel') {
        this.visible = false
      } else {
        this.params.fenceName = ''
        this.geojson.clearOverlays()
        this.params.nautica = ''
        this.map.clearMap()
      }
    },
    // 是否禁用
    changeChose(e) {
      console.log('选中内容', e.target.value)
    },
    cancel() {
      this.visible = false
    },
  },
}
</script>

<style scoped lang='less'>
// @import 'https://a.amap.com/jsapi_demos/static/demo-center/css/demo-center.css';

.map {
  width: 100%;
  height: 75vh;
}
:deep(.ant-modal-body) {
  padding: 0 !important;
}

.search {
  top: 2%;
  left: 3%;
  z-index: 9999999 !important;
}

.searchRail {
  top: 2%;
  right: 2%;
  z-index: 9999999 !important;
}

.iconfont {
  font-size: 25px;
}

:deep(.ant-radio-wrapper) {
  color: #000;
  font-size: 12px !important;
}

.drawActive {
  color: #02a7f0;
}
.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
}
@font-face {
  font-family: 'iconfont'; /* Project id 3579334 */
  src: url('//at.alicdn.com/t/c/font_3579334_do8ycgthtk.woff2?t=1665718126438') format('woff2'),
    url('//at.alicdn.com/t/c/font_3579334_do8ycgthtk.woff?t=1665718126438') format('woff'),
    url('//at.alicdn.com/t/c/font_3579334_do8ycgthtk.ttf?t=1665718126438') format('truetype');
}
/* 定位2 */
.icon-position2:before {
  content: '\e603';
}

/* 工人 */
.icon-worker:before {
  content: '\e609';
}
/* 男性 */
.icon-men:before {
  content: '\e6a6';
}
/* 女性 */
.icon-women:before {
  content: '\e6a7';
}

/* 左切换 */
.icon-left:before {
  content: '\e602';
}
/* 右切换 */
.icon-right:before {
  content: '\e601';
}

/* 编辑 */
.icon-edit:before {
  content: '\e6d5';
}

/* 删除 */
.icon-delet:before {
  content: '\e74b';
}

/* 圆圈 */
.icon-circle:before {
  content: '\ea97';
  margin-right: 6px;
}

/* 多边形 */
.icon-polygon:before {
  content: '\ed98';
  margin-right: 6px;
}

/* 正方形 */
.icon-squares:before {
  content: '\ea98';
  margin-right: 6px;
}

/* 播放 */
.icon-play:before {
  content: '\ea82';
}

/* 暂停 */
.icon-stop:before {
  content: '\e662';
}

/* 定位 */
.icon-position:before {
  content: '\e62f';
}
/* 轨迹 */
.icon-path:before {
  content: '\ea05';
}
</style>
