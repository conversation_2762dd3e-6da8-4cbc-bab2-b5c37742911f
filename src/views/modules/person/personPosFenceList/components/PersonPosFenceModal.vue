

<template>
  <j-modal
    :title="title"
    :width="1200"
    :visible="visible"
    :maskClosable="false"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel">
    <person-pos-fence-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"/>
    <template slot="footer" v-if="!disableSubmit">
      <a-button @click="handleOk">保存</a-button>
    </template>
  </j-modal>
</template>

<script>

  import PersonPosFenceForm from './PersonPosFenceForm'

  export default {
    name: 'PersonPosFenceModal',
    components: {
      PersonPosFenceForm
    },
    data() {
      return {
        title:'',
        width:800,
        visible: false,
        disableSubmit: false,
        issubmit: false,
      }
    },
    methods:{
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.issubmit = false
        this.$refs.realForm.handleOk();
      },
      handleSubmit() {
        this.issubmit = true
        this.$refs.realForm.handleOk()
      },
      submitCallback(data){
        if (this.issubmit) {
          if (data && data.id) this.$emit('submit', data)
        } else {
          this.$emit('ok')
        }
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>

<style scoped>
</style>