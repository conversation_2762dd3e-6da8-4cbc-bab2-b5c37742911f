<template>
  <div class="content">
    <!-- 左侧围栏 -->
    <div class="mr-20 bg-fa" :class="pagefrom == 'IsIframe' ? 'cssIsIframe' : 'cssNoIsIframe'">
      <div class="sider-menu position-relative" style="padding: 10px">
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item>
              <a-input-search :value="searchTrackword" placeholder="输入关键词进行检索 " @change="searchTrack" />
              <!-- <i class="icon" /> -->
            </a-form-item>
          </a-col>
        </a-row>
        <div>
          <div v-if="pagefrom != 'IsIframe'" class="addrail" @click="trackEditAdd('add')">
            <div>+</div>
          </div>
          <!-- 围栏列表 -->
          <div v-if="filterTeackList.length">
            <div
              v-for="(item, index) in filterTeackList"
              :key="index"
              class="railEdit"
              :class="index == current ? 'activeBgColor' : ''"
              @click="changerail(item.id, index)"
            >
              <div class="">{{ item.fenceName }}</div>
              <div class="edit" v-if="pagefrom != 'IsIframe'">
                <div
                  class="iconfont icon-edit"
                  style="margin-right: 15px"
                  :class="index == current ? 'activeIconEdit' : ''"
                  @click.stop="trackEditAdd(item)"
                ></div>
                <div
                  class="iconfont icon-delet"
                  :class="index == current ? 'activeIconEdit' : ''"
                  @click.stop="trackDel(item.id)"
                ></div>
              </div>
            </div>
          </div>
          <a-empty v-else />
        </div>
        <div class="position-absolute right-20" v-if="filterTeackList.length">
          <a-pagination
            :current="leftCurrent"
            :total="TrackTotal"
            :defaultPageSize="defaultPageSize"
            show-less-items
            @change="changePage"
          />
        </div>
      </div>
    </div>
    <!-- 右侧列表 -->
    <div class="right-container">
      <!-- 查询区域 -->
      <div class="search-container">
        <a-form layout="inline" @keyup.enter.native="getTrackPerson">
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-item label="姓名">
                <j-search-select-tag
                  placeholder="请选择姓名"
                  v-model="queryParam.userId"
                  dict="person_info,name,id"
                  style="width: 150px"
                />
              </a-form-item>
              <a-form-item label="单位类型">
                <j-dict-select-tag
                  style="width: 150px"
                  placeholder="请选择单位类型"
                  v-model="queryParam.unitType"
                  dictCode="unit_type"
                />
              </a-form-item>
              <a-form-item>
                <a-button style="margin-right: 8px" type="primary" @click="getTrackPerson" icon="search"
                  >查询
                </a-button>
                <a-button type="primary" @click="searchReset" icon="reload">重置 </a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- table区域-begin   :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" -->
      <div>
        <a-table
          ref="table"
          size="middle"
          bordered
          rowKey="id"
          class=""
          :scroll="{ y: 650 }"
          :columns="columns"
          :dataSource="dataSourceTest"
          :pagination="paginationtest"
          :loading="loading"
          @change="handleTableChange"
        >
          <template slot="htmlSlot" slot-scope="text">
            <div v-html="text"></div>
          </template>

          <template slot="photo" slot-scope="text">
            <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
            <img v-else :src="getImgView(text)" height="25px" alt="" />
          </template>

          <template slot="post_work" slot-scope="text, record">
            <span v-if="record.work && record.post">{{ (record.work, record.post) }}</span>
            <span v-if="record.work && !record.post">{{ record.work }}</span>
            <span v-if="!record.work && record.post">{{ record.post }}</span>
            <span v-if="!record.work && !record.post">暂无</span>
          </template>

          <template slot="personType" slot-scope="text">
            <!-- getFieldValue(personType) -->
            <span>暂未{{ text }}</span>
          </template>
        </a-table>
      </div>
      <mapModal ref="modalMap" @ok="getTrackList" />
    </div>
    <!-- 弹窗组件 -->
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, postAction, deleteAction } from '@/api/manage.js'
import mapModal from './components/mapModal'
import { getDictItemsFromCache } from '@/api/api'
import '@/assets/less/TableExpand.less'
export default {
  name: 'PersonPosFenceList',
  mixins: [JeecgListMixin],
  components: {
    mapModal,
  },
  props: {
    pagefrom: {
      default: '',
      type: String,
    },
  },
  data() {
    return {
      paginationtest: {
        defaultPageSize: 15,
      },
      searchTrackword: '', //电子围栏搜索
      defaultPageSize: 10,
      TrackTotal: 0,
      leftCurrent: 1,
      current: 0,
      description: '人员定位电子围栏信息表管理页面',
      accessControl: {
        tablename: 'PersonPosFence',
      },
      TeackList: [],
      filterTeackList: [], //过滤列表
      dataSourceTest: [],
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '照片',
          align: 'center',
          dataIndex: 'photo',
          width: 150,
          scopedSlots: {
            customRender: 'photo',
          },
        },
        {
          title: '人员姓名',
          align: 'center',
          dataIndex: 'name',
          width: 100,
        },
        {
          title: '手机号',
          align: 'center',
          dataIndex: 'number',
          width: 120,
        },
        {
          title: '单位类型',
          width: 200,
          align: 'center',
          dataIndex: 'unitType',
          customRender: (text) => {
            //字典值翻译通用方法
            let unit_type_text,
              dits = getDictItemsFromCache('unit_type')
            // console.log('字典解析', text, dits)
            dits.forEach((item) => {
              if (item.value == text) {
                unit_type_text = item.title
              }
            })
            return unit_type_text
          },
        },
        {
          title: '单位名称',
          align: 'center',
          dataIndex: 'unit',
        },
        {
          title: '工种/岗位',
          align: 'center',
          width: 120,
          dataIndex: 'post_work', //post_dictText
          scopedSlots: {
            customRender: 'post_work',
          },
          // customRender: (text) => {
          //   //字典值翻译通用方法
          //   if (text) {
          //     let post = getDictItemsFromCache('post')
          //     let work = getDictItemsFromCache('work')
          //     // return post[0].title + ',' + work[0].title
          //     return ''
          //   }
          // },
        },
        // {
        // 	title: '操作',
        // 	dataIndex: 'action',
        // 	align: "center",
        // 	fixed: "right",
        // 	width: 147,
        // 	scopedSlots: {
        // 		customRender: 'action'
        // 	},
        // }
      ],
      url: {
        listTrack: '/person/personPosFence/list', //新增围栏
        list: '/person/personPosFence/list', //新增围栏
        del: '/person/personPosFence/delete',
        trackPerson: '/personinfoapi/personLocation/queryList',
      },
    }
  },
  mounted() {
    this.getTrackList()
  },
  methods: {
    // 搜索电子围栏
    searchTrack(e) {
      const value = e.target.value
      this.searchTrackword = value
      this.filterTeackList = this.TeackList.filter((item) => item.fenceName.includes(value))
    },
    // 页面默认获取围栏列表
    getTrackList(pageNo = 1, pageSize = 10) {
      getAction(this.url.listTrack, {
        pageNo,
        pageSize,
      }).then((res) => {
        // console.log('请求电子围栏', res.result.records);
        if (res.success) {
          this.TeackList = res.result.records
          this.filterTeackList = this.TeackList // 过滤列表

          this.TrackTotal = res.result.total
          this.defaultPageSize = res.result.size
          this.queryParam.id = this.TeackList[0].id

          this.TeackList && this.getTrackPerson() //请求 在范围内的的人
        }
      })
    },

    // 切换围栏列表页
    changePage(pageNo, pageSize) {
      console.log('改变', pageNo)
      this.current = 0
      this.getTrackList(pageNo, pageSize)
    },
    // 编辑围栏
    trackEditAdd(item) {
      // item 是 'add' 代表的是新增 否则就 返回来编辑得对象
      console.log('编辑围栏', item)
      this.$refs.modalMap.title = item == 'add' ? '新增 - 电子围栏' : '编辑 - 电子围栏'
      this.$refs.modalMap.addOredit = item == 'add' ? 0 : 1
      this.$refs.modalMap.visible = true
      this.$refs.modalMap.animated = false
      if (item == 'add') {
        this.$nextTick(() => {
          this.$refs.modalMap.initMap()
          this.$refs.modalMap.add()
        })
        return
      }
      this.$nextTick(() => {
        this.$refs.modalMap.initMap()
        this.$refs.modalMap.edit(item)
      })
    },
    // 删除围栏
    trackDel(listid) {
      console.log('删除围栏', listid)
      let param = {
        id: listid,
      }
      deleteAction(this.url.del, param).then((res) => {
        res.success && this.getTrackList()
      })
    },

    // 围栏切换
    changerail(id, i) {
      console.log('点击第几个围栏', i)
      this.current = i
      this.queryParam.id = id
      this.getTrackPerson()
    },

    // 获取电子围栏下人员
    getTrackPerson() {
      console.log('params', this.queryParam)
      getAction(this.url.trackPerson, this.queryParam).then((res) => {
        console.log('请求人员', res)
        // 查询之后置空条件
        this.dataSourceTest = res.result
      })
    },
  },
}
</script>
<style scoped lang='less'>
@import '~@assets/less/common.less';
.cssIsIframe {
  height: 800px;
  width: 300px;
}
.cssNoIsIframe {
  height: 750px;
  width: 264px;
}

.content {
  display: flex;
  flex-direction: row;
}

.icon {
  display: inline-block;
  height: 16px;
  width: 16px;
  background-image: url('http://www.webkaka.com/tutorial/html/2022/0329245/search.png');
  background-repeat: no-repeat;
  position: absolute;
  top: 18%;
  left: 215px;
  z-index: 2;
}

.addrail {
  line-height: 50px;
  text-align: center;
  font-weight: 900;
  font-size: 20px;
  margin-bottom: 8px;
  border: 1px dashed #d7d7d7;
  width: 100%;
  height: 50px;
}

.addrail:hover {
  border: 1px dashed #0096ff;
  color: #0096ff;
}

.railEdit {
  display: flex;
  background-color: #f2f2f2;
  margin-bottom: 8px;
  padding: 0 10px;
  height: 50px;
  align-items: center;
  justify-content: space-between;

  .edit {
    display: flex;
  }
}

.railEdit:hover {
  background-color: #979797 !important;
  border: 1px #979797 solid;
  transform: scale(1.05);
  color: #fff;

  .edit {
    .iconfont {
      color: #fff;
    }
  }
}

.railEdit .icon-edit,
.icon-delet {
  color: #979797;
}

.activeBgColor {
  background-color: #02a7f0 !important;
  color: #fff !important;
}

.activeIconEdit {
  color: #fff !important;
}
.right-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  .search-container {
    margin-bottom: 10px;
    display: flex;
    flex-direction: row;
    height: 50px;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
