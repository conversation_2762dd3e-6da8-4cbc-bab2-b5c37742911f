<template>
  <div class="list-page person-info-list">
    <table-layout
      :search-props="searchProps"
      :table-props="tableProps"
      @search-submit="handleSearchSubmit"
      @table-change="handleSearchSubmit"
    >
    </table-layout>
    <mapModal ref="modalForm" @ok="modalFormOk"></mapModal>
  </div>
</template>

<script>
import { getAction, postAction, deleteAction } from '@/api/manage.js'
import mapModal from './components/mapModal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
export default {
  name: 'PersonInfoList',
  mixins: [JeecgTreeListMixin],
  components: {
    mapModal,
  },
  data() {
    return {
      searchData: {},
      // 搜索组件的props
      searchProps: {
        formModel: {
          searchTrackword: null,
        },
        formItems: [{ key: 'searchTrackword', label: '围栏名称', placeholder: '请输入围栏名称' }],
      },
      // 表格组件的props
      tableProps: {
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            },
          },
          {
            title: '照片',
            align: 'center',
            dataIndex: 'photo',
            width: 150,
            scopedSlots: {
              customRender: 'photo',
            },
          },
          {
            title: '人员姓名',
            align: 'center',
            dataIndex: 'name',
            width: 100,
          },
          {
            title: '手机号',
            align: 'center',
            dataIndex: 'number',
            width: 120,
          },
          {
            title: '单位类型',
            width: 200,
            align: 'center',
            dataIndex: 'unitType',
            customRender: (text) => {
              //字典值翻译通用方法
              let unit_type_text,
                dits = getDictItemsFromCache('unit_type')
              // console.log('字典解析', text, dits)
              dits.forEach((item) => {
                if (item.value == text) {
                  unit_type_text = item.title
                }
              })
              return unit_type_text
            },
          },
          {
            title: '单位名称',
            align: 'center',
            dataIndex: 'unit',
          },
          {
            title: '工种/岗位',
            align: 'center',
            width: 120,
            dataIndex: 'post_work', //post_dictText
            scopedSlots: {
              customRender: 'post_work',
            },
            // customRender: (text) => {
            //   //字典值翻译通用方法
            //   if (text) {
            //     let post = getDictItemsFromCache('post')
            //     let work = getDictItemsFromCache('work')
            //     // return post[0].title + ',' + work[0].title
            //     return ''
            //   }
            // },
          },
          // {
          // 	title: '操作',
          // 	dataIndex: 'action',
          // 	align: "center",
          // 	fixed: "right",
          // 	width: 147,
          // 	scopedSlots: {
          // 		customRender: 'action'
          // 	},
          // }
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],
      },

      url: {
        listTrack: '/person/personPosFence/list', //新增围栏
        list: '/person/personPosFence/list', //新增围栏
        del: '/person/personPosFence/delete',
        trackPerson: '/personinfoapi/personLocation/queryList',
      },
    }
  },
  created() {},
  mounted() {
    this.getTrackList()
  },
  computed: {},
  watch: {},
  methods: {
    modalFormOk() {
      const formModel = { ...this.searchData.formModel }
      delete formModel.date
      this.loadData(formModel)
    },

    handleSearchSubmit(searchData) {
      this.searchData = searchData || {}
      const formModel = { ...this.searchData.formModel }
      delete formModel.date
      this.loadData(formModel)
    },
    getTrackList(pageNo = 1, pageSize = 10) {
      getAction(this.url.listTrack, {
        pageNo,
        pageSize,
      }).then((res) => {
        // console.log('请求电子围栏', res.result.records);
        if (res.success) {
          this.TeackList = res.result.records
          this.filterTeackList = this.TeackList // 过滤列表

          this.TrackTotal = res.result.total
          this.defaultPageSize = res.result.size
          this.queryParam.id = this.TeackList[0].id

          this.TeackList && this.getTrackPerson() //请求 在范围内的的人
        }
      })
    },
  },
}
</script>
