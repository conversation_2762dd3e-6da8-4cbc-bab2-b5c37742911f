

<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :maskClosable="false"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
  >
    <device-info-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit" @changeWidth="changeWidth" />
    <template slot="footer" v-if="!disableSubmit">
      <a-button @click="handleOk">保存</a-button>
    </template>
  </j-modal>
</template>

<script>
import DeviceInfoForm from './DeviceInfoForm'

export default {
  name: 'DeviceInfoModal',
  components: {
    DeviceInfoForm,
  },
  data() {
    return {
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false,
      issubmit: false,
      isEdit: false,
    }
  },
  methods: {
    changeWidth(value) {
      this.isEdit = value
    },
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.issubmit = false
      this.$refs.realForm.handleOk()
    },
    handleSubmit() {
      this.issubmit = true
      this.$refs.realForm.handleOk()
    },
    submitCallback(data) {
      if (this.issubmit) {
        if (data && data.id) this.$emit('submit', data)
      } else {
        this.$emit('ok')
      }
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>

<style scoped>
</style>