<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <!-- 主表单区域 -->
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item label="设备名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['deviceName', validatorRules.deviceName]" placeholder="请输入设备名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="设备编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['deviceCode', validatorRules.deviceCode]" placeholder="请输入设备编号"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item class="position-relative" label="数据上传间隔" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                :formatter="(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="(value) => value.replace(/\$\s?|(,*)/g, '')"
                :precision="0"
                v-decorator="['gap']"
                step="0.5"
                :min="1"
                placeholder="请输入设备定位数据上传间隔"
                style="width: 100%"
              />
              <span class="position-absolute right-10">分钟</span>
            </a-form-item>
          </a-col>
          <!--          <div v-show="formDisabled">-->
          <!--            <a-col :span="12">-->
          <!--              <a-form-item label="设备电量" :labelCol="labelCol" :wrapperCol="wrapperCol">-->
          <!--                <a-input v-decorator="['electricity']" placeholder="请输入设备电量"></a-input>-->
          <!--              </a-form-item>-->
          <!--            </a-col>-->
          <!--            <a-col :span="12">-->
          <!--              <a-form-item label="网络状态" :labelCol="labelCol" :wrapperCol="wrapperCol">-->
          <!--                <j-dict-select-tag-->
          <!--                  type="list"-->
          <!--                  v-decorator="['isOnline']"-->
          <!--                  :trigger-change="true"-->
          <!--                  dictCode="network_status"-->
          <!--                  placeholder="请选择网络状态"-->
          <!--                />-->
          <!--              </a-form-item>-->
          <!--            </a-col>-->
          <!--            <a-col :span="12">-->
          <!--              <a-form-item label="使用状态" :labelCol="labelCol" :wrapperCol="wrapperCol">-->
          <!--                <j-dict-select-tag-->
          <!--                  type="list"-->
          <!--                  v-decorator="['isUsed']"-->
          <!--                  :trigger-change="true"-->
          <!--                  dictCode="use_status"-->
          <!--                  placeholder="请选择使用状态"-->
          <!--                />-->
          <!--              </a-form-item>-->
          <!--            </a-col>-->
          <!--          </div>-->
        </a-row>
      </a-form>
    </j-form-container>

    <a-row v-if="showFlowSubmitButton" style="text-align: center; width: 100%; margin-top: 16px"
    >
      <a-button @click="handleOk">提 交</a-button>
    </a-row
    >
  </a-spin>
</template>

<script>
import pick from 'lodash.pick'
import { getAction } from '@/api/manage'
import { JEditableTableMixin } from '@/mixins/JEditableTableMixin'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'DeviceInfoForm',
  mixins: [JEditableTableMixin],
  components: {
  },
  data() {
    return {
      // 新增时子表默认添加几行空数据
      addDefaultRowNum: 1,
      validatorRules: {
        deviceName: {
          rules: [{ required: true, message: '请输入设备名称!' }]
        },
        deviceCode: {
          rules: [
            { required: true, message: '请输入设备编号!' },
            {
              validator: (rule, value, callback) =>
                validateDuplicateValue('device_helmet', 'device_code', value, this.model.id, callback)
            }
          ]
        }
      },
      refKeys: ['personDeviceBind'],
      tableKeys: [],
      activeKey: 'personDeviceBind',
      // 设备绑定记录表
      personDeviceBindTable: {
        loading: false,
        dataSource: [],
        columns: []
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      url: {
        add: '/person/deviceHelmet/add',
        edit: '/person/deviceHelmet/edit',
        queryById: '/person/deviceHelmet/queryById',
        personDeviceBind: {
          list: '/person/deviceHelmet/queryPersonDeviceBindByMainId'
        }
      }
    }
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false
    },
    //表单模式：false流程表单 true普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      this.$emit('changeWidth', this.disabled)
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    addBefore() {
      this.form.resetFields()
    },
    getAllTable() {
      return new Promise((resolve) => {
        resolve([])
      })
    },
    /** 调用完edit()方法之后会自动调用此方法 */
    editAfter(record) {
      let fieldval = pick(this.model, 'deviceName', 'deviceCode', 'gap', 'electricity', 'isOnline', 'isUsed')
      this.$nextTick(() => {
        this.form.setFieldsValue(fieldval)
      })
      // 加载子表数据
      if (this.model.id) {
        let params = { id: this.model.id }
      }
    },
    /** 整理成formData */
    classifyIntoFormData(allValues) {
      let main = Object.assign(this.model, allValues.formValue)
      return {
        ...main, // 展开
      }
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    validateError(msg) {
      this.$message.error(msg)
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'deviceName', 'deviceCode', 'gap', 'electricity', 'isOnline', 'isUsed'))
    }
  }
}
</script>

<style scoped lang='less'>
// /deep/ .ant-input-number-handler-wrap {
//   display: none;
// }
</style>