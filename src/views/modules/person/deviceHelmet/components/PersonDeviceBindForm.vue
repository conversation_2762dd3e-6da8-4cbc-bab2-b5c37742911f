<template>
  <j-form-container :disabled="disabled">
    <a-form :form="form" slot="detail">
      <a-row>
        <a-col :span="12">
          <a-form-item label="绑定人员" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <j-search-select-tag v-decorator="['bindUserId']" dict="person_info,name,id" placeholder="请输入绑定人员" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="上次通讯时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <j-date
              placeholder="请选择上次通讯时间"
              v-decorator="['lastTime']"
              :dateFormat="dateFormat"
              :showTime="true"
              :trigger-change="true"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="上次定位地点" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input v-decorator="['lastLocation']" placeholder="请输入上次定位地点"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="设备标识" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input v-decorator="['deviceId']" placeholder="请输入设备标识"></a-input>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </j-form-container>
</template>
<script>
import pick from 'lodash.pick'
import { getAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'
import JSearchSelectTag from '@/components/dict/JSearchSelectTag.vue'

export default {
  name: 'PersonDeviceBindForm',
  components: {},
  props: {
    JSearchSelectTag,
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      dateFormat: 'YYYY-MM-DD HH:mm:ss',
      validatorRules: {},
      confirmLoading: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
    }
  },
  methods: {
    initFormData(url, id) {
      this.clearFormData()
      if (!id) {
        this.edit({})
      } else {
        getAction(url, { id: id }).then((res) => {
          if (res.success) {
            let records = res.result
            if (records && records.length > 0) {
              this.edit(records[0])
            }
          }
        })
      }
    },
    edit(record) {
      this.model = Object.assign({}, record)
      console.log('PersonDeviceBindForm-edit', this.model)
      let fieldval = pick(this.model, 'bindUserId', 'lastTime', 'lastLocation', 'deviceId')
      this.$nextTick(() => {
        this.form.setFieldsValue(fieldval)
      })
    },
    getFormData() {
      let formdata_arr = []
      this.form.validateFields((err, values) => {
        if (!err) {
          let formdata = Object.assign(this.model, values)
          let isNullObj = true
          Object.keys(formdata).forEach((key) => {
            if (formdata[key]) {
              isNullObj = false
            }
          })
          if (!isNullObj) {
            formdata_arr.push(formdata)
          }
        } else {
          this.$emit('validateError', '设备绑定记录表表单校验未通过')
        }
      })
      console.log('设备绑定记录表表单数据集', formdata_arr)
      return formdata_arr
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'bindUserId', 'lastTime', 'lastLocation', 'deviceId'))
    },
    clearFormData() {
      this.form.resetFields()
      this.model = {}
    },
  },
}
</script>
