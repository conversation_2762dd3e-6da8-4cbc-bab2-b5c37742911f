<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item label="设备编号">
              <a-input placeholder="请输入设备编号" v-model="queryParam.deviceCode"></a-input>
            </a-form-item>

            <a-form-item label="绑定人员">
              <j-search-select-tag
                placeholder="请选择绑定人员"
                v-model="queryParam.bindUserId"
                dict="person_info,name,id"
                style="width: 150px"
              />
            </a-form-item>

            <a-form-item label="网络状态">
              <j-dict-select-tag
                style="width: 100px"
                placeholder="请选择网络状态"
                v-model="queryParam.isOnline"
                dictCode="network_status"
              />
            </a-form-item>

            <template v-if="toggleSearchStatus">
              <a-form-item label="使用状态">
                <j-dict-select-tag
                  style="width: 100px"
                  placeholder="请选择使用状态"
                  v-model="queryParam.isUsed"
                  dictCode="use_status"
                />
              </a-form-item>
            </template>

            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置 </a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button
        :type="selectedRowKeys.length > 0 ? 'primary' : ''"
        @click="batchDel"
        v-has="accessControl.tablename + accessControl.batch_delete"
        >删除</a-button
      >
      <a-button @click="handleAdd('添加设备信息表')" type="primary" v-has="accessControl.tablename + accessControl.add">
        新增</a-button
      >
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        class=""
        :scroll="{ y: 500 }"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            height="25px"
            alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic"
          />
        </template>
        <template slot="isUsed_dictText" slot-scope="text">
          <a-button :class="text == '未绑' ? 'noactive' : 'active'" type="primary" size="small">
            {{ text }}
          </a-button>
        </template>

        <template slot="isOnline_dictText" slot-scope="text">
          <a-button :class="text == '在线' ? 'isOnlineactive' : 'notOnlinenoactive'" type="primary" size="small">
            {{ text == '在线' ? '在线' : '离线' }}
          </a-button>
        </template>

        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <template slot="action" slot-scope="text, record">
          <span class="flex justify-around">
            <a href="javascript:;" @click="handleDetail(record)">查看</a>
            <a @click="handleEdit(record)" v-has="accessControl.tablename + accessControl.edit">编辑</a>
            <a-popconfirm
              v-if="record.isUsed == 1"
              title="确定解绑吗?"
              @confirm="Unbindid(record.id)"
              v-has="accessControl.tablename + accessControl.delete"
            >
              <a>解绑</a>
            </a-popconfirm>
            <a v-else @click="isbind(record)">绑定</a>

            <a-popconfirm
              title="确定删除吗?"
              @confirm="handleDelete(record.id)"
              v-has="accessControl.tablename + accessControl.delete"
            >
              <a>删除</a>
            </a-popconfirm>
          </span>
        </template>
      </a-table>
    </div>
    <j-modal
      title="绑定人员"
      :width="1000"
      :visible="visible"
      :maskClosable="false"
      switchFullscreen
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form-item label="人员选择" :labelCol="labelCol" :wrapperCol="wrapperCol">
        <a-tree-select
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '600px', overflow: 'auto', width: '100px' }"
          placeholder="请选择人员"
          allow-clear
          :tree-data="treeData"
          treeNodeFilterProp="title"
          @change="nodeInfo"
          :replaceFields="replaceFields"
        >
        </a-tree-select>
      </a-form-item>

      <template slot="footer">
        <a-button @click="handleOk">保存</a-button>
      </template>
    </j-modal>
    <device-info-modal ref="modalForm" @ok="modalFormOk" />
  </a-card>
</template>

<script>
import { getAction } from '@/api/manage.js'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import DeviceInfoModal from './components/DeviceInfoModal'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'
import '@/assets/less/TableExpand.less'
import JSearchSelectTag from '@/components/dict/JSearchSelectTag.vue'

export default {
  name: 'DeviceInfoList',
  mixins: [JeecgListMixin],
  components: {
    DeviceInfoModal,
    JSearchSelectTag,
  },
  data() {
    return {
      userId: '',
      businessId: 0, //业务id
      treeData: [],
      visible: false,
      description: '设备信息表管理页面',
      accessControl: {
        tablename: 'DeviceInfo',
      },
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '设备名称',
          align: 'center',
          dataIndex: 'deviceName',
          width: 120,
        },
        {
          title: '设备编号',
          align: 'center',
          dataIndex: 'deviceCode',
          width: 180,
        },
        {
          title: '电量(% )',
          align: 'center',
          dataIndex: 'electricity',
          width: 90,
        },
        {
          title: '网络状态',
          align: 'center',
          dataIndex: 'isOnline_dictText',
          width: 90,
          scopedSlots: {
            customRender: 'isOnline_dictText',
          },
        },
        {
          title: '使用状态',
          align: 'center',
          dataIndex: 'isUsed_dictText',
          width: 90,
          scopedSlots: {
            customRender: 'isUsed_dictText',
          },
        },
        {
          title: '上传定位地点',
          align: 'center',
          dataIndex: 'lastLocation',
        },
        {
          title: '上次通讯时间',
          align: 'center',
          dataIndex: 'lastTime',
          width: 180,
        },
        {
          title: '绑定人员',
          align: 'center',
          dataIndex: 'bindUserId_dictText',
          width: 90,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 190,
          scopedSlots: {
            customRender: 'action',
          },
        },
      ],
      url: {
        list: '/person/deviceHelmet/list',
        delete: '/person/deviceHelmet/delete',
        deleteBatch: '/person/deviceHelmet/deleteBatch',
        exportXlsUrl: '/person/deviceHelmet/exportXls',
        importExcelUrl: 'person/deviceHelmet/importExcel',
        unBind: '/person/deviceHelmet/unBind', // 解绑设备
        queryTreeData: '/personinfoapi/deptApi/getDeptPerson', //获取人员
        bind: '/person/deviceHelmet/bind',
      },
      replaceFields: {
        children: 'children',
        title: 'name',
        key: 'id',
        value: 'id',
      },
      labelCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 6,
        },
      },
      wrapperCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 16,
        },
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {},
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    isbind(record) {
      console.log('record', record)
      this.userId = ''
      this.businessId = ''
      this.businessId = record.id
      this.loadTreeData()
    },

    nodeInfo(value, label, extra) {
      this.userId = value
    },
    handleOk() {
      let params = {
        id: this.businessId,
        userId: this.userId,
      }
      getAction(this.url.bind, params).then((res) => {
        if (res.success) {
          console.log('绑定成功', res)
          this.visible = false
          this.loadData()
        }
      })
    },

    // 左侧树刷新数据
    loadTreeData() {
      getAction(this.url.queryTreeData).then((res) => {
        if (res.success) {
          this.treeData = res.result
          this.visible = true
        }
      })
    },

    handleCancel() {
      this.visible = false
    },

    Unbindid(id) {
      getAction(this.url.unBind, {
        id,
      }).then((res) => {
        console.log('解绑请求', res)
        if (res.success) {
          this.loadData()
        }
      })
    },

    initDictConfig() {},
  },
}
</script>
<style scoped lang="less">
@import '~@assets/less/common.less';

.active {
  background-color: #e2fcbb;
  color: #80a743;
  cursor: auto !important;
  border: 1px solid #9cbb6c !important;
}

.noactive {
  background-color: #f5bac1;
  color: red;
  cursor: auto !important;
  border: 1px solid #e86a7a !important;
}

.isOnlineactive {
  background-color: #bae7fb;
  color: #2690d8;
  cursor: auto !important;
  border: 1px solid #8fc8ec !important;
}

.notOnlinenoactive {
  background-color: #e9e9e9;
  color: #9a9a9a;
  cursor: auto !important;
  border: 1px solid #dcdcdc !important;
}
.table-page-search-wrapper {
  .ant-form-inline {
    .ant-row {
      .ant-col {
        &.ant-col-24 {
          display: flex;

          .ant-form-item {
            display: flex;
            margin-bottom: 24px;
            margin-right: 10px;

            .ant-form-item-control-wrapper {
              flex: 1 1;
              display: inline-block;
              vertical-align: middle;

              // (这里有更多嵌套样式...)
            }

            .ant-form-item-control {
              height: 32px;
              line-height: 32px;
            }

            &.marginRight0 {
              margin-right: 0px;
            }
          }
        }
      }
    }
  }

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
    margin-left: 10px;
  }
}
</style>
