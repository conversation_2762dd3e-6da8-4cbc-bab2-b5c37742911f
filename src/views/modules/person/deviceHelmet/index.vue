<template>
  <div class="list-page person-info-list">
    <table-layout
      :search-props="searchProps"
      :table-props="tableProps"
      @search-submit="handleSearchSubmit"
      @table-change="handleSearchSubmit"
    >
    </table-layout>
    <DeviceInfoModal ref="modalForm" @ok="modalFormOk"></DeviceInfoModal>
  </div>
</template>

<script>
import { getAction } from '@/api/manage.js'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'

import DeviceInfoModal from './components/DeviceInfoModal'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'
import '@/assets/less/TableExpand.less'
import JSearchSelectTag from '@/components/dict/JSearchSelectTag.vue'

export default {
  name: 'DeviceInfoList',
  mixins: [JeecgTreeListMixin],
  components: {
    DeviceInfoModal,
    JSearchSelectTag
  },
  data() {
    return {
      searchData: {},
      searchProps: {
        formModel: {
          name: null,
          bindUserId: null
        },
        formItems: [
          { key: 'deviceCode', label: '设备编号', placeholder: '请输入设备编号' },
          {
            key: 'bindUserId',
            label: '绑定人员',
            type: 'list',
            dictCode: 'person_info,name,id',
            placeholder: '请选择绑定人员',
            allowClear: true
          }
        ]
      },
      userId: '',
      businessId: 0, //业务id
      treeData: [],
      visible: false,
      description: '设备信息表管理页面',
      accessControl: {
        tablename: 'DeviceInfo'
      },
      tableProps: {
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0
        },
        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: function(t, r, index) {
              return parseInt(index) + 1
            }
          },
          {
            title: '设备名称',
            align: 'center',
            dataIndex: 'deviceName',
            width: 120
          },
          {
            title: '设备编号',
            align: 'center',
            dataIndex: 'deviceCode',
            width: 180
          },
          {
            title: '电量(% )',
            align: 'center',
            dataIndex: 'electricity',
            width: 90
          },
          {
            title: '网络状态',
            align: 'center',
            dataIndex: 'isOnline_dictText',
            width: 90,
            scopedSlots: {
              customRender: 'isOnline_dictText'
            }
          },
          {
            title: '使用状态',
            align: 'center',
            dataIndex: 'isUsed_dictText',
            width: 90,
            scopedSlots: {
              customRender: 'isUsed_dictText'
            }
          },
          {
            title: '上传定位地点',
            align: 'center',
            dataIndex: 'lastLocation'
          },
          {
            title: '上次通讯时间',
            align: 'center',
            dataIndex: 'lastTime',
            width: 180
          },
          {
            title: '绑定人员',
            align: 'center',
            dataIndex: 'bindUserId_dictText',
            width: 90
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 190,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],

        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail
          },
          {
            text: '编辑',
            handler: this.handleEdit
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete
          }
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd
          }
        ]
      },
      // 表头

      url: {
        list: '/person/deviceHelmet/list',
        delete: '/person/deviceHelmet/delete',
        deleteBatch: '/person/deviceHelmet/deleteBatch',
        exportXlsUrl: '/person/deviceHelmet/exportXls',
        importExcelUrl: 'person/deviceHelmet/importExcel',
        unBind: '/person/deviceHelmet/unBind', // 解绑设备
        queryTreeData: '/personinfoapi/deptApi/getDeptPerson', //获取人员
        bind: '/person/deviceHelmet/bind'
      },
      replaceFields: {
        children: 'children',
        title: 'name',
        key: 'id',
        value: 'id'
      },
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 6
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      dictOptions: {},
      superFieldList: []
    }
  },
  created() {
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    modalFormOk() {
      const formModel = { ...this.searchData.formModel }
      delete formModel.date
      this.loadData(formModel)
    },

    handleSearchSubmit(searchData) {
      this.searchData = searchData || {}
      const formModel = { ...this.searchData.formModel }
      delete formModel.date
      this.loadData(formModel)
    },
    isbind(record) {
      console.log('record', record)
      this.userId = ''
      this.businessId = ''
      this.businessId = record.id
      this.loadTreeData()
    },

    nodeInfo(value, label, extra) {
      this.userId = value
    },
    handleOk() {
      let params = {
        id: this.businessId,
        userId: this.userId
      }
      getAction(this.url.bind, params).then((res) => {
        if (res.success) {
          console.log('绑定成功', res)
          this.visible = false
          this.loadData()
        }
      })
    },

    // 左侧树刷新数据
    loadTreeData() {
      getAction(this.url.queryTreeData).then((res) => {
        if (res.success) {
          this.treeData = res.result
          this.visible = true
        }
      })
    },

    handleCancel() {
      this.visible = false
    },

    Unbindid(id) {
      getAction(this.url.unBind, {
        id
      }).then((res) => {
        console.log('解绑请求', res)
        if (res.success) {
          this.loadData()
        }
      })
    },

    initDictConfig() {
    }
  }
}
</script>
<style scoped lang="less">
// @import '~@assets/less/common.less';

// .active {
//   background-color: #e2fcbb;
//   color: #80a743;
//   cursor: auto !important;
//   border: 1px solid #9cbb6c !important;
// }

// .noactive {
//   background-color: #f5bac1;
//   color: red;
//   cursor: auto !important;
//   border: 1px solid #e86a7a !important;
// }

// .isOnlineactive {
//   background-color: #bae7fb;
//   color: #2690d8;
//   cursor: auto !important;
//   border: 1px solid #8fc8ec !important;
// }

// .notOnlinenoactive {
//   background-color: #e9e9e9;
//   color: #9a9a9a;
//   cursor: auto !important;
//   border: 1px solid #dcdcdc !important;
// }
// .table-page-search-wrapper {
//   .ant-form-inline {
//     .ant-row {
//       .ant-col {
//         &.ant-col-24 {
//           display: flex;

//           .ant-form-item {
//             display: flex;
//             margin-bottom: 24px;
//             margin-right: 10px;

//             .ant-form-item-control-wrapper {
//               flex: 1 1;
//               display: inline-block;
//               vertical-align: middle;

//               // (这里有更多嵌套样式...)
//             }

//             .ant-form-item-control {
//               height: 32px;
//               line-height: 32px;
//             }

//             &.marginRight0 {
//               margin-right: 0px;
//             }
//           }
//         }
//       }
//     }
//   }

//   .table-page-search-submitButtons {
//     display: block;
//     margin-bottom: 24px;
//     white-space: nowrap;
//     margin-left: 10px;
//   }
// }
</style>
