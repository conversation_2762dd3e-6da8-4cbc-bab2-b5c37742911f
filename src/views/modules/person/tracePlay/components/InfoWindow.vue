
<template>
  <div>
    <div class="bg-fff p-10" style="width: max-content">
      <div class="flex">
        <div class="img mr-10 w-80 h-120">
          <img src="http://img.daimg.com/uploads/allimg/220827/1-220RGR038.jpg" width="80" height="120" />
        </div>
        <div>
          <div>姓名：{{ infoWindowcontent.name }}</div>
          <div class="text-ellipsis w-150">部门：{{ infoWindowcontent.departmentName }}</div>
          <div>工种/岗位：{{ infoWindowcontent.work }}</div>
          <div>电话：{{ infoWindowcontent.number }}</div>
          <!-- <div>位置：经{{ infoWindowcontent.location.split(',')[0] }}</div>
          <div class="pl-42">纬 {{ infoWindowcontent.location.split(',')[1] }}</div> -->
        </div>
      </div>
      <div class="mt-10 flex align-center">
        <span>轨迹查询</span>
        <div class="px-10">
          <DatePickerLv style="width: 130px" :id="infoWindowcontent.id" @ok="ok" :totoday="pickerDate"></DatePickerLv>
        </div>
        <!-- <input id="input" placeholder="输入关键词进行检索 " type="date" /> -->
        <a-button id="button" type="primary" @click="search">查询</a-button>
      </div>
    </div>
  </div>
</template>
<script>
import DatePickerLv from './DatepickerLv.vue'
export default {
  components: { DatePickerLv },
  props: {
    infoWindow: {
      type: Object,
      default: () => {},
    },
    infoWindowcontent: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      pickerDate: '',
    }
  },
  methods: {
    // 关闭
    close() {
      // 高德地图信息窗关闭的api
      this.infoWindow.close()
    },
    edit() {
      console.log('编辑按钮测试')
    },
    del() {
      console.log('删除按钮测试')
    },
    ok(value) {
      this.datePicker = value
    },
    search() {
      this.$emit('datePicker', this.datePicker)
    },
  },
}
</script>
 
<style lang="css" scoped>
#del-div {
  position: absolute;
  right: 20;
  top: 20;
  transform: scale(1.2);
}
</style>
