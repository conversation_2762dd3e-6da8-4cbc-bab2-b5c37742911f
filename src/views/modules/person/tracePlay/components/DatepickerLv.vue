<template>
  <div>
    <div>
      <a-date-picker
        @openChange="openChange"
        :disabledDate="disabledDate"
        @change="choseDate"
        v-model="today"
        :allowClear="false"
        style="width: 130px"
      >
      </a-date-picker>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { getAction } from '@/api/manage.js'

export default {
  props: {
    id: {
      type: [Number, String],
      default: 0,
    },
    totoday: {
      type: String,
      default: '2022-10-01',
    },
    open: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      today: '',
      yearMonth: '',
      month: '',
      year: '',
      newDateArr: [], //有数据得日期
      indexTest: 0,
      realDay: {
        year: '',
        month: '',
      },
      url: {
        queryMonthById: '/personinfoapi/personLocation/queryMonthById',
      },
    }
  },
  mounted() {
    this.getDay()
  },
  watch: {
    totoday: {
      handler: function (newvalue, old) {
        this.today = newvalue
        let test = newvalue.split('-')
        this.year = test[0]
        this.month = test[1]
        this.yearMonth = test[0] + '-' + test[1]
        console.log('传进来的日期', this.today, this.yearMonth)
      },
      // immediate: true,
    },
  },

  methods: {
    moment,
    // 获取今日
    getDay() {
      let date = new Date()
      this.year = date.getFullYear()
      this.realDay.year = date.getFullYear()
      this.month = date.getMonth() + 1
      this.realDay.month = date.getMonth() + 1
      let day = date.getDate()
      this.yearMonth = this.year + '-' + this.month

      // this.today = this.yearMonth + '-' + day
    },

    disabledDate(current) {
      return (
        (current && current < moment({ year: this.year, month: this.month - 1 }).startOf('month')) ||
        current > moment({ year: this.year, month: this.month - 1 }).endOf('month') ||
        eval(this.ruleDay()) ||
        current > moment({ year: this.realDay.year, month: this.realDay.month - 1 }).endOf('month') ||
        current < moment({ year: 2022, month: 8 }).startOf('month')
      ) //字符串转成js 代码运行
    },
    // 把禁用的日期找出来
    ruleDay(e) {
      console.log('index', this.indexTest)
      this.indexTest == 0 && this.testopenChange()
      this.indexTest = 1
      let DateArr = this.newDateArr
      let aa = ''
      DateArr.forEach((item, index) => {
        if (index == 0) {
          aa = `${aa}current.valueOf() === this.moment('${item}').valueOf()`
        } else {
          aa = `${aa}|| current.valueOf() === this.moment('${item}').valueOf()`
        }
      })
      // console.log('语法', aa)
      return aa
    },
    // 控制禁用
    testopenChange(status) {
      // this.indexTest = this.indexTest + 1
      let params = {
        id: this.id,
        date: this.yearMonth,
      }
      console.log('请求禁用时间参数', params)
      getAction(this.url.queryMonthById, params).then((res) => {
        console.log('含有数据的日期', res.result.data)
        // 更改日期
        let momentArr = []
        if (res.result.data.at() > 0) {
          this.today = this.yearMonth + '-' + res.result.data.at(-1)
          // console.log('禁用时间*******', this.today)
          if (res.success) {
            let momment = moment({ year: this.year, month: this.month - 1 }).daysInMonth()
            for (let i = 1; i <= momment; i++) {
              momentArr.push(`${this.year}/${this.month}/${i < 10 ? '0' + i : i}`)
            }
            // console.log('完整、momentArr', momentArr)
            res.result.data.forEach((item) => {
              // 如果这个日期是在里面就从数组中删除数据
              if (momentArr.indexOf(`${this.year}/${this.month}/${item}`) != -1) {
                momentArr.splice(momentArr.indexOf(`${this.year}/${this.month}/${item}`), 1)
              }
            })
          }
        } else {
          let momment = moment({ year: this.year, month: this.month - 1 }).daysInMonth()
          for (let i = 1; i <= momment; i++) {
            momentArr.push(`${this.year}/${this.month}/${i < 10 ? '0' + i : i}`)
          }
        }
        console.log('禁用日期', momentArr)
        this.newDateArr = momentArr
      })
    },

    // 展开日期组件 需要做的事情是 1.把顶部的可以输入禁用 2.请求数据 禁用处理
    openChange(e) {
      console.log('展开关闭日期', e)
      this.datePanelChange(e) // 监听到年月发生了变化 this.yearMonth变化
      setTimeout(() => {
        let setDisabled = document.getElementsByClassName('ant-calendar-input')
        e ? setDisabled[0].setAttribute('disabled', true) : setDisabled[0].removeAttribute('disabled')
      }, 200)
    },

    // 日期组件 切换月份  年份  被触发
    datePanelChange(isShow) {
      // console.log('当前月份', moment().startOf('month'))
      console.log('月份发生变化', this.yearMonth)
      if (isShow) {
        setTimeout(() => {
          const dateDom = document.querySelector('.ant-calendar-ym-select')
          dateDom.addEventListener('DOMCharacterDataModified', () => {
            this.year = document.querySelector('.ant-calendar-year-select').innerText.replace('年', '')
            let month = document.querySelector('.ant-calendar-month-select').innerText.replace('月', '')
            this.month = month < 10 ? '0' + month : month
            let yearMonth = this.year + '-' + this.month
            this.indexTest = 0
            this.yearMonth = yearMonth
            if (this.yearMonth != yearMonth) {
            }
          })
        }, 200)
      }
    },

    // 选定日期
    choseDate(e) {
      let date = new Date(e._d)
      // var date = new Date(timestamp) //时间戳为10位需*1000，时间戳为13位的话不需乘1000
      var Y = date.getFullYear() + '-'
      var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
      var D = date.getDate() + ' '
      let day = Y + M + D
      this.today = day
      console.log('子组件选定日期', this.today)
      // 这里需要  把事件传递出去
      this.$emit('ok', this.today)
    },
  },
}
</script>

<style>
</style>