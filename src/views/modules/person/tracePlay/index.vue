<template>
  <div class="h-100pre position-relative">
    <div class="bg-fff w-320 position-absolute index-1000 left-16 top-16 over-auto font-14 p-10" style="height: 50%">
      <a-tree
        v-if="treeData.length > 0"
        default-expand-all
        :show-line="false"
        :showIcon="true"
        :tree-data="treeData"
        @select="nodeSelect"
        :replaceFields="{ children: 'children', title: 'name', key: 'personId', value: 'personId' }"
        :defaultSelectedKeys="defaultSelectedKeys"
        :defaultExpandedKeys="defaultExpandedKeys"
        @expand="onExpand"
      >
        <a-icon slot="switcherIcon" type="down" />
        <template slot="handle" slot-scope="item">
          <span>{{
            item.children ? `${item.name}(${item.children.length})人` : `${item.personName}(${item.puname})`
          }}</span>
        </template>
      </a-tree>
    </div>
    <Map :defaultLegend="false" :defaultTypeChange="false" ref="Map" @initMap="initMap"></Map>
  </div>
</template>

<script>
import { getData, saveData } from '@/utils/geoJson.js'
import { getAction } from '@/api/manage.js'
import Map from '@/components/Map'

import DatePickerLv from './components/DatepickerLv.vue'

import InfoWindow from './components/InfoWindow'
import moment from 'dayjs'
export default {
  name: 'Article',
  components: { InfoWindow, DatePickerLv, Map },
  props: {
    pagefrom: {
      default: '',
      type: String,
    },
  },

  data() {
    return {
      showInfoWindow: false,
      infoWindowcontent: {},
      infoWindow: {}, //页面初始化 点击的时候的窗体

      showDate: false, //右上角的日期展示
      circleArr: [], //存放圆形实例
      expandedKeys: [],
      autoExpandParent: true,
      dataList: [],
      filterTreeNode: '',
      searchValue: '',
      value1: '',
      isMove: false,
      showTrack: false, //是否展示 播放轨迹
      speed: 1000,
      speedArr: ['倍速8', '倍速4', '倍速2', '倍速1'],
      defauleSpeed: '倍速1',
      inputValue: 0,
      slidermax: 0,
      totalTime: '00:00:00',
      test: '',
      isClick: true,
      // 默认展开的树节点
      defaultExpandedKeys: [],
      // 默认选中的树节点
      defaultSelectedKeys: [],
      treeData: [],
      personList: [],
      // 地图变量
      map: '',
      contextMenu: '', //右键菜单
      marker: '',
      geojson: '', //持久化缓存
      markerIcon: '', //自定义图标
      newmarkerIcon: '', // 高亮
      passedPolyline: '', //通过的线路 实例
      passedPath: '', //通过的线路 数据
      passedPathStart: '', // //重绘通过路径轨迹的集合
      Lastitme: '', // 上一个点击高亮的图标
      showPolyline: '',
      maList: [],
      pathArr: [], //元数据
      runMarker: '', //轨迹回放次奥测
      playEnd: false, //代表还没有播放到结尾
      isPlay: false, //状态 播放 / 暂停
      changecount: 0, //切换过倍速
      stopcount: 0, //点击过暂停按钮
      pathArrPlony: [], //多边形
      url: {
        queryTreeData: '/person/personInfo/location/list',
        listTrack: '/person/personPosFence/list',
        queryNowPerson: '/personinfoapi/personLocation/queryNowPerson', //
        queryById: 'personinfoapi/personLocation/queryById', //获取人员当天的轨迹
        queryLately: '/personinfoapi/personLocation/queryLately', //返回数据  当天没有就返回最近一天的数据
        queryMonthById: '/personinfoapi/personLocation/queryMonthById', //获取  有数据得当月日期
      },

      drawConfig: {
        strokeColor: '#02a7f0', //描边
        strokeWeight: 2, //线宽
        strokeOpacity: 1, //描边透明度
        fillColor: '#BFDDFD', //填充
        fillOpacity: 0.5,
        strokeStyle: 'dashed', // 线样式还支持  dotted solid double dashed
        // strokeDasharray: [30,10],
      },
      baseUrl: window._CONFIG['staticDomainURL'],
      today: '2022-10-01',
      usename: '',
      id: '', //点击轨迹的时候 这个人的id
    }
  },
  watch: {
    personList() {
      this.setPersonLocation()
    },
  },

  mounted() {
    this.loadTreeData()
  },
  methods: {
    // 左侧树刷新数据
    loadTreeData() {
      getAction(this.url.queryTreeData).then((res) => {
        this.personList = res.result || []
        this.treeData = [
          { name: '人员列表', personId: '0', children: [], scopedSlots: { title: 'handle' } },
          { name: '违规人员列表', personId: '1', children: [], scopedSlots: { title: 'handle' } },
        ]
        this.personList.forEach((x) => {
          let index = 0
          if (!x.isAssessed || !x.isRegistered || x.isInFence) index = 1
          else index = 0
          this.treeData[index].children.push({ ...x, scopedSlots: { title: 'handle' } })
        })
      })
    },
    initMap() {
      this.setPersonLocation()
    },
    setPersonLocation() {
      if (this.personList.length <= 0 || !this.$refs.Map.map) return
      this.personList.forEach((person) => {
        // 判断是否违规
        let type = !person.isAssessed || !person.isRegistered || person.isInSafeFence || !person.isInWorkFence ? 1 : 0
        var icon = new T.Icon({
          iconUrl: require('@/assets/map/img/person.png'),
          iconSize: new T.Point(19, 27),
          iconAnchor: new T.Point(10, 25),
        })
        //向地图上添加自定义标注
        var marker = new T.Marker(new T.LngLat(person.longitude, person.latitude), { icon: icon })
        this.$refs.Map.map.addOverLay(marker)
        var infoWin1 = new T.InfoWindow()
        let violationOfRules = []
        if (!person.isAssessed) violationOfRules.push('未通过安全培训')
        if (!person.isRegistered) violationOfRules.push('未登记')
        if (!person.isInWorkFence) violationOfRules.push('超出施工围栏')
        if (person.isInSafeFence) violationOfRules.push('进入安全围栏')
        var sContent = `<div>
          <div>设备编号：${person.puname}</div>
          <div>人员：${person.personName}</div>
          <div>位置：${person.position ? person.position : ''}</div>
          <div style="display${type == 0 ? 'none' : ''}">进场时间：${person.enterDate ? person.enterDate : ''}</div>
          <div style="display${!person.latestViolation ? 'none' : ''}">违规时间：${moment(person.gpstime * 1000).format(
          'YYYY-MM-DD HH:mm:ss'
        )}</div>
          <div style="display${type == 0 ? '' : 'none'}">违规类型：${violationOfRules.join('、')}</div>
          </div>`
        infoWin1.setContent(sContent)
        marker.addEventListener('click', function () {
          marker.openInfoWindow(infoWin1)
        })
      })
    },

    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },

    getParentKey(key, tree) {
      let parentKey
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.children) {
          if (node.children.some((item) => item.key === key)) {
            parentKey = node.key
          } else if (this.getParentKey(key, node.children)) {
            parentKey = this.getParentKey(key, node.children)
          }
        }
      }
      console.log('parentKey', parentKey)
      return parentKey
    },

    //树 搜索
    onChange(e) {
      const value = e.target.value
      if (!value) {
        this.searchValue = value
        this.autoExpandParent = false
        this.expandedKeys = []
        return
      }
      const expandedKeys = this.dataList
        .map((item) => {
          if (item.title.indexOf(value) > -1) {
            console.log('包含的有 ')
            return this.getParentKey(item.key, this.treeData)
          } else {
            return null
          }
        })
        .filter((item, i, self) => item && self.indexOf(item) === i)
      this.searchValue = value
      this.autoExpandParent = true
      this.expandedKeys = expandedKeys
    },

    // 点击树节点触发
    nodeSelect(val) {
      const person = this.personList.find((x) => x.personId === val[0])
      this.$refs.Map.map.centerAndZoom(new T.LngLat(person.longitude, person.latitude), 11)
    },
  },
}
</script>

<style lang="less" scoped></style>
