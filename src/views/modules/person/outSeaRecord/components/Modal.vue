<template>
  <j-modal
    :title="title"
    :width="width"
    :bodyStyle="{height:'60vh' , overflow:'auto'}"
    :visible="visible"
    :maskClosable="false"
    switchFullscreen
    @ok="handleOk"
    :footer="false"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <data-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></data-form>
    <template slot="title">
      <div class="flex align-center">
        <div class="line1 mr-8"></div>
        <div class="home" style="color: #444; font-weight: bolder">{{ title }}</div>
      </div>
    </template>
  </j-modal>
</template>

<script>
import DataForm from './record'
export default {
  name: 'seaModal',
  components: {
    DataForm,
  },
  data() {
    return {
      title: '出海状态异常人员处理记录',
      width: 1200,
      hight: 800,
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit()
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style scoped>
.line1 {
  width: 3px;
  height: 10px;
  background: #3254ff;
}
</style>