<template>
  <div class="list-page">
    <!-- <table-layout
    :table-props="tableProps"
    >

    </table-layout> -->
    <!-- :pagination="ipagination" -->
    <a-table
      :columns="tableProps.columns"
      :data-source="tableProps.dataSource"
      bordered
      :rowClassName="rowClassName"
      size="small"
      :scroll="{y: 430 }"
    >
      <template #result="text">
        <span>{{ getMessage(text) }}</span>
      </template>
      <template #remark="text">
        <a-tooltip>
          <template slot="title"> {{text}} </template>
          <span class="text-ellipsis-clamp"
            >{{text}}</span
          >
        </a-tooltip>
      </template>
    </a-table>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
export default {
  name: 'exceptionRecord',
  data() {
    return {
      rowClassName: Object,
      url: {
        exceptionList: '/sailingcheck/userSailingRecord/exceptionList',
      },
      params: {
        current: 2,
        pageSize: 10,
      },
      // columns,
      // 表格组件的props
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '50', '100'],
        showTotal: (total) => {
          return ' 共' + total + '条'
        },
        showSizeChanger: true,
        total: 0,
      },
      tableProps: {
        dataSource: [],

        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 50,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '姓名',
            width: 80,
            align: 'center',
            dataIndex: 'realName',
          },
          {
            title: '单位',
            align: 'center',
            width: 250,
            dataIndex: 'departmentName',
          },
          //   {
          //     title: '所乘船舶',
          //     align: 'center',
          //     dataIndex: 'shipName',
          //   },
          {
            title: '手机号码',
            align: 'center',
            width: 120,
            dataIndex: 'phone',
          },
          {
            title: '处理原因',
            align: 'center',
            width: 120,
            dataIndex: 'result',
            scopedSlots: { customRender: 'result' },
          },
          {
            title: '处理人',
            align: 'center',
            width: 80,
            dataIndex: 'disposeUserName',
          },
          {
            title: '处理时间',
            align: 'center',
            width: 160,
            dataIndex: 'updateTime',
            customRender: function (t, r, index) {
              return !t ? '' : t.length > 10 ? t.substr(0, 16) : t
            },
          },
          {
            title: '备注',
            dataIndex: 'remark',
            align: 'center',
            width: 240,
            scopedSlots: { customRender: 'remark' },
          },
        ],
      },
    }
  },
  methods: {
    getMessage(record) {
      let message
      switch (record) {
        case '1':
          console.log('已转乘其他船舶', record)
          message = '已转乘其他船舶'
          break
        case '2':
          message = '已下船，未打卡'
          break
        case '3':
          message = '人员失踪'
          break
        case '4':
          message = '其他'
          break
      }
      return message
    },
    submitForm() {},
    edit() {
      getAction(this.url.exceptionList, this.params).then((res) => {
        console.log('异常信息', res)
        const { success, result } = res
        if (success) {
          this.tableProps.dataSource = res.result
        }
      })
    },
  },
  rowClassName(record, index) {
    return this.useZebraStripes ? (index % 2 === 1 ? 'zebra-row' : '') : ''
  },
}
</script>
<style lang="less" scoped>
::v-deep .ant-spin-container {
  .ant-table-tbody > tr.zebra-row {
    background: #eff5ff;
  }
  .ant-table-header {
    width: calc(100% + 9px);
  }
  .ant-table-thead {
    tr > th {
      background: #066fec !important;
      color: #ffffff;
    }
  }
}
::v-deep .ant-table-small > .ant-table-content > .ant-table-body {
  margin: 0 0px;
}
</style>