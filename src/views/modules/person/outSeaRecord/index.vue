<template>
  <left-right-layout :spanConfig="{ left: 6, right: 18 }">
    <template #left>
      <div class="left-card flex flex-column py-10 px-16">
        <!-- 出航总览 -->
        <div class="h-150 mb-20" style="border-bottom: 1px solid #e5e6eb">
          <div class="flex align-center">
            <div class="h-10 w-3 mr-5" style="background-color: #3254ff"></div>
            <div class="font-14" style="font-weight: 800">出航总览</div>
          </div>
          <div class="flex h-48 justify-between align-center mb-20 px-10">
            <div class="h-100pre flex align-center">
              <div class="mr-20">
                <a-avatar shape="square" :size="30" :src="require('@/assets/date.png')" />
              </div>
              <div class="h-100pre flex flex-column justify-between font-12">
                <div class="font-14" style="color: #86909c; font-weight: 600">当日出航</div>
                <div>
                  <span class="font-16" style="font-weight: 800">{{ statisticsInfo.todaySum }}</span
                  >人次
                </div>
              </div>
            </div>
            <div class="line h-100pre w-1" style="background: #e5e6eb"></div>
            <div class="h-100pre flex align-center">
              <div class="mr-20">
                <a-avatar shape="square" :size="30" :src="require('@/assets/date2.png')" />
              </div>
              <div class="h-100pre flex flex-column justify-between font-12">
                <div class="font-14" style="color: #86909c; font-weight: 600">当月出航</div>
                <div>
                  <span class="font-16" style="font-weight: 800">{{ statisticsInfo.monthSum }}</span
                  >人次
                </div>
              </div>
            </div>
          </div>
          <div class="h-48 flex align-center px-10">
            <div class="mr-20">
              <a-avatar shape="square" :size="30" :src="require('@/assets/date3.png')" />
            </div>
            <div class="h-100pre flex flex-column justify-between font-12">
              <div class="font-14" style="color: #86909c; font-weight: 600">累计出航</div>
              <div>
                <span class="font-16" style="font-weight: 800">{{ statisticsInfo.yearSum }}</span
                >人次
              </div>
            </div>
          </div>
        </div>
        <!-- 出海船舶 -->
        <div class="h-264 w-100pre mb-20 over-h" style="border-bottom: 1px solid #e5e6eb">
          <div class="flex align-center mb-10">
            <div class="h-10 w-3 mr-5" style="background-color: #3254ff"></div>
            <div class="font-14" style="font-weight: 800">出海船舶</div>
          </div>
          <div class="w-100pre over-auto" style="height: calc(100% - 40px)">
            <div v-if="shipData.length == 0" class="flex align-center justify-center h-100pre w-100pre">
              <a-empty :image="require('@/assets/outSeaEmpty.png')" />
            </div>
            <div v-else class="h-100pre flex flex-wrap">
              <div
                style="width: 25%"
                class="flex align-center justify-center mb-20"
                v-for="item in shipData"
                :key="item.id"
              >
                <a-popover>
                  <template slot="content">
                    <div class="">{{ item.departmentName }}</div>
                  </template>
                  <div class="text-center">
                    <a-avatar :size="40" :src="require('@/assets/ship.png')" />
                    <div
                      :title="item.shipName"
                      class="h-40 text-ellipsis-clamp font-14 mt-10 w-90"
                      style="font-weight: 800"
                    >
                      {{ item.shipName }}
                    </div>
                  </div>
                </a-popover>
              </div>
            </div>
          </div>
        </div>
        <!-- 出海人员 -->
        <div class="flex-1 flex flex-column over-h">
          <div class="flex align-center justify-between">
            <div class="flex align-center justify-center">
              <div class="h-10 w-3 mr-5" style="background-color: #3254ff"></div>
            <div class="font-14" style="font-weight: 800">出海人员</div>
            </div>
            <div class="font-14">
              <a-button type="link" @click="exceptionRecord"> 异常记录> </a-button>
            </div>
          </div>
          <div class="w-100pre over-auto" style="height: calc(100% - 40px)">
            <div v-if="personData.length == 0" class="flex align-center justify-center h-100pre w-100pre">
              <a-empty :image="require('@/assets/outSeaEmpty.png')" />
            </div>
            <div v-else class="w-100pre h-100pre flex flex-column">
              <div class="flex-1 flex flex-wrap">
                <div
                  style="width: 25%"
                  class="h-80 flex align-center justify-center"
                  v-for="item in personData"
                  :key="item.id"
                >
                  <a-popover>
                    <template slot="content">
                      <div class="">单位：{{ item.departmentName }}</div>
                      <div class="">手机号码：{{ item.phone }}</div>
                    </template>
                    <div class="text-center">
                      <a-avatar :size="40" :src="require('@/assets/person.png')" />
                      <div class="font-14 mt-5" style="font-weight: 800">{{ item.realName }}</div>
                    </div>
                  </a-popover>
                </div>
              </div>
              <div class="w-100pre flex justify-center mt-10 h-30">
                <a-pagination
                  size="small"
                  v-model="personPage.pageNo"
                  :pageSize="personPage.pageSize"
                  :total="personPage.total"
                  @change="personPageChange"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #right>
      <a-card :bordered="false" class="right-card">
        <table-layout
          :search-props="searchProps"
          :table-props="tableProps"
          @init-params="initParams"
          @search-submit="loadData"
          @table-change="loadData"
        >
          <template #type="{ record }">
            <a-badge
              :numberStyle="{
                backgroundColor: record.type == 1 ? '#3254FF' : '#FF7D00',
              }"
              :count="record.type == 1 ? '上船' : '下船'"
            />
          </template>
        </table-layout>
      </a-card>
      <Modal ref="modal"></Modal> 
    </template>
    
  </left-right-layout>

</template>

<script>
import { getAction } from '@/api/manage'
import Modal from './components/Modal.vue'
import { Calendar as ACalendar } from 'ant-design-vue'
import LeftRightLayout from '@/components/tableLayout/LeftRightLayout.vue'
import TableLayout from '@/components/tableLayout/TableLayout.vue'
import moment from 'moment'
export default {
  name: 'historicalTrack',
  components: {
    Modal,
    LeftRightLayout,
    ACalendar,
    TableLayout,
  },
  data() {
    return {
      meageParams: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      total: 0,
      data: [],
      url: {
        shipList: '/sailingcheck/shipSailingRecord/list', //船舶查询
        statistics: '/sailingcheck/userBoardRecord/statistics', //出海记录统计
        personList: '/sailingcheck/userSailingRecord/list', //出海人员
        userBoardRecord: '/sailingcheck/userBoardRecord/list', //上下船记录
      },
      // 搜索组件的props
      searchProps: {
        formModel: {
          type: null,
          datetime: null,
        },
        formItems: [
          {
            key: 'datetime',
            label: '选择日期',
            type: 'datetime_range',
            keyParams: ['startDate', 'endDate'],
            format: 'YYYY-MM-DD',
            showTime: false,
          },
        ],
      },
      // 表格组件的props
      tableProps: {
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '姓名',
            align: 'center',
            dataIndex: 'realName',
          },
          {
            title: '所属单位',
            align: 'center',
            dataIndex: 'departmentName',
          },
          {
            title: '所乘船舶',
            align: 'center',
            dataIndex: 'shipName',
          },
          {
            title: '手机号码',
            align: 'center',
            dataIndex: 'phone',
          },
          {
            title: '时间',
            align: 'center',
            dataIndex: 'createTime',
          },
          {
            title: '上/下船',
            dataIndex: 'type',
            align: 'center',
            width: 160,
            scopedSlots: { customRender: 'type' },
          },
        ],
      },
      alarmDay: moment(),
      statisticsData: {},
      shipTotal: 0,
      statisticsInfo: {
        monthSum: 0,
        todaySum: 0,
        yearSum: 0,
      },
      personPage: {
        pageNo: 1,
        pageSize: 12,
        total: 0,
      },
      personData: [],
      shipData: [],
    }
  },
  mounted() {
    this.loadData(this.meageParams)
    this.getStatistics()
    this.getShipList()
    this.getpersonList()
  },
  computed: {},
  watch: {},
  methods: {
    exceptionRecord(){
      // let record=[{
      //   updateBy:'ssss'
      // }]

      this.$refs.modal.visible=true
      this.$nextTick(()=>{this.$refs.modal.edit()})
    },
    // 出海船舶
    getShipList() {
      let params = {
        status: 1,
        pageNo: 1,
        pageSize: 100, //不会有很多船舶
      }
      getAction(this.url.shipList, params).then((res) => {
        console.log('出海船舶', res)
        const { success, result } = res
        if (success) {
          this.shipData = result.records || []
        }
      })
    },
    personPageChange(e) {
      console.log('e', e)
      this.personPage.pageNo = e
      this.getpersonList()
    },
    // 出海人员
    getpersonList() {
      let params = {
        ...this.personPage,
        status: 1,
      }
      getAction(this.url.personList, params).then((res) => {
        console.log('出海人员', res)
        const { success, result } = res
        if (success) {
          this.personPage.total = result.total
          this.personData = result.records || []
        }
      })
    },
    initParams(params) {
      this.meageParams = params
    },
    /**
     * @description 加载列表
     */
    loadData(data) {
      console.log('startDatestartDatestartDate', data)
      let params = {
        ...data.params,
      }
      if (!data.params.datetime) {
        params.startDate = ''
        params.endDate = ''
      }
      console.error('params.endDate', params.endDate)
      getAction(this.url.userBoardRecord, params).then((res) => {
        console.log('上下船记录', res)
        this.tableProps.dataSource = res.result.records
        this.tableProps.ipagination.total = res.result.total
      })
    },
    onShowSizeChange(current, pageSize) {
      this.pageSize = pageSize
    },
    /**
     * @description 分析统计
     */
    getStatistics() {
      getAction(this.url.statistics).then((res) => {
        console.log('分析统计', res)
        const { success, result } = res
        if (success) {
          this.statisticsInfo = result
        }
      })
    },
  },
}
</script>
<style scoped lang="less">
@import '~@assets/less/common.less';
.left-card,
.right-card {
  height: 100%;
  ::v-deep .ant-card-body {
    height: 100%;
    padding: 0;
  }
}
.right-card {
  ::v-deep .ant-table {
    .belongTag {
      width: 72px;
      height: 22px;
      border-radius: 11px;
      display: inline-block;
    }
    .belongTag-interior {
      background: rgba(50, 84, 255, 0.15);
      color: #3254ff;
      border: 1px solid #3254ff;
    }
    .belongTag-without {
      background: rgba(255, 125, 0, 0.2);
      color: #ff7d00;
      border: 1px solid #ff7d00;
    }
  }
}

::v-deep .ant-badge-multiple-words {
  padding: 0 20px;
}

::v-deep .ant-card-body {
  height: 100%;
}
::v-deep .ant-input {
  padding-left: 0;
  padding-right: 0;
}
</style>
