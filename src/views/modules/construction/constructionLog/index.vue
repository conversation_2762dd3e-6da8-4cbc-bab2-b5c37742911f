<template>
  <div class="pageMain" id="constructionLog">
    <Map @clickMap="clickMap"></Map>
    <!-- <div id="map" ref="mapContainer"></div> -->
    <!-- 地图工具类操作 -->
    <div class="map-tool">
      <div v-for="item in mapTool" :key="item">
        <a-popover title="" placement="leftTop" trigger="click" v-if="item == 'menu'">
          <template slot="content">{{ item }}</template>
          <div class="tool-item" @click="handleMapToolClick(item)" :class="item"></div>
        </a-popover>
        <div v-else class="tool-item" @click="handleMapToolClick(item)" :class="item"></div>
      </div>
    </div>
    <j-modal
      :keyboard="false"
      :mask="false"
      :getContainer="getContainer"
      :width="width"
      :visible="visible"
      @ok="handleOk"
      :okButtonProps="{ class: { 'jee-hidden': false } }"
      @cancel="cancelDialog"
      :footer="null"
    >
      <template slot="title">
        <div class="flex align-center">
          <div class="line1 mr-8"></div>
          <div class="home" style="color: #444; font-weight: bolder">{{ modalTitle }}</div>
        </div>
      </template>
      <template slot="closeIcon">
        <div class="back icon-back iconfont">返回</div>
      </template>
      <modal ref="modalForm" @ok="modalFormOk" @update="update"></modal>
    </j-modal>
  </div>
</template>

<script>
import MapService from '@/utils/MapService'
// import mapData from '../../../../../public/static/map/mapData.json'
import Map from '@/components/Map'
import Modal from './components/Modal'
import { getAction } from '@/api/manage'

export default {
  components: { Modal, Map },
  data() {
    return {
      modalTitle: '测试',
      visible: false,
      width: '100%',
      mapTool: ['alert', 'menu', 'position', 'big', 'xiao'],
      mapDefaultZoom: 12,
      map: null,
      mousemoveLnglat: null,
    }
  },
  mounted() {
    // this.roadMap()
  },
  methods: {
   
    cancelDialog() {
      this.$refs.modalForm.cancelShow()
      this.visible = false
    },
    handleOk() {},
    getContainer() {

      let dom = document.getElementById('constructionLog')
      console.log("施工ri",dom)
      return dom
    },
    clickMap(mark, markerType) {
      console.log('🚀 ~ clickMap ~ mark:', mark)
      if ((markerType == 'fengji' || markerType == 'hailan') && mark.fanSection == '0') {
        this.getFormData(mark.processType, mark.id, mark.name)
      }
    },

    update(processType, pbsId, name) {
      this.getFormData(processType, pbsId, name)
    },
    getFormData(processType, pbsId, name) {
      getAction('/construction/constructionLog/queryList', {
        processType: processType,
        pbsId: pbsId,
      }).then((res) => {
        const { result, success, message } = res
        if (!success) {
          this.$message.error(message)
        }
        console.log('弹窗展示请求 接口', result)
        this.visible = true
        this.modalTitle = name + '日志填报'
        this.$nextTick(() => {
          this.$refs.modalForm.tableProps.dataSource = result
          this.$refs.modalForm.pbsId = pbsId
          this.$refs.modalForm.processType = processType
          this.$refs.modalForm.name = name
        })
      })
    },
    modalFormOk() {},
    // ===============地图工具类操作===================
    handleMapToolClick(eventName) {
      if (!eventName) {
        return
      }
      //   this[eventName]()
    },
    roadMap() {
      const { mapCenter } = mapData
      let mapConfig = {
        mapContainer: this.$refs.mapContainer,
        lng: mapCenter[0],
        lat: mapCenter[1],
        zoom: this.mapDefaultZoom,
      }
      // 定义地图点击事件的回调函数
      const clickMapCallback = (event) => {
        console.log('地图点击事件', event)
        if (event.type == 'click') {
          //   this.mousemoveLnglat = event.lnglat
        }
      }
      MapService.initMap(mapConfig, clickMapCallback)
        .then((map) => {
          this.map = map
          console.log('地图初始化成功')
          this.getData()
        })
        .catch((error) => {
          console.error('地图初始化失败', error)
        })
    },
    getData() {
      getAction('/construction/constructionLog/list').then((res) => {
        console.log('热水', res)
        const { success, result, message } = res
        if (!success) {
          this.$message.error(message)
          return
        }
        if (result) {
          MapService.setFanMarks(result, (mark) => {
            console.log('地图打点成功', mark)
            // 弹窗展示请求 接口
            getAction('/construction/constructionLog/queryList', {
              processType: mark.processType,
              pbsId: mark.pbsId,
            }).then((pbsData) => {
              console.log('弹窗展示请求 接口', pbsData)
              if (!pbsData.success) {
                this.$message.error(pbsData.message)
                return
              }
              if (pbsData.result) {
                this.$refs.modalForm.visible = true
                pbsData.result.forEach((item) => {
                  item['editable'] = false
                })
                this.$refs.modalForm.tableData = pbsData.result
                this.$refs.modalForm.pbsId = mark.pbsId
              }
            })
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .ant-modal {
  height: 100% !important;
  .ant-modal-content {
    height: 100% !important;
    display: flex;
    flex-direction: column;
    .ant-modal-body {
      flex: 1;
      overflow: hidden;
      padding: 0;
    }
  }
}

::v-deep .ant-modal-wrap {
  position: absolute;
}

::v-deep .ant-modal {
  top: 0 !important;
  padding-bottom: 0 !important;
}

.pageMain {
  --index: 500;
  --left: 54px;
  --top: 50px;
  --right: 54px;
  --bottom: 16px;
  width: 100%;
  height: 100%;
  position: relative;

  #map {
    width: 100%;
    height: calc(88vh - 5px);
  }

  .map-tool {
    display: none;
    position: absolute;
    bottom: var(--bottom);
    right: var(--right);
    z-index: var(--index);

    .tool-item {
      width: 36px;
      height: 36px;
      border-radius: 4px;
      margin-bottom: 10px;
      cursor: pointer;
    }

    .alert {
      background: url('../../../../assets/map/img/1.png') no-repeat;
      background-size: 100%;
    }

    .alert:active {
      background: url('../../../../assets/map/img/1-active.png') no-repeat;
      background-size: 100%;
    }

    .menu {
      background: url('../../../../assets/map/img/2.png') no-repeat;
      background-size: 100%;
    }

    .menu:active {
      background: url('../../../../assets/map/img/2-active.png') no-repeat;
      background-size: 100%;
    }

    .position {
      background: url('../../../../assets/map/img/3.png') no-repeat;
      background-size: 100%;
    }

    .position:active {
      background: url('../../../../assets/map/img/3-active.png') no-repeat;
      background-size: 100%;
    }

    .big {
      background: url('../../../../assets/map/img/4.png') no-repeat;
      background-size: 100%;
    }

    .big:active {
      background: url('../../../../assets/map/img/4-active.png') no-repeat;
      background-size: 100%;
    }

    .xiao {
      background: url('../../../../assets/map/img/5.png') no-repeat;
      background-size: 100%;
    }

    .xiao:active {
      background: url('../../../../assets/map/img/5-active.png') no-repeat;
      background-size: 100%;
    }
  }
}

.line1 {
  width: 3px;
  height: 10px;
  background: #3254ff;
}
.back {
  width: 90px;
  line-height: 32px;
  color: #3254ff;
  height: 32px;
  background: #dbe3ff;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #3254ff;
  font-size: 14px;
}
.icon-back {
  color: #3254ff;
}

::v-deep .ant-modal-close-x {
  display: flex;
  align-items: center;
  width: 103px;
}
</style>
