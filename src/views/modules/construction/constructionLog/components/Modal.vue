<template>
  <div class="h-100pre puhui">
    <div class="custom-modal" v-if="showPopover">
      <div class="flex align-center border-bottom">
        <div class="line1 mr-8"></div>
        <div class="home" style="color: #444; font-weight: bolder">{{ form.processName }}</div>
      </div>
      <div class="w-360">
        <div class="flex align-center border-bottom-cell">
          <div>填报人</div>
          <div class="left-auto">{{ form.createName || '' }}</div>
        </div>
        <div class="flex align-center border-bottom-cell">
          <div>施工单位</div>
          <div class="left-auto">{{ form.unitName }}</div>
        </div>
        <div class="flex align-center border-bottom-cell">
          <div class="must">开始时间</div>
          <div class="left-auto" :class="{ 'titip-err': iserrorStart }">
            <a-date-picker
              size="small"
              v-model="form.startTime"
              show-time
              @change="(date) => changeTime('start', date)"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm"
              placeholder="请选择开始时间"
            />
          </div>
        </div>
        <div class="flex align-center border-bottom-cell">
          <div class="must">完成时间</div>
          <div class="left-auto" :class="{ 'titip-err': iserrorEnd }">
            <a-date-picker
              size="small"
              v-model="form.endTime"
              show-time
              @change="(date) => changeTime('end', date)"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm"
              placeholder="请选择完成时间"
            />
          </div>
        </div>
        <div class="flex align-center border-bottom-cell">
          <div>耗时</div>
          <div class="left-auto">{{ getUseTime(form) }}</div>
        </div>
        <div class="flex align-center border-bottom-cell">
          <div>施工状态</div>
          <div class="left-auto">{{ form.endTime ? '已完成' : '未开始' }}</div>
        </div>
        <div class="border-bottom-cell position-relative">
          <div class="mb-5">施工内容</div>
          <a-textarea
            class="custom-textarea"
            :maxLength="100"
            v-model="form.content"
            placeholder="请输入"
            :auto-size="{ minRows: 4, maxRows: 4 }"
          />
          <div class="position-absolute bottom-14 font-12 right-10 index-10" style="color: #86909c">
            {{ fontNum }}/100
          </div>
        </div>
        <div class="border-bottom-cell mb-20">
          <div class="mb-5">{{ `附件（${getFileSize(form.file)}/10）` }}</div>
          <div class="popoverContent">
            <j-upload text="添加附件" :number="10" :fileType="'image'" v-model="form.file" size="'mine'">
              <template slot="icon">
                <a-icon type="plus-circle" theme="filled" :style="{ fontSize: '24px', color: '#0096ff' }" />
              </template>
            </j-upload>
          </div>
        </div>
        <div class="flex justify-center w-100pre position-absolute bottom-10">
          <div class="cancel mr-20 pointer" @click="cancelShow">取消</div>
          <div class="sure pointer" @click="submit">确定</div>
        </div>
      </div>
    </div>
    <table-layout ref="tableLayout" :table-props="tableProps">
      <span slot="index" slot-scope="{ index }">{{ index + 1 }} </span>
      <template slot="content" slot-scope="{ text }">
        <div class="text-ellipsis w-100" :title="text">{{ text }}</div>
      </template>
      <template slot="startTime" slot-scope="{ text }">
        <template> {{ text }}</template>
      </template>
      <template slot="endTime" slot-scope="{ text }">
        {{ text }}
      </template>
      <template slot="useTime" slot-scope="{ record }">
        <div>{{ getUseTime(record) }}</div>
      </template>

      <template slot="file" slot-scope="{ record }">
        <div v-show="isShow(record)" class="text-ellipsis table-image w-100pre flex justify-center">
          <img :src="getSecend(record.file)" alt="" class="w-40 h-40" @click="showImg(record.file)" />
        </div>
      </template>

      <template slot="constructionState" slot-scope="{ record }">
        <div class="flex justify-center w-100pre">
          <div class="" :class="record.endTime ? 'complete' : 'noSatrt'">
            {{ record.endTime ? '完成' : '未开始' }}
          </div>
        </div>
      </template>

      <template slot="operation" slot-scope="{ record }">
        <div style="display: flex;justify-content: center;">
          <div class="text-0096 pointer" @click="show(record)">填报</div>
          <a-popconfirm v-if="record.endTime" title="确定清除吗?" @confirm="() => clear(record)">
            <div class="text-0096 pointer" style="margin-left: 10px;color: #ff4d4f">清除</div>
          </a-popconfirm>
        </div>
      </template>
    </table-layout>
    <a-modal :visible="previewVisible" :destroyOnClose="true" width="60%" @cancel="handleCancel" :footer="false">
      <div class="w-100pre h-500 customModal"></div>
      <template slot="title">
        <div class="flex align-center">
          <div class="line1 mr-8"></div>
          <div class="home" style="color: #444; font-weight: bolder">预览</div>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script>
import { getFileAccessHttpUrl } from '@/api/manage'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { getAction, postAction } from '@/api/manage'
import { getStore } from '@/utils/storage.js'
import moment from 'moment'
const getFileName = (path) => {
  if (path.lastIndexOf('\\') >= 0) {
    let reg = new RegExp('\\\\', 'g')
    path = path.replace(reg, '/')
  }
  return path.substring(path.lastIndexOf('/') + 1)
}
export default {
  name: 'constructionLogModal',
  mixins: [JeecgTreeListMixin],
  data() {
    return {
      previewVisible: false,
      showPopover: false,
      formModel: {
        date: null,
      },
      realname: '',
      unitName: '',
      unitId: '',
      visible: false,
      disableSubmit: false,
      pbsId: '',
      processType: '',
      name: '',
      tableProps: {
        ipagination: false,
        isort: {
          column: 'watchDate',
          order: 'desc',
        },
        rowKey: 'index',
        dataSource: [],
        columns: [
          {
            width: 60,
            title: '序号',
            align: 'center',
            dataIndex: 'index',
            key: 'index',
            scopedSlots: { customRender: 'index' }, //内容自定义插槽
          },
          {
            dataIndex: 'processName',
            title: '工序名称',
            key: 'processName',
            //   slots: { title: 'customTitle' },//标题自定义插槽
            // scopedSlots: { customRender: 'processName' }, //内容自定义插槽
          },
          {
            title: '填报人',
            align: 'center',
            dataIndex: 'createName',
            key: 'createName',
          },
          {
            title: '施工单位',
            align: 'center',
            dataIndex: 'unitName',
            key: 'unitName',
          },
          {
            title: '开始时间',
            align: 'center',
            dataIndex: 'startTime',
            key: 'startTime',
            scopedSlots: { customRender: 'startTime' }, //内容自定义插槽
          },
          {
            title: '完成时间',
            align: 'center',
            dataIndex: 'endTime',
            key: 'endTime',
            scopedSlots: { customRender: 'endTime' }, //内容自定义插槽
          },
          {
            title: '耗时',
            align: 'center',
            dataIndex: 'useTime',
            key: 'useTime',
            scopedSlots: { customRender: 'useTime' }, //内容自定义插槽
          },
          {
            title: '施工内容',
            align: 'center',
            dataIndex: 'content',
            key: 'content',
            scopedSlots: { customRender: 'content' }, //内容自定义插槽
          },
          {
            title: '附件',
            dataIndex: 'file',
            key: 'file',
            width: 100,
            scopedSlots: { customRender: 'file' }, //内容自定义插槽
          },
          {
            title: '施工状态',
            align: 'center',
            dataIndex: 'constructionState',
            key: 'constructionState',
            scopedSlots: { customRender: 'constructionState' }, //内容自定义插槽
          },
          {
            title: '操作',
            align: 'center',
            dataIndex: 'operation',
            key: 'operation',
            scopedSlots: { customRender: 'operation' }, //内容自定义插槽
          },
        ],
        headerButtons: null,
        bordered: false,
      },

      iserrorStart: false,
      iserrorEnd: false,
      form: {
        startTime: '',
        endTime: '',
      },
    }
  },
  computed: {
    fontNum() {
      if (this.form.content) {
        return this.form.content.length
      }
      return 0
    },
  },
  mounted() {
    //监听鼠标事件
  },
  methods: {
    handleCancel() {
      this.previewVisible = false
      this.$closeImagePreview({ way: 'closeBtn' })
    },
    isShow(record) {
      if (record.file) {
        return true
      }
      return false
    },
    // 图片预览
    showImg(filesString) {
      this.previewVisible = true
      this.$nextTick(() => {
        let fileArr = JSON.parse(filesString)
        let files = fileArr.filter((x) => x.type.includes('image'))
        let urls = files.map((x) => x.url)
        this.$ImgPreview({
          multiple: true,
          nowImgIndex: 0,
          imgList: urls,
          targetElement: '.customModal', //这个是需要插入的元素的类名
        })
        this.$previewRefresh()
      })
    },
    initFileList(paths) {
      let fileList = []
      let arr = paths.split(',')
      for (var a = 0; a < arr.length; a++) {
        let url = getFileAccessHttpUrl(arr[a])
        fileList.push({
          name: getFileName(arr[a]),
          status: 'done',
          url: url,
          response: {
            status: 'history',
            message: arr[a],
          },
        })
      }
      return fileList
    },
    getSecend(file) {
      if (file) {
        // return getFileAccessHttpUrl(file.split(',')[0])
        return JSON.parse(file)[0].url
      }
    },

    getFileSize(size) {
      if (size) {
        return JSON.parse(size).length
      }
      return 0
    },
    clear(record){
      postAction('/construction/constructionLog/clear', {
        id: record.id,
        pbsId: record.pbsId,
        processId: record.processId
      })
        .then((result) => {
          console.log('result', result)
          if (!result.success) {
            this.$message.error('清除失败')
            return
          }
          this.$message.success('清除成功')
          this.cancelShow()
          // 回调数据
          this.$emit('update', this.processType, this.pbsId, this.name)
        })
        .catch((err) => {})
    },
    show(record) {
      this.iserrorStart = false
      this.$set(this, 'form', JSON.parse(JSON.stringify(record)))
      this.showPopover = true
    },
    // 消除混入的 loadData
    loadData() {},
    getPopupContainer(trigger) {
      return trigger.parentElement
    },
    cancelShow() {
      this.showPopover = false
    },
    submit() {
      let row = JSON.parse(JSON.stringify(this.form))
      console.log('提交', row)
      if (!row.startTime || !row.endTime) {
        this.iserrorStart = !Boolean(row.startTime)
        this.iserrorEnd = !Boolean(row.endTime)
        return
      }
      console.log('提交', row)
      let local = getStore('pro__Login_UserinfoNew')
      row.createName = local.workUserName
      row.unitName = local.workDepartmentName
      row.unitId = local.workDepartmentId
      console.log('local', row)
      postAction('/construction/constructionLog/addList', {
        pbsId: this.pbsId,
        constructionLogs: [row],
      })
        .then((result) => {
          console.log('result', result)
          if (!result.success) {
            this.$message.error('保存失败')
            return
          }
          this.$message.success('保存成功')
          this.cancelShow()
          // 回调数据
          this.$emit('update', this.processType, this.pbsId, this.name)
        })
        .catch((err) => {})
    },
    changeTime(text, value) {
      if (text == 'start') {
        this.iserrorStart = !Boolean(value)
      } else {
        this.iserrorEnd = !Boolean(value)
      }
    },
    getUseTime(record) {
      if (record.startTime && record.endTime) {
        // 输入的时间字符串
        const startTime = record.startTime
        const endTime = record.endTime

        // 使用Moment.js解析时间字符串
        const start = moment(startTime)
        const end = moment(endTime)

        // 计算时间差异
        const duration = moment.duration(end.diff(start))

        // 获取各时间单位的值
        const days = Math.floor(duration.asDays())
        const hours = duration.hours()
        const minutes = duration.minutes()
        const seconds = duration.seconds()

        // 如果秒数大于等于30，则分钟进一
        if (seconds >= 30) {
          minutes++
        }

        this.computedTime(record)
        return `${days}天 ${hours}小时 ${minutes}分钟`
        // return moment(record.endTime).diff(moment(record.startTime), 'hours')
      } else {
        return ''
      }
    },
    computedTime(record) {
      // console.log('record,index', record)
      if (record.startTime && record.endTime) {
        // 计算小时
        const start = new Date(record.startTime)
        const end = new Date(record.endTime)
        const hour = ((end - start) / 3600000).toFixed(1)
        // console.log('sssssssssssssssss', hour)
        this.$set(record, 'useTime', hour)
      } else {
        this.$set(record, 'useTime', 0)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.hideUpload {
  ::v-deep .ant-upload {
    display: none !important;
  }
}

// ::v-deep .ant-modal-header {
//   background-color: #0096ff;
// }

::v-deep .anticon-fullscreen {
  color: #fff;
}


::v-deep .ant-upload-list-item-name {
  overflow: hidden;
  text-overflow: ellipsis;
  lines: 1;
  white-space: nowrap;
  word-break: break-all;
  width: 150px;
}

::v-deep .ant-calendar-picker {
  min-width: 180px !important;
}

.line1 {
  width: 3px;
  height: 10px;
  background: #3254ff;
}
.border-bottom {
  border-bottom: 4px solid #c9cdd4;
  padding: 10px 0;
}
.border-bottom-cell {
  padding: 15px 0;
  border-bottom: 1px solid #c9cdd4;
  font-size: 14px;
  > div:first-child {
    color: #4e5969;
  }
  > div:nth-child(2) {
    font-weight: bold;
    color: #1d2129;
  }
}
::v-deep .ant-popover-title {
  border-bottom: 0 !important;
}
.cancel {
  color: #0096ff;
  width: 80px;
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  text-align: center;
  color: #3254ff;
  background: rgba(133, 161, 255, 0.3);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #3254ff;
}
.sure {
  color: #fff;
  width: 80px;
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  text-align: center;
  background: #3254ff;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #3254ff;
}
.must {
  position: relative;
}
.must:before {
  position: absolute;
  left: -12px;
}
.titip-err {
  position: relative;
}
.titip-err:before {
  content: '请选择时间';
  color: red;
  font-weight: 500;
  font-size: 12px;
  position: absolute;
  bottom: -16px;
  left: 5px;
}
// 完成
.complete {
  font-size: 14px;
  color: #ffffff;
  line-height: 22px;
  text-align: center;
  width: 72px;
  height: 22px;
  background: #3254ff;
  border-radius: 11px 11px 11px 11px;
}
.noSatrt {
  font-size: 14px;
  color: #ffffff;
  line-height: 22px;
  text-align: center;
  width: 72px;
  height: 22px;
  background: #999;
  border-radius: 11px 11px 11px 11px;
}

.table-image {
  ::v-deep .anticon-delete {
    display: none !important;
  }

  ::v-deep .ant-upload-list-item {
    width: 40px;
    height: 40px;
    margin: 0;
    padding: 0;
  }
  ::v-deep .ant-upload-list-picture-card-container {
    width: 40px;
    height: 40px;
    margin: 0;
  }
  ::v-deep .uploadty-mover-mask {
    display: none;
  }
  ::v-deep .movety-container {
    display: none;
  }
}
.popoverContent {
  height: 160px;
  overflow: auto;
}

::v-deep .uploadty-mover-mask {
  display: none;
}
::v-deep .movety-container {
  display: none;
}

::v-deep .ant-calendar-time-picker-combobox .ant-calendar-time-picker-select {
  display: none !important;
}

.custom-modal {
  position: absolute;
  z-index: 9999;
  top: 0;
  width: 400px;
  right: 186px;
  /* height: 100%; */
  background-color: #fff;
  bottom: 0;
  overflow: scroll;
  border-radius: 4px;
  padding: 10px 20px;
  outline: 1px solid #c9cdd4;
  overflow: hidden;
  margin: 5px 0;
}
</style>
