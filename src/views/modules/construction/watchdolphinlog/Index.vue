<template>
  <div class="list-page watch-dolphin-log-list">
    <table-layout
      ref="tableLayout"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="onSearchSubmit"
      @table-change="onSearchSubmit"
      :rightTitle="rightTitle"
    >
      <template #searchWithDolphin>
        <a-button type="primary" icon="search" @click="searchWithDolphin">仅查看有海豚的日志</a-button>
      </template>
      <template slot="shipId_dictText" slot-scope="{ text }">
        <div class="w-100pre text-left">
          {{ text }}
        </div>
      </template>
      <template slot="exsitDolphin" slot-scope="{ record }">
        <div class="w-100pre flex justify-center">
          <div class="flex">
            <div
              :class="getBg(record, index)"
              class="mr-2 h-30 w-30 line-30 text-center"
              v-for="(item, index) in 24"
              :key="index"
            >
              {{ item - 1 }}
            </div>
          </div>
        </div>
      </template>
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { mapActions, mapGetters, mapState } from 'vuex'
import { getStore, setStore } from '@/utils/storage'
import { getAction } from '@api/manage'
import moment from 'moment'
export default {
  name: 'WatchDolphinLogList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {
      isorter: {
        //此处禁用排序
        column: '',
        order: '',
      },
      queryParam: {},
      username: '',
      rightTitle: '观豚日志列表',
      // 搜索组件的props
      searchProps: {
        formModel: {},
        formItems: [
          {
            text: '仅查看有海豚的日志',
            slot: 'searchWithDolphin',
            colConfig: { sm: 24, md: 12, lg: 8, xl: 4 },
          },
          {
            key: 'date',
            label: '观测日期',
            type: 'datetime_range',
            keyParams: ['start', 'end'],
            placeholder: ['开始日期', '结束日期'],
            format: 'YYYY-MM-DD',
            showTime: false,
          },

          { key: 'username', label: '观测人员', placeholder: '请输入' },
        ],
      },
      // 表格组件的props
      tableProps: {
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        isort: {
          column: 'watchDate',
          order: 'desc',
        },
        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '观测日期',
            align: 'center',
            dataIndex: 'watchDate',
            width: 150,
            customRender: (text) => {
              return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
            },
          },
          {
            title: '观测人员	',
            align: 'center',
            dataIndex: 'username',
            width: 150,
          },
          {
            title: '观测船舶',
            align: 'center',
            dataIndex: 'shipId_dictText',
            scopedSlots: { customRender: 'shipId_dictText' },
            width: 300,
          },
          {
            title: '观测结果',
            align: 'center',
            dataIndex: 'exsitDolphin',
            scopedSlots: { customRender: 'exsitDolphin' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 200,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
            disabled: (record) => {
              console.log(record, this.username)
              return record.username !== this.username
            },
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
            disabled: (record) => {
              console.log(record, this.username)
              return record.username !== this.username
            },
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],
        bordered: false,
      },
      url: {
        list: '/construction/monitorDolphinLog/list',
        delete: '/construction/monitorDolphinLog/delete',
        deleteBatch: '/construction/monitorDolphinLog/deleteBatch',
        exportXlsUrl: '/construction/monitorDolphinLog/exportXls',
        importExcelUrl: 'construction/monitorDolphinLog/importExcel',
      },
    }
  },
  created() {
    this.username = this.userInfo().realname
  },
  computed: {},
  watch: {},
  methods: {
    getBg(record, index) {
      if (record.details.length > 0 && record.details[0].endDate) {
        // 找出当前 record.details所有的时间段
        let hours = record.details.map((item) => {
          return {
            hour: moment.unix(item.startDate).hour(),
            isConstructionAffected: item.isConstructionAffected,
          }
        })
        console.log('hours', hours)
        let arr = hours.filter((item) => item.hour == index)
        console.log('arr', arr)
        if (arr.length == 0) {
          return 'default'
        } else {
          let isConstructionAffected = arr.find((item) => item.isConstructionAffected == 1)
          if (isConstructionAffected) {
            return 'detal'
          } else {
            return 'normal'
          }
        }
      }
      return 'default'
    },
    initParams(mergeParams) {
      console.error('mergeParams', mergeParams)
      this.queryParam = mergeParams.formModel
    },
    onSearchSubmit(params) {
      console.log('onSearchSubmit', params)
      let searchParams = Object.assign({}, params.params, params.ipagination)
      this.loadData(searchParams)
    },
    ...mapGetters(['userInfo']),
    searchWithDolphin(params) {
      console.log('searchProps', this.searchProps, this.queryParam)
      let searchParams = Object.assign({ exsitDolphin: '1' }, this.queryParam)
      this.loadData(searchParams)
    },
    handleAdd() {
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.add({
        username: this.username,
        watchDate: moment().format('YYYY-MM-DD'),
        exsitDolphin: '0',
      })
      this.$refs.modalForm.disableSubmit = false
    },
  },
}
</script>
<style lang="scss" scoped>
.default {
  background: #dee2ea;
  color: #86909c;
}
.normal {
  background: #3254ff;
  color: #fff;
}
.detal {
  background: #f53f3f;
  color: #fff;
}
</style>
