<template>
  <div class="">
    <a-tabs
      :activeKey="activeKey"
      :type="disableForm ? 'card' : 'editable-card'"
      @edit="handleEdit"
      class="w-100pre"
      @tabClick="changeTab"
      hide-add
    >
      <a-tab-pane
        v-for="pane in panes"
        :key="pane.key"
        :tab="pane.title"
        :closable="disableForm ? false : pane.closable"
      >
      </a-tab-pane>
      <template slot="tabBarExtraContent">
        <a-button :disabled="disableForm" @click="handleAdd" type="primary" icon="plus">新增</a-button>
      </template>
    </a-tabs>
    <!-- <j-form-container :disabled="disableForm"> -->
    <a-row v-if="panes.length > 0" slot="detail">
      <a-form :form="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-col :span="12">
          <a-form-item label="出现时间:">
            <div class="flex align-center">
              <a-time-picker
                style="width: 40%"
                :disabled="disableForm"
                format="HH:mm"
                valueFormat="X"
                v-decorator="['startDate', formRule.startDate]"
              />
              <span class="mx-10">~</span>
              <a-time-picker
                :disabled="disableForm"
                @change="endonChange"
                style="width: 40%"
                format="HH:mm"
                valueFormat="X"
                v-decorator="['endDate', formRule.endDate]"
              />
            </div>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="天气状况:">
            <a-input
              v-decorator="['weather']"
              class="w-100pre"
              placeholder="请输入"
              :disabled="disableForm"
              :maxLength="20"
              autocomplete="off"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="白海豚位置:">
            <div class="flex">
              <a-input disabled v-decorator="['position']" placeholder="请输入" />
              <a-button class="ml-10" @click="showmarkPosition" type="primary" icon="environment">
                {{ disableForm ? '查看位置' : '标记位置' }}</a-button
              >
            </div>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="是否影响施工">
            <a-select
              v-decorator="['isConstructionAffected', formRule.isConstructionAffected]"
              :disabled="disableForm"
              @change="handleChangeConstruction"
            >
              <a-select-option value="1"> 是</a-select-option>
              <a-select-option value="0"> 否</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="24" v-if="affect">
          <a-form-item label="施工状态及采取措施:" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
            <a-textarea
              v-decorator="['statusAction', formRule.statusAction]"
              :disabled="disableForm"
              placeholder="请输入"
              allow-clear
              :rows="3"
              :maxLength="500"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="图片上传" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
            <j-upload
              text="添加附件"
              :number="3"
              :fileType="'image'"
              v-decorator="['pictures']"
              size="'mine'"
              :disabled="disableForm"
            >
              <template slot="icon">
                <a-icon type="plus-circle" theme="filled" :style="{ fontSize: '24px', color: '#0096ff' }" />
              </template>
            </j-upload>
          </a-form-item>
        </a-col>
      </a-form>
    </a-row>
    <!-- </j-form-container> -->
    <div v-if="showComponent" class="position-fixed w-100pre h-100pre" style="right: 0; top: 0; z-index: 3500">
      <div class="position-relative w-100pre h-100pre">
        <components
          :markPosition="markPosition"
          :position="position"
          :is="component"
          @clickMap="markPositionCallBack"
        ></components>
        <div class="position-absolute index-3501" style="bottom: 50px; left: 49%">
          <a-button class="mr-10" @click="closeMap"> 关闭</a-button>
          <a-button class="" type="primary" @click="sureMap" v-if="!disableForm"> 确定</a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Map from '@/components/Map'
import moment from 'moment'
export default {
  components: { Map },
  props: {
    disableForm: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      affect: false,
      markPosition: false,
      showComponent: false,
      disabledEnd: false,
      component: '',
      activeKey: '1',
      panes: [
        {
          title: `详情1`,
          key: '1',
          closable: true,
          form: {
            startDate: '',
            endDate: '',
            position: '',
            isConstructionAffected: '0',
            statusAction: '',
            pictures: '',
          },
        },
      ],
      form: this.$form.createForm(this, {
        onValuesChange: (props, fields) => {
          // if (fields.startDate) {
          //   this.disabledEnd = false
          // } else {
          //   this.disabledEnd = true
          // }
        },
      }),
      formRule: {
        // 自定义检验 startDate    startDate和endDate 都必须有值
        startDate: { rules: [{ required: true, message: '请选择出现时间!' }, { validator: this.validateDates }] },
        position: { rules: [{ required: true, message: '请选择白海豚位置!' }] },
        isConstructionAffected: { initialValue: '0', rules: [{ required: true, message: '请选择是否影响施工!' }] },
        statusAction: { rules: [{ required: true, message: '请输入施工状态以及措施!' }] },
      },
      mapClickPoint: '',
      position: '',
    }
  },
  methods: {
    moment,
    validateDates(rule, value, callback) {
      console.log('startDatestartDatestartDate', this.form.getFieldValue('startDate'))
      console.log('endDateendDateendDate', this.form.getFieldValue('endDate'))
      if (!this.form.getFieldValue('startDate') || !this.form.getFieldValue('endDate')) {
        callback(new Error('请选择开始和结束时间!'))
      } else {
        callback()
      }
    },
    closeMap() {
      this.showComponent = false
    },
    sureMap() {
      if (this.mapClickPoint && this.mapClickPoint.lng) {
        this.form.setFieldsValue({ position: `${this.mapClickPoint.lng} ,${this.mapClickPoint.lat}` })
      }
      this.showComponent = false
    },
    // 提供给父级调用 因为存在未填写 直接 确认的情况
    validateFields() {
      let Boolean = false
      console.log('开始校验表单')
      this.form.validateFields((err, values) => {
        if (!err) {
          this.saveForm(this.activeKey)
          Boolean = true
        }
      })
      console.log('开始校验表单', Boolean)
      return Boolean
    },
    showmarkPosition() {
      // 展示组件
      this.component = 'Map'
      this.markPosition = !this.disableForm
      this.position = this.form.getFieldValue('position')
      this.showComponent = true
    },
    markPositionCallBack(data) {
      console.log('data回调', data)
      this.mapClickPoint = data.lnglat
    },
    setPoint() {
      if (this.mapClickPoint.lng) {
        this.form.setFieldsValue({ position: `${this.mapClickPoint.lng} ,${this.mapClickPoint.lat}` })
      }
    },
    findPane(key) {
      let pane = this.panes.find((pane) => pane.key == key)
      console.log('activeKeyactiveKey', pane)
      if (pane) {
        return pane
      } else {
        return false
      }
    },
    changeTab(activeKey) {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.saveForm(this.activeKey)
          console.log('activeKey', activeKey, this.activeKey)
          this.activeKey = activeKey
          this.$forceUpdate()
          this.setForm(activeKey)
        }
      })
    },
    setForm(activeKey) {
      this.form.resetFields()
      const pane = { ...this.findPane(activeKey) }
      console.log('当前pane', pane)
      if (pane) {
        const formValues = {
          ...pane.form,
          startDate: pane.form.startDate + '',
          endDate: pane.form.endDate + '',
        }

        console.log('表单赋值', formValues)
        if (formValues.isConstructionAffected === '1') {
          this.affect = true
        } else {
          this.affect = false
        }
        // 表单赋值
        this.$nextTick(() => {
          this.form.setFieldsValue({ ...formValues })
        })
      }
    },
    handleChangeConstruction(e) {
      this.affect = e === '1'
      this.form.setFieldsValue({ isConstructionAffected: e })
    },
    endonChange(date) {
      this.form.validateFields(['startDate'])
      console.log('date', date)
      // this.form.startDate = date[0]
      // this.form.endDate = date[1]
    },
    handleEdit(targetKey, action) {
      console.log('targetKey, action', targetKey, action)
      if (action == 'add') {
        this.form.validateFields((err, values) => {
          if (!err) {
            console.log('校验通过')
            // 等于0 代表什么都没有 就不需要执行保存操作 也不需要校验
            this.saveForm(this.activeKey) //保存当前的数据
            // this.onEdit(targetKey, action);
            this.add(targetKey, action)
          }
        })
      } else {
        this.remove(targetKey, action)
      }
    },

    handleAdd() {
      const newKey = `${this.panes.length + 1}`
      this.handleEdit(newKey, 'add')
    },

    saveForm(activeKey) {
      let tmpData = this.findPane(activeKey)
      //此处丢弃了其他字段 导致错误
      let form = { ...this.form.getFieldsValue() }
      console.log('当前数据aaaaaaaaaaa', JSON.stringify(form))
      form.id = tmpData.form.id ? tmpData.form.id : null
      this.findPane(activeKey).form = { ...form }
      console.log('this.findPane', this.panes)
    },

    addForm(key) {
      let form = {
        startDate: '',
        endDate: '',
        position: '',
        isConstructionAffected: '',
        statusAction: '',
        pictures: '',
      }
      this.form.setFieldsValue({ ...form })
      this.panes.push({ title: `详情${this.panes.length + 1}`, key: key, closable: true, form: { ...form } })
    },

    clearAll() {
      this.panes = []
      this.activeKey = ''
    },
    add() {
      let form = {
        startDate: '',
        endDate: '',
        position: '',
        isConstructionAffected: '0',
        statusAction: '',
        pictures: '',
        weather: '',
      }

      this.affect = false
      this.form.setFieldsValue({ ...form })
      this.activeKey = `${this.panes.length + 1}`
      // this.panes.forEach((x, index) => {
      //   x.closable = true
      // })
      this.panes.push({ title: `详情${this.panes.length + 1}`, key: this.activeKey, closable: true, form: { ...form } })
    },
    remove(targetKey) {
      // 如果当前页面只有一个说明把所有的都删除了

      if (this.panes.length == 1) {
        this.activeKey = '1'
        this.panes = []
        this.$emit('closePanes')
        return
      }
      console.log('targetKey', targetKey, this.panes)
      const panes = JSON.parse(JSON.stringify(this.panes.filter((pane) => pane.key != targetKey)))
      console.log('剩下的', panes)
      panes.forEach((x, index) => {
        x.title = `详情${index + 1}`
      })
      this.activeKey = panes[panes.length - 1].key
      this.setForm(this.activeKey)
      this.panes = panes
    },
  },
}
</script>

<style scoped lang="scss">
.upload-button {
  width: 100%;
}

::v-deep .ant-tabs-tab-active {
  background: aliceblue !important;
  border-color: #0096ff !important;
  border-bottom: 1px solid #fff !important;
}

::v-deep .ant-tabs-extra-content .ant-tabs-new-tab {
  border: 1px solid #0096ff;
}
</style>
