<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail" labelAlign="right">
        <a-row>
          <a-col :span="12">
            <a-form-item label="观测日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                :disabledDate="disabledDate"
                :default-value="moment(new Date(), 'YYYY-MM-DD')"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择观测日期"
                v-decorator="['watchDate', validatorRules.watchDate]"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="观测船舶" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['shipId', validatorRules.shipId]"
                :trigger-change="true"
                dictCode="ship_info,name,id"
                placeholder="请选择观测船舶"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="观测人员" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['username', validatorRules.username]"
                placeholder="请输入观测人员"
                disabled
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="是否观测到白海豚" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                :disabled="disabledExsitDolphin"
                type="list"
                v-decorator="['exsitDolphin', validatorRules.exsitDolphin]"
                :trigger-change="true"
                dictCode="yn"
                placeholder="请输入是否观测到白海豚"
                @change="handleChange"
              />
            </a-form-item>
          </a-col>

          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
    <!--    <a-col :span='24'>-->
    <spanForm :disableForm="formDisabled" v-if="showSpanForm" ref="spanForm" @closePanes="closePanes"></spanForm>
    <!--    </a-col>-->
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import spanForm from './spanForm.vue'
import pick from 'lodash.pick'
import _ from 'lodash'
import moment from 'moment'

export default {
  name: 'WatchDolphinLogForm',
  components: { spanForm },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      disabledExsitDolphin: false,
      showSpanForm: false,
      form: this.$form.createForm(this, {
        onValuesChange: (props, fields) => {
          if (fields.hasOwnProperty('exsitDolphin')) {
            if (fields.exsitDolphin == '1') {
              this.showSpanForm = true
              this.disabledExsitDolphin = true
            } else {
              this.showSpanForm = false
              this.disabledExsitDolphin = false
            }
          }
        },
      }),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 17 },
      },
      confirmLoading: false,
      validatorRules: {
        watchDate: {
          rules: [{ required: true, message: '请输入观测日期!' }],
        },
        shipId: {
          rules: [{ required: true, message: '请输入观测船舶!' }],
        },
        username: {
          rules: [{ required: true, message: '请输入观测人员	!' }],
        },
        exsitDolphin: {
          rules: [{ required: true, message: '请输入是否观测到白海豚!' }],
        },
      },
      url: {
        add: '/construction/monitorDolphinLog/add',
        edit: '/construction/monitorDolphinLog/edit',
        queryById: '/construction/monitorDolphinLog/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    moment,
    disabledDate(current) {
      return current && current > moment().endOf('day')
    },
    closePanes() {
      this.form.setFieldsValue({
        exsitDolphin: '0',
      })
    },
    handleChange() {},
    add(oldRecord) {
      console.log('add', oldRecord)
      // 清空主要数据
      // this.form.resetFields()
      // this.form.setFieldsValue({
      //   username: oldRecord.username,
      //   exsitDolphin: '0'
      // })
      this.edit(oldRecord)
    },
    edit(oldRecord) {
      // 三件事   第一 主表单赋值   第二件事 下面的spans 赋值  第三 spans下面的form 赋值
      let record = _.cloneDeep(oldRecord)
      console.log('edit', record)
      this.form.resetFields()
      this.model = Object.assign({}, record)
      // 第一 主表单赋值
      this.form.setFieldsValue(pick(this.model, 'watchDate', 'shipId', 'username', 'exsitDolphin'))
      // 第二 spans 赋值
      let details = _.cloneDeep(oldRecord.details) || []
      console.log('子表数据', details)
      if (details.length === 0) {
        console.log('子表数据', details)
        return
      }
      let panes = []
      record.exsitDolphin == '1' &&
        (panes = details.map((x, index) => {
          return {
            title: `详情${index + 1}`,
            key: index + 1,
            closable: true,
            form: {
              ...x,
            },
          }
        }))
      // return
      this.$nextTick(() => {
        this.$refs.spanForm.panes = panes
        this.$refs.spanForm.activeKey = '1' // 默认显示第一个 第一个 也就是 key为1  没有的时候 也就无所谓了
        console.log('修改spans', panes)
        // 第三 spans下面的form 赋值
        this.$refs.spanForm.setForm('1')
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    close() {
      // 这个时候   如果存在底图就让底图消失
      let hasMap = false
      if (this.showSpanForm && this.$refs.spanForm.showComponent) {
        hasMap = this.$refs.spanForm.showComponent
      }
      if (hasMap) {
        this.$refs.spanForm.showComponent = false
      }
      return hasMap
    },
    setCloseMap() {
      this.$refs.spanForm.showComponent = false
    },
    combinDate(date, time) {
      //将10时间戳转为 时间并取分秒部分和date组合返回
      // 将时间戳（秒）转换为毫秒，并创建 Date 对象
      const timeDate = new Date(Number(time) * 1000)
      // 使用 Intl.DateTimeFormat 来提取 time 的小时和分钟
      const timeFormatter = new Intl.DateTimeFormat('default', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      })
      // 获取 time 的 HH:mm 部分
      const timePart = timeFormatter.format(timeDate)
      // 组合 date 和 timePart
      return `${date} ${timePart}`
    },
    submitForm() {
      // 触发子表的校验
      let exsitDolphin = this.form.getFieldValue('exsitDolphin')
      console.log('exsitDolphin', exsitDolphin, typeof exsitDolphin)
      let sonForm = []
      if (exsitDolphin == 0) {
        //不存在时不验证子表单
      } else {
        console.log('需要校验表单')
        let sonvalidateFields = this.$refs.spanForm.validateFields()
        if (!sonvalidateFields) return
        // 收集子表的数据
        let panes = this.$refs.spanForm.panes
        console.log('收集子表的数据', panes)
        console.log('form信息', this.$refs.spanForm.form)
        if (panes.length > 0) {
          sonForm = panes.map((x) => x.form)
        }
        console.log('给后端的数据', sonForm)
      }
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          console.log('提交url', this.model.id, httpurl)

          let formData = Object.assign(this.model, values)
          formData.details = sonForm
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'watchDate', 'shipId', 'username', 'exsitDolphin'))
    },
  },
}
</script>
