<template>
  <div>
    <a-modal
      @cancel.stop="close"
      v-model="visable"
      :title="title"
      cancel-text="关闭"
      @ok="visable = false"
      :width="'80%'"
      :footer="null"
    >
      <table-layout
        v-if="visable"
        style="height: 600px"
        ref="tableLayout"
        :tree-props="treeProps"
        :table-props="tableProps"
        @init-params="initParams"
        @search-submit="onSearchSubmit"
        @tree-init="initTree"
        @tree-select="onTreeSelect"
        @table-change="onTableChange"
        @table-expand="handleExpand"
      >
      </table-layout>
      <div class="custom-footer">
        <a-button type="primary" @click="onSave">保存</a-button>
        &nbsp;&nbsp;
        <a-button type="primary" @click="close">关闭</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script>
import TableLayout from '@/components/tableLayout/TableLayout.vue'

import { getAction, postAction, deleteAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin' //
import { filterObj } from '@/utils/util'

export default {
  components: {
    TableLayout,
  },
  mixins: [JeecgListMixin],
  data() {
    return {
      dictInfo: {},
      disableMixinCreated: true,
      url: {
        leftTree: '/construction/constructionPlanProcess/leftTree',
        getProcessByPlan: '/construction/constructionPlanProcess/getProcessByPlan',
        saveProcessPlan: '/construction/constructionPlanProcess/saveProcessPlan',
      },
      hasChildrenField: 'hasChild',
      pidField: 'pid',
      loadParent: false,
      mergeParams: {},
      queryParam: {},

      //   // 右侧头部
      //   rightTitle: 'PBS列表',
      // 树组件的props
      treeProps: {
        // dictCode: 'B06',
        isSelectFirstChild: true,
        selectedKeys: [],
        isSelectParentNodes: false,
        replaceFields: { title: 'name', key: 'id' },
        treeData: [],
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        checkable: true,
        dataSource: [],
        ipagination: false,
        columns: [
          {
            title: '工序编号',
            dataIndex: 'processCode',
            width: 120,
            align: 'center',
            // customRender: (t, r, index) => {
            //   return parseInt(index) + 1
            // },
          },
          {
            title: '工序名称',
            dataIndex: 'name',
            align: 'center',
            width: 120,
          },
          {
            title: '工序内容',
            dataIndex: 'workContent',
            align: 'center',
            width: 120,
          },
          {
            title: '已关联进度计划',
            dataIndex: 'planName',
            align: 'center',
            width: 120,
          },
        ],
        expandedRowKeys: [], // 用于控制哪些行展开
        // 勾选相关的 props 将统一放在 tableDataProps 内
        rowSelection: {
          selectedRowKeys: [], // 用于控制哪些行被勾选
          onChange: this.onSelectChange, // 这个处理函数应该在 methods 中定义
          getCheckboxProps: (record) => ({
            props: {
              disabled: record.planCode != this.code && record.planName != null, // Column configuration not to be checked
            },
            // name属性是你数据中的特定字段，你可以根据实际情况替换
          }),
        },

        showList: false,
        tableProps: {
          ipagination: false,
        },
      },
      title: '',
      code: '',
      visable: false, //详情是否可见
      dataArray: {},
      pbsId: '',
      version: 0,
    }
  },
  created() {
    this.initTree()
  },
  methods: {
    onSave() {
      this.visable = false
      postAction(this.url.saveProcessPlan, { planCode: this.code, pbsIds: this.dataArray }).then((res) => {
        if (res.success) {
          this.dataArray = {}
        } else {
          this.$message.warning(res.message)
        }
      })
      console.log('点击保存')
    },
    initParams(mergeParams) {
      this.queryParam = mergeParams
    },
    //初始化树
    initTree() {
      getAction(this.url.leftTree)
        .then((res) => {
          if (res.success) {
            let result = res.result
            //此处根据type字段的类型 如果是风机,显示风机规格, 如果是海缆 显示海缆型号,集电海缆路由
            if (result && result.length > 0) {
              this.treeProps.treeData = result
              this.loadData(this.queryParam)
            } else {
              // this.tableProps.ipagination.total = 0;
              this.treeProps.treeData = []
            }
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.treeProps.loading = false
        })
    },
    // 右侧列表
    loadData(arg) {
      this.pbsId = arg.params.treeId
      this.tableProps.loading = true
      let params = { id: this.pbsId, planCode: this.code, version: this.version }
      console.error('loadData-params===>', params)
      //   params.id = params.type
      getAction(this.url.getProcessByPlan, params)
        .then((res) => {
          if (res.success) {
            let result = res.result.projectProcess
            //此处根据type字段的类型 如果是风机,显示风机规格, 如果是海缆 显示海缆型号,集电海缆路由
            if (result && result.length > 0) {
              console.log('回显数据', res.result.checkOn, this.dataArray[this.pbsId])
              if (!this.dataArray[this.pbsId]) {
                this.tableProps.rowSelection.selectedRowKeys = res.result.checkOn
              } else {
                this.tableProps.rowSelection.selectedRowKeys = this.dataArray[this.pbsId]
              }

              this.tableProps.dataSource = this.getDataByResult(res.result.projectProcess)
              this.calcPath(this.tableProps.dataSource)

              //   this.calcPath(this.tableProps.rowSelection.selectedRowKeys)
            } else {
              // this.tableProps.ipagination.total = 0;
              this.tableProps.dataSource = []
            }
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.tableProps.loading = false
        })
    },
    onTreeSelect(params) {
      this.mergeParams = params
      //   return
      this.tableProps.rowSelection.selectedRowKeys = data || []
      this.onSearchSubmit(params)
      console.log('test-onTreeSelect', params)
      const id = params.params.treeId
      let data = this.dataArray[id]
      // 树形结构选择响应事件
    },
    getDataByResult(result) {
      if (result) {
        return result.map((item) => {
          // 判断是否标记了带有子节点
          if (item[this.hasChildrenField] == '1') {
            let loadChild = { id: item.id + '_loadChild', name: 'loading...', isLoading: true }
            item.children = [loadChild]
          }
          return item
        })
      }
    },
    handleExpand(expanded, record) {
      console.log('test-handleExpand===>', record)
      // 判断是否是展开状态
      if (expanded) {
        this.tableProps.expandedRowKeys.push(record.id)
        if (record.children.length > 0 && record.children[0].isLoading === true) {
          let params = this.getQueryParams(1) // 查询条件
          params[this.pidField] = record.id
          params.hasQuery = 'false'
          getAction(this.url.childList, params).then((res) => {
            if (res.success) {
              if (res.result) {
                record.children = this.getDataByResult(res.result.records)
                this.tableProps.dataSource = [...this.tableProps.dataSource]
                this.calcPath(this.tableProps.dataSource, '')
              } else {
                record.children = ''
                record.hasChildrenField = '0'
              }
            } else {
              this.$message.warning(res.message)
            }
          })
        }
      } else {
        let keyIndex = this.tableProps.expandedRowKeys.indexOf(record.id)
        if (keyIndex >= 0) {
          this.tableProps.expandedRowKeys.splice(keyIndex, 1)
        }
      }
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.tableProps.rowSelection.selectedRowKeys = selectedRowKeys
      const id = this.pbsId
      this.dataArray[id + ''] = selectedRowKeys
      console.log('勾选', selectedRowKeys, '----')
      // 可能还需要其他处理逻辑...
    },
    onTableChange(params) {
      console.error('test-onTableChange', params)
      this.onSearchSubmit(params)
    },

    onSearchSubmit(params) {
      console.log('test-onSearchSubmit', params)
      this.loadData(params)
    },
    close() {
      this.dataArray = {}
      this.tableProps.rowSelection.selectedRowKeys = []
      this.$nextTick(() => {
        this.visable = false
      })
    },
  },
}
</script>
