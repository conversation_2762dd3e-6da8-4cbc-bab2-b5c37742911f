<template>
  <div class="gantt">
    <div class="scale-container">
      <a-radio-group v-model="scaleType" v-show="scaleShow" :options="options" @change="scaleTypeChange" />
      <a-icon :type="scaleShow ? 'caret-left' : 'caret-right'" @click="scaleShow = !scaleShow" />
    </div>
    <div ref="ganttContainer" :class="{ customgantt: showList }" id="gantt_here" class="w-100pre h-100pre"></div>
  </div>
</template>
<script>
import { gantt } from '@/utils/dhtmlx-gantt/js/dhtmlxgantt.js?v=8.0.1'
import '@/utils/dhtmlx-gantt/css/dhtmlxgantt.css?v=8.0.1'
import moment from 'dayjs'
export default {
  props: {
    showList: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      scaleType: 'day',
      scaleShow: true,
      taskId: '',
      options: [
        { label: '日', value: 'day' },
        { label: '周', value: 'week' },
        { label: '月', value: 'month' },
        { label: '季', value: 'quarter' },
        { label: '年', value: 'year' },
      ],
      gantt: '',
    }
  },
  watch: {
    showList(newValue, oldValue) {
      console.log('是否暂时显示', newValue)
      let layout = this.gantt.config.layout
      layout.cols[0].width = newValue ? 0 : 1143 // Move the splitter 100px to the left
      layout.cols[2].width = newValue ? 1654 : 500 // Move the splitter 100px to the left
      setTimeout(() => {
        // this.gantt.render()
        // this.gantt.resetLayout()
        // this.$forceUpdate()
        this.gantt.init(this.$refs.ganttContainer)
      }, 500)
    },
  },
  created() {
    window['clickGridButton'] = this.clickGridButton
    window['clickProcessButton'] = this.clickProcessButton
  },
  destroyed() {
    // 销毁gantt
    // gantt.destructor();
  },
  methods: {
    scaleTypeChange(e) {
      gantt.ext.zoom.setLevel(e.target.value)
    },
    clickProcessButton(task,name, type) {
      console.log('关联工序', task, type)
      this.$emit('clickProcessButton', task,name, type)
    },
    clickGridButton(task, type) {
      console.log('操作按钮', task, type)
      this.$emit('clickGridButton', task, type)
      // this.$nextTick(() => {
      //   let task = gantt.getTask(id)
      //   console.log('task', task)
      // })
    },
    getTaskById(code) {
      let item = ''
      gantt.eachTask((task) => {
        if (task.code === code) {
          item = task
          return
        }
      })
      return item
    },
    //所有配置选项都在 gantt.config 对象中声明。
    // 要设置所需的选项，只需按照本文档中的说明编写即可。
    // 请注意，配置选项应该在带有 dhtmlxGantt 初始化的代码行之前。
    setGanttConfig(data) {
      gantt.parse({ data: data })
      // gantt.destructor()
      // 将业务数据赋值给甘特图
      data.forEach((item) => {
        if (item.followTaskCodes) {
          let followTasks = item.followTaskCodes.split(',')
          followTasks.forEach((task) => {
            gantt.addLink({
              source: item.id,
              target: this.getTaskById(task).id,
              type: '0', // 0 表示普通连接线
            })
          })
        }
      })
    },
    initGanttConfig(data) {
      gantt.clearAll()
      // gantt.config['scales'] = [
      //   {
      //     unit: 'year',
      //     step: 1,
      //     format: '%Y年',
      //   },
      //   { unit: 'week', step: 1, format: '%m月 %w周' },
      // ]
      // gantt.config.xml_date = '%Y-%m-%d' // 模板可用于更改日期和标签的显示。
      gantt.i18n.setLocale('cn') //设置日期中文显示
      gantt.templates.task_text = function (start, end, task) {
        // return 换行需要加()
        return `<b class='task_text mx-20'>${task.name} 计划时间：${task.startDate} ~ ${task.endDate} 工期：${task.planDay}</b>`
      }
      gantt.config.autofit = true //列表宽度自适应
      gantt.config.autoscroll = false //把任务或者连线拖拽到浏览器屏幕外时，自动触发滚动效果，相当于拖动滚动条
      // gantt.config.autoscroll_speed = 50;
      gantt.config.drag_move = true //拖动移动
      // gantt.config.start_on_monday = true // 第一天将变为周日
      gantt.config.details_on_dblclick = false //双击task时，弹出lightbox弹出框
      gantt.config.grid_width = 400 //设置左侧表格的宽度  默认是350
      gantt.config.task_height = 15 //时间轴图表中，任务条形图的高度
      // gantt.config.min_column_width = 10;
      gantt.config.preserve_scroll = true //图表刷新后，滚动条的位置跟原来保持一致
      gantt.config.scale_height = 50 //设置时间轴的高度
      // gantt.config.link_wrapper_width = 10;
      // gantt.config.prevent_default_scroll = false;//阻止鼠标滚动事件冒泡
      gantt.config.show_links = true // 是否显示依赖连线
      // gantt.config.show_menu = false;// 是否显示右键菜单
      gantt.config.drag_lightbox = false //允许鼠标通过按住的lightbox头部拖拽的方式，调整lightbox的位置
      gantt.config.drag_links = false //允许通过拖拽的方式新增任务依赖的线条
      gantt.config.drag_move = false //允许用户拖动条形图来改变位置
      gantt.config.drag_progress = false //允许用户推拽条形图上用来调整进度百分比的小按钮
      // gantt.config.show_grid = true //隐藏表格
      // gantt.config.duration_unit = 'week' //工期计算的基本单位“minute”, “hour”, “day”, “week”, “month”, “year”
      // gantt.config.date_scale = "%F, %Y";
      // gantt.config.subscales = [
      // { unit: "week", step: 1, date: "%W" }
      // ];
      gantt.config.use_collections = true // 启用任务树功能
      gantt.config.open_tree_initially = true // 初始状态下展开所有任务
      gantt.config.row_height = 35
      gantt.config.open_split_tasks = true
      gantt.attachEvent('onLinkDblClick', function (link_id, e) {
        // 阻止默认行为
        e.preventDefault()
        return false
      })
      let startDate, endDate
      data.forEach((x) => {
        if (!startDate || startDate > new Date(x.startDate)) {
          startDate = new Date(x.startDate)
        }
        // if (!endDate || endDate < new Date(x.endDate)) {
        //   endDate = new Date(x.endDate)
        // }
      })
      if (startDate) gantt.config.start_date = moment(startDate).format('YYYY-MM-DD')
      // if (endDate) gantt.config.end_date = moment(endDate).format('YYYY-MM-DD')

      //月天
      let dayScale = [
        {
          unit: 'day',
          step: 1,
          format: function (date) {
            let zhouNum = gantt.date.date_to_str('%l')(date)
            let dayNum = gantt.date.date_to_str('%j')(date)
            zhouNum = zhouNum.replace('星期', '周')
            return `${dayNum} ${zhouNum}`
          },
        },
        { unit: 'month', step: 1, format: '%Y年%M' },
      ]
      //月周
      let weekscale = [
        {
          unit: 'month',
          step: 1,
          format: function (date) {
            var yearNum = gantt.date.date_to_str('%Y')(date)
            var monthNum = gantt.date.date_to_str('%M')(date)
            return `${yearNum}年${monthNum}`
          },
        },
        {
          unit: 'week',
          step: 1,
          format: function (date) {
            var weekNum = gantt.date.date_to_str('%w')(date)
            return `第${weekNum}周`
          },
        },
      ]
      //年月
      let monthScale = [
        { unit: 'month', format: '%M' },
        { unit: 'year', step: 1, format: '%Y年' },
      ]
      //年季
      let quarterScale = [
        { unit: 'year', step: 1, format: '%Y年' },
        {
          unit: 'quarter',
          step: 1,
          format: function (date) {
            var dateToStr = gantt.date.date_to_str('%M')
            var endDate = gantt.date.add(gantt.date.add(date, 3, 'month'), -1, 'day')
            return dateToStr(date) + ' - ' + dateToStr(endDate)
          },
        },
      ]
      // 年
      let yearScale = [{ unit: 'year', step: 1, format: '%Y年' }]

      var zoomConfig = {
        levels: [
          {
            name: 'day',
            scale_height: 50,
            min_column_width: 50,
            scales: dayScale,
          },
          {
            name: 'week',
            scale_height: 50,
            min_column_width: 60,
            scales: weekscale,
          },
          {
            name: 'month',
            scale_height: 50,
            min_column_width: 90,
            scales: monthScale,
          },
          {
            name: 'quarter',
            height: 50,
            min_column_width: 90,
            scales: quarterScale,
          },
          { name: 'year', scale_height: 50, min_column_width: 30, scales: yearScale },
        ],
      }
      gantt.ext.zoom.init(zoomConfig)

      gantt.ext.zoom.setLevel(this.scaleType)
      // 初始化甘特图
      // 布局
      // 布局是通过gantt.config.layout配置选项设置的。布局的默认配置如下：
      gantt.config.layout = {
        css: 'gantt_container',
        cols: [
          {
            width: 1143,
            rows: [
              {
                group: 'gantt',
                cols: [
                  {
                    rows: [
                      {
                        view: 'grid',
                        scrollX: 'gridScrollX',
                        scrollY: 'scrollVer',
                        scrollable: true,
                        class: 'left-list',
                      },
                      { view: 'scrollbar', id: 'gridScrollX' },
                    ],
                  },
                ],
              },
            ],
          },
          { resizer: true, width: 1 },
          {
            rows: [
              {
                group: 'gantt',
                cols: [
                  {
                    rows: [
                      { view: 'timeline', scrollX: 'scrollHor', scrollY: 'scrollVer', class: 'right-list' },
                      { view: 'scrollbar', id: 'scrollHor' },
                    ],
                  },
                  { view: 'scrollbar', id: 'scrollVer' },
                ],
              },
            ],
          },
        ],
      }

      // 自定义网格元素
      let colContent = (task) => {
        return `<span  class='pointer text-0096'  onclick="clickGridButton('${task.code}','view')">查看</span>`
      }
      let processContent = (task) => {
        if (task.hasChild === '0') {
          // console.log('自定义网格元素', task)
          // const data = JSON.stringify(task)
          return `<span  class='pointer text-0096'  onclick="clickProcessButton('${task.code}','${task.name}','view')">关联工序</span>`
        }
      }
      gantt.templates.grid_row_class = function (start, end, task) {
        if (task.highlight) {
          return 'highlight-row'
        }
        return ''
      }
      gantt.templates.task_class = function (start, end, task) {
        let css = ''
        // 根据任务的属性设置不同的样式
        if (task.pid == '0') {
          css += ' root-priority-task'
        } else if (task.followTaskCodes) {
          css += ' node-priority-task'
        } else {
          css += ' leaf-priority-task'
        }
        return css
      }
      gantt.init(this.$refs.ganttContainer)
      console.log('🚀 ~ setTimeout ~ gantt.collapseAl:', gantt.collapse)
      gantt.config.columns = [
        {
          name: 'index',
          label: '序号',
          align: 'center',
          min_width: 60,
        },
        {
          name: 'code',
          label: '编码',
          min_width: 80,
          align: 'center',
        },
        {
          name: 'name',
          tree: true,
          label: '名称',
          min_width: 360, //300
          align: 'left',
          template: function (task) {
            return `<span title="${task.name}">${task.name}</span>`
          },
        },
        // { name: 'taskType', label: '类型', min_width: 100, align: 'center' },
        {
          name: 'startDate',
          label: '计划开始日期',
          min_width: 100,
          align: 'center',
        },
        {
          name: 'endDate',
          label: '计划完成日期',
          min_width: 100,
          align: 'center',
        },

        {
          name: 'planDay',
          label: '计划工期(天)',
          min_width: 100,
          align: 'center',
        },
        {
          name: 'buttons',
          label: '',
          align: 'center',
          min_width: 60,
          template: processContent,
        },
        {
          name: 'buttons',
          label: '操作',
          align: 'center',
          min_width: 60,
          template: colContent,
          resize: true,
        },
      ]
      this.gantt = gantt
    },
  },
}
</script>
<style lang="scss" scoped>
.line {
  background-color: #fff;
}

.gantt {
  height: 68vh;
  width: 100%;
  position: relative;
  .scale-container {
    position: absolute;
    top: 1px;
    right: 1px;
    z-index: 1;
    background: #fff;
  }
}
::v-deep .highlight-row {
  background-color: yellow !important;
  font-weight: bold;
  padding: 2px 4px;
  border-radius: 3px;
}
::v-deep .gantt_task_content {
  text-align: left !important;
}
::v-deep .gantt_task_line {
  border-radius: 40px;
  // background-color: #1890ff !important;
  border: 0 !important;
}

::v-deep .gantt_bar_task {
  height: 20px !important;
  line-height: 20px !important;
  // top: 7px !important;
  margin-top: 5px;
}
::v-deep .gantt_task .gantt_task_scale .gantt_scale_cell {
  color: #000 !important;
  font-weight: 550 !important;
}

::v-deep .root-priority-task {
  background-color: #52c41a !important; /* 设置高优先级任务的背景颜色 */
}
::v-deep .node-priority-task {
  background-color: #74a3ce !important; /* 设置高优先级任务的背景颜色 */
}
::v-deep .leaf-priority-task {
  background-color: #1890ff !important; /* 设置低优先级任务的背景颜色 */
}

::v-deep .gantt_grid_head_cell {
  background-color: #e8f4ff;
  color: #000;
  font-weight: bold;
}

::v-deep .gantt_row_task:hover {
  background-color: #e8f4ff !important;
}
::v-deep .gantt_scale_cell {
  background-color: #e8f4ff !important;
}

::v-deep .gantt_grid_data .gantt_row.gantt_selected,
.gantt_grid_data .gantt_row.odd.gantt_selected,
.gantt_task_row.gantt_selected {
  background-color: #e8f4ff !important;
}

::v-deep .gantt_layout_root {
  display: flex !important;
  > .gantt_layout_cell:nth-child(3) {
    flex: 1 !important;
    .gantt_layout_x {
      width: 100% !important;
      > .gantt_layout {
        width: 100% !important;
        > .timeline_cell {
          width: 100% !important;
        }
      }
    }
  }
}
.customgantt {
  ::v-deep .gantt_layout_root {
    .right-list {
      width: 100% !important;
    }
    > .gantt_layout_cell:nth-child(1) {
      transition: width 0.5s ease; /* 过渡时间为0.5秒，使用 ease 缓动函数 */
      width: 0px !important;
    }
    > .gantt_layout_cell:nth-child(3) {
      transition: width 0.5s ease; /* 过渡时间为0.5秒，使用 ease 缓动函数 */
      // width: auto !important;
    }
  }
}
::v-deep .scrollVer_cell {
  right: 6px !important;
}
</style>

<!-- 1、data里面的部分属性的key是不能更改的，比如id，parent，start_date、end_date、progress、open
  links里面的全部属性的key是不能更改的id source target type
  2、data如果type是project，那么这条数据的开始时间会取其里面所有数据的最早开始时间，结束时间会取里面所有数据的最晚开始时间，如果这条数据里面的所有数据都是空数据，那么start_date会甘特图渲染的最早时间，end_date是start_date的后一天，这样数据就会不准确？
  解决方案： data里加个unscheduled属性来控制是否渲染这条数据，需要注意的是在所有涉及到日期的地方都要加，如tooltips_text 、columns、 拖拽等等
  3、 dhtmlx-gantt都是下划线分割，而且api中都是这样,但在layout中min_width、max_width不生效，要用minWidth、maxWidth替换才生效。
  4、排序后的数据默认从页面获取的row元素可能是不准确的，需要从dataStroe中获取。
  5、甘特图在不占全屏的情况下， order_branch = true，拖拽会有限制？
  
  作者：ygunoil
  链接：https://juejin.cn/post/6930900493602390024
  来源：稀土掘金
  著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。 -->
