<template>
  <div class="list-page pageMain sea-weather-live-list" id="planingProcess">
    <table-layout
      :rightTitle="'进度计划列表'"
      :searchProps="searchProps"
      @search-submit="search"
      :tableProps="tableProps"
      @search-reset="searchReset"
    >
      <template slot="table">
        <div class="flex mb-10 w-100pre">
          <span class="left-auto"
            ><a-button @click="changeList" class="mr-10 button-show">
              <a-icon :type="showList ? 'eye-invisible' : 'eye'" />{{ showList ? '展开' : '收起' }}列表</a-button
            >
            <a-upload
              :showUploadList="false"
              accept=".xls,.xlsx"
              name="file"
              :multiple="true"
              :action="uploadAction"
              :headers="headers"
              :data="{ version: version }"
              @change="handleChange"
            >
              <a-button class="button-show"> <a-icon type="upload" />导入计划</a-button>
            </a-upload></span
          >
        </div>
        <GanttChart
          :showList="showList"
          ref="gantt"
          class="GanttChart"
          @clickGridButton="clickGridButton"
          @clickProcessButton="clickProcessButton"
        >
        </GanttChart>
      </template>
    </table-layout>
    <a-modal v-model="visable" title="详情" cancel-text="关闭" @ok="visable = false" :width="1100" :footer="null">
      <a-table :data-source="dataSource" :columns="columns" rowKey="id" :pagination="false"> </a-table>
      <div class="custom-footer">
        <a-button type="primary" @click="visable = false">关闭</a-button>
      </div>
    </a-modal>
    <Modal ref="Modal" ></Modal>
  </div>
</template>
<script>
import TableLayout from '@/components/tableLayout/TableLayout.vue'
import GanttChart from './GanttChart.vue'
import Modal from './Modal.vue'
import { getAction } from '@/api/manage'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import Vue from 'vue'
export default {
  name: 'VueChart',
  components: {
    GanttChart,
    TableLayout,
    Modal,
  },
  data() {
    return {
      showList: false,
      tableProps: {
        ipagination: false,
      },
      visable: false, //详情是否可见
      dataSource: [],
      columns: [
        {
          title: '编码',
          dataIndex: 'code',
          width: 120,
          align: 'center',
          // customRender: (t, r, index) => {
          //   return parseInt(index) + 1
          // },
        },
        {
          title: '名称',
          dataIndex: 'name',
          align: 'center',
        },
        {
          title: '计划开始日期',
          dataIndex: 'startDate',
          align: 'center',
          width: 120,
        },
        {
          title: '计划结束日期',
          dataIndex: 'endDate',
          align: 'center',
          width: 120,
        },
        {
          title: '计划工期(天)',
          dataIndex: 'planDay',
          align: 'center',
          width: 120,
        },
        {
          title: '紧后工作',
          dataIndex: 'followTaskCodes',
          align: 'center',
          customRender: (t, r, index) => {
            if (!t) {
              return t
            }
            return t
              .replaceAll('\n', '')
              .split(',')
              .map((item) => {
                let obj = this.listData.find((x) => x.code == item)
                if (obj) {
                  return obj.code + obj.name
                } else {
                  return ''
                }
              })
              .join(',\n')
          },
        },
        {
          title: '类型',
          dataIndex: 'followTaskRelationship',
          align: 'center',
          customRender: (t, r, index) => {
            if (!t) {
              return t
            }
            return t
              .replaceAll('\n', '')
              .split(',')
              .map((item) => {
                switch (item) {
                  case 'FS':
                    return '完成-开始(FS)'
                  case 'FF':
                    return '完成-完成(FF)'
                  case 'SS':
                    return '开始-开始(SS)'
                  case 'SF':
                    return '开始-完成(SF)'
                  default:
                    return ''
                }
              })
              .join(',\n')
          },
        },
        {
          title: '延隔时间(天)',
          dataIndex: 'delayDays',
          align: 'center',
          width: 120,
        },
      ],
      headers: {},
      uploadAction: window._CONFIG['domianURL'] + '/project/projectPlan/importExcel',
      // 搜索组件的props
      searchProps: {
        formModel: {
          version: '',
          name: '',
        },
        formItems: [
          { key: 'name', label: '任务名称' },
          {
            key: 'version',
            label: '版本号',
            type: 'select',
            options: [],
          },
        ],
      },
      version: 0,
      listData: [],
      boolShow:false
    }
  },
  created() {
    const token = Vue.ls.get(ACCESS_TOKEN)
    this.headers = { 'X-Access-Token': token }
  },
  mounted() {
    this.getTableData()
    getAction('/project/projectPlan/version').then((res) => {
      this.searchProps.formItems[1].options = res.result.map((x) => {
        return {
          label: `V${x.version}.0`,
          value: x.version,
        }
      })
    })
    setTimeout(()=>{
      this.boolShow=true
    },2000)
  },
  // destroyed() {
  //   this.$refs.gantt.destructorGantt()
  // },
  methods: {
    // gantt_layout_content

    changeList() {
      this.showList = !this.showList
    },
    searchReset(params) {
      console.log('searchReset', params)
      this.search(params)
    },
    async search(e) {
      await this.getTableData(e.params)
      this.listData.forEach((task) => {
        task.highlight = e.params.name ? task.name.toLowerCase().includes(e.params.name.toLowerCase()) : false
      })
      this.$refs.gantt.setGanttConfig([...this.listData])
    },
    //关联工序
    clickProcessButton(code, name, view) {
      // let data =JSON.stringify(task)
      // console.log('clickGridButton', data, view)
      this.$refs.Modal.visable = true
      this.$refs.Modal.title = code + name
      this.$refs.Modal.code = code
      this.$refs.Modal.version = this.version
    },
    clickGridButton(type, view) {
      //type是编码
      console.log('clickGridButton', type, view)
      let index = this.listData.findIndex((x) => x.code == type)
      if (index >= 0) {
        this.dataSource = [this.listData[index]]
        this.visable = true
        console.log('clickGridButton', this.dataSource)
      }
    },
    handleChange(info) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList)
      }
      // 已经上传成功
      if (info.file.status === 'done') {
        this.getTableData()
        this.$message.success(`${info.file.name} file uploaded successfully`)
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} file upload failed.`)
      }
    },
    async getTableData(params = {}) {
      let { version } = params
      const { result, success } = await getAction('/project/projectPlan/list', { version })
      console.log('列表数据', result)
      if (!success) {
        this.$message.error(result.message)
        return
      }
      if (result && result.length > 0) {
        this.searchProps.formModel.version = result[0].version
        this.version = result[0].version
        this.listData = result
        this.initGnnt(result)
      }
    },
    initGnnt(arr) {
      console.log('arr', arr)
      // key是不能更改的，比如id，parent，start_date、end_date、progress、open
      arr.forEach((item, index) => {
        item.start_date = item.startDate.split('-').reverse().join('-')
        // item.end_date = item.endDate
        item.progress = 0 //这个展示为0  现在没有工期进度
        item.parent = item.pid
        item.open = true
        item.index = index + 1
        item.duration = item.planDay
      })
      this.$refs.gantt.initGanttConfig([...arr]) //这里需要优化一下
      this.$refs.gantt.setGanttConfig([...arr])
    },
  },
}
</script>

<style lang="scss" scoped>
.GanttChart {
  width: 100%;
}
.pageMain {
  --index: 500;
  --left: 54px;
  --top: 50px;
  --right: 54px;
  --bottom: 16px;
  width: 100%;
  height: 100%;
  position: relative;
}
.button-show {
  background: #3254ff;
  color: #fff;
}
.custom-footer {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}
::v-deep .highlight-task {
  background-color: yellow !important; /* 设置高亮颜色 */
}
</style>
