<template>
  <div :ref="refName" class="w-100pre h-95"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'DonutChart',
  props: {
    refName: {
      type: String,
      required: true,
      default: () => 'chart',
    },
    // chartData: {
    //   type: Object,
    //   required: true,
    // },
  },
  data() {
    return {
      chartData: null,
      chart: null,
      option: null,
    }
  },
  mounted() {
    // this.initChart()
  },
  methods: {
    setOption() {
      // this.chart.setOption(JSON.parse(JSON.stringify(this.option)))
      this.initChart()
    },
    /**
     * 初始化图表
     * 使用ECharts库创建一个饼图，并配置图表的各种选项，如提示框、图例、系列等。
     * 此函数还处理了窗口大小变化时图表的自适应调整。
     */
    initChart() {
      // 计算所有系列值的总和
      const total = this.chartData.series.reduce((sum, item) => sum + item.value, 0)
      const data1 = this.chartData.series.map((item) => {
        return {
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color,
          },
        }
      })
      // 初始化ECharts实例
      const chart = echarts.init(this.$refs[this.refName])
      // 配置图表的选项
      const option = {
        tooltip: {
          trigger: 'item',
        },
        legend: {
          show:false,
        },
        series: [
          {
            type: 'pie',
            radius: ['60%', '85%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center',
              fontSize: 14,
              formatter: () => {
                const total = this.chartData.series.reduce((sum, item) => sum + item.value, 0)
                return `{a|${total}}\n{b|总数量}`
              },
              rich: {
                a: {
                  color: '#000',
                  fontWeight: 800,
                  fontSize: 18,
                  height: 30,
                },
                b: {
                  color: '#86909C',
                  fontSize: 12,
                },
              },
            },
            emphasis: {
              label: {
                show: false,
                fontSize: '20',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: data1,
          },
        ],
        graphic: {
          type: 'group',
          left: 'center',
          top: '32%',
          elements: [
            {
              type: 'text',
              left: 'center',
              top: '37%',
              style: {
                text: total,
                textAlign: 'center',
                fill: '#000',
                fontSize: 16,
                fontWeight: 'bold',
              },
            },
            {
              type: 'text',
              left: 'center',
              top: '55%',
              style: {
                text: '总数量',
                textAlign: 'center',
                fill: '#86909C',
                fontSize: 14,
              },
            },
          ],
        },
      }
      this.option = option
      this.chart = chart
      chart.setOption(option)
      // 监听窗口resize事件，以调整图表大小
      window.addEventListener('resize', () => {
        chart.resize()
      })
    },
  },
}
</script>

<style scoped>
/* 可选的样式 */
</style>
