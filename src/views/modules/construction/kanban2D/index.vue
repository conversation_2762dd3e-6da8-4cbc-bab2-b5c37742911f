<template>
  <div class="pageMain position-relative" id="getContainer">
    <Map @clickMap="clickMap" @initMapCompleted="initMapCompleted" :defaultLegend="showAnalysis" :defaultTypeChange="showAnalysis" :defaultShip="showAnalysis" ref="Map">
      <template slot="right-top">
        <div v-if="showAnalysis" class="w-289 h-270 rounded-6 p-16 bg-fff">
          <div>
            <div class="flex align-center">
              <div class="h-10 w-3 mr-5" style="background-color: #3254ff"></div>
              <div class="font-14" style="font-weight: 800">风机整体施工进度</div>
            </div>
            <div>
              <a-row>
                <a-col :span="13">
                  <circleecharts class="w-100pre" ref="chartData1" :refName="'chartData1'" />
                </a-col>
                <a-col :span="11">
                  <div class="flex flex-column justify-center h-95">
                    <div v-for="(item, index) in chartData1.series" :key="index"  class="flex align-center font-14 " style="color: #333;">
                      <div class="len-circle" :style="{backgroundColor:item.color}"></div>
                      <div class="mx-10">{{item.name}}</div>
                      <div>{{item.value}}</div>
                    </div>
                  </div>
                </a-col>
              </a-row>
            </div>
          </div>
          <div>
            <div class="flex align-center">
              <div class="h-10 w-3 mr-5" style="background-color: #3254ff"></div>
              <div class="font-14" style="font-weight: 800">线缆整体施工进度</div>
            </div>
            <div>
              <a-row>
                <a-col :span="13">
                  <circleecharts ref="chartData2" :refName="'chartData2'" />
                </a-col>
                <a-col :span="11">
                  <div class="flex flex-column justify-center h-95">
                    <div v-for="(item, index) in chartData2.series" :key="index"  class="flex align-center font-14 " style="color: #333;">
                      <div class="len-circle" :style="{backgroundColor:item.color}"></div>
                      <div class="mx-10">{{item.name}}</div>
                      <div>{{item.value}}</div>
                    </div>
                  </div>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>
      </template>
    </Map>
    <j-modal
      :mask="false"
      :getContainer="getContainer"
      :width="width"
      :visible="visible"
      :title="pbsName"
      @ok="handleOk"
      :okButtonProps="{ class: { 'jee-hidden': false } }"
      @cancel="handleOk"
      cancelText="关闭"
      :footer="null"
    >
      <modal-form ref="modalForm"></modal-form>
    </j-modal>
  </div>
</template>

<script>
import Map from '@/components/Map'
import { getAction, postAction } from '@/api/manage'
import circleecharts from './echarts.vue'
import modalForm from './modalForm.vue'
export default {
  components: { Map, circleecharts, modalForm },
  props: {
    // 是否显示整体进度分析
    showAnalysis: {
      type: Boolean,
      default: true
    },
    // 详情弹窗父元素id
    container: {
      type: String,
      default: 'getContainer'
    },
  },
  data() {
    return {
      pbsName: '',
      width: '100%',
      visible: false,
      chartData1: {
        legend: ['已完成', '施工中', '未完成'],
        series: [
          { value: 0, name: '已完成', color: '#26C04E' },
          { value: 0, name: '施工中', color: '#3254FF' },
          { value: 0, name: '未完成', color: '#FF8541' },
        ],
      },
      chartData2: {
        legend: ['已完成', '施工中','未施工'],
        series: [
          { value: 0, name: '已完成', color: '#26C04E' },
          { value: 0, name: '施工中', color: '#3254FF' },
          { value: 0, name: '未施工', color: '#FF8541' },
        ],
      },
      map: null,
      url: {
        // list: '/construction/monitorDolphinLog/list',
        list: '/construction/monitorDolphinLog/queryByMonth',
        PolarPlot: '/construction/watchDolphinLogList/getPolarPlotData',
        report: '/construction/watchDolphinLogList/getReport',
        condition: '/dataPanel/condition',
        schedule: '/dataPanel/schedule',
      },
      pageParams: {
        pageNo: 1,
        pageSize: 200,
      },
    }
  },
  mounted() {
    this.condition()
  },
  computed: {},
  created() {},
  beforeDestroy() {
    if (this.map) {
      this.map.destroy()
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.visible = val
      }
    },
  },
  methods: {
    async condition() {
      const res = await getAction(this.url.condition)
      if (res.success) {
        const { fjSum, fjcomplete, fjnotStarted, fjunderway, hlSum, hlcomplete, hlnotStarted, hlunderway } = res.result
        this.chartData1.series[0].value = fjcomplete
        this.chartData1.series[1].value = fjunderway
        this.chartData1.series[2].value = fjnotStarted
        this.chartData2.series[0].value = hlcomplete
        this.chartData2.series[1].value = hlunderway
        this.chartData2.series[2].value = hlnotStarted
        this.$nextTick(() => {
          this.$refs.chartData1.chartData = this.chartData1
          this.$refs.chartData2.chartData = this.chartData2
          this.$refs.chartData1.setOption()
          this.$refs.chartData2.setOption()
        })
      }
    },
    handleOk() {
      this.visible = false
    },
    getContainer() {
      console.log(this.container)
      let dom = document.getElementById(this.container)
      console.log('dom', dom)
      return dom
    },
    initMapCompleted() {},
    clickMap(mark, markerType) {
      console.log('🚀 ~ clickMap ~ mark:', markerType, mark)
      if (mark.fanSection == '0') {
        this.visible = true
        this.$nextTick(() => {
          this.pbsName = mark.name
          let params = { pbsId: mark.id }
          getAction(this.url.schedule, params).then((res) => {
            if (res.success) {
              this.$refs.modalForm.init(res.result)
            }
          })
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.len-circle{
  width: 15px;
  height: 15px;
  border-radius: 50%;
}
.pageMain {
  --index: 500;
  --left: 54px;
  --top: 50px;
  --right: 54px;
  --bottom: 16px;
  width: 100%;
  height: 100%;
  #map {
    height: calc(88vh - 5px);
  }
  .map-tool {
    display: none;
    position: absolute;
    bottom: var(--bottom);
    right: var(--right);
    z-index: var(--index);
    .tool-item {
      width: 36px;
      height: 36px;
      border-radius: 4px;
      margin-bottom: 10px;
      cursor: pointer;
    }
  }
}
::v-deep .ant-modal {
  height: 100% !important;
  .ant-modal-content {
    height: 100% !important;
    display: flex;
    flex-direction: column;
    .ant-modal-body {
      flex: 1;
      overflow: hidden;
    }
  }
}

::v-deep .ant-modal-wrap {
  position: absolute;
}

::v-deep .ant-modal {
  top: 0 !important;
  padding-bottom: 0 !important;
}
</style>
