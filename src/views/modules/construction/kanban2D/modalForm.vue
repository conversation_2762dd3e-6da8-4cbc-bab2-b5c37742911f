<template>
  <div class="w-100pre h-100pre flex flex-column">
    <div class="flex align-center mb-10">
      <div class="mr-20">施工进度</div>
      <a-progress :percent="schedule" status="active" :strokeWidth="16" :strokeColor="strokeColor" style="width: 50%" />
      <div class="ml-50 pt-6">
        <span class="mr-10 font-w-700">施工耗时</span><span class="font-w-800">{{ sumUseTime.toFixed(1) }}h</span>
      </div>
    </div>
    <div class="flex-1" style="overflow-y: auto; overflow-x: hidden">
      <a-row :gutter="[16, 16]">
        <!-- 鼠标艺术效果 -->
        <a-col class="h-230 mb-20" v-for="item in formData" :key="item.id" :span="6">
          <div
            @mouseenter="mouseenter($event, item)"
            @mouseleave="mouseleave($event, item)"
            class="rounded-6 index-100"
            :class="[item.mouseenter ? 'hover-active' : 'hover-default', item.endTime ? 'item-active' : 'item-default']"
            :style="styleObject"
          >
            <div class="item">
              <div class="h-48 flex mb-10">
                <a-avatar
                  shape="square"
                  class="avatar"
                  :src="require(`@/assets/${item.endTime ? 'log' : 'log2'}.png`)"
                />
                <div class="h-100pre flex flex-column justify-between ml-10">
                  <div class="font-16 font-w-700">
                    <a-tooltip v-if="item.name.length > 18" placement="topLeft">
                      <template slot="title">
                        <span>{{ item.name }}</span>
                      </template>
                      <span>{{ item.name.length <= 18 ? item.name : item.name.substr(0, 18) + '...' }}</span>
                    </a-tooltip>
                    <span v-if="item.name.length <= 18" class="title">{{ item.name }}</span>
                  </div>
                  <div class="font-14 text-ellipsis text-fff px-5 lilun">理论耗时：{{ item.theoryHours }}小时</div>
                </div>
              </div>
              <div class="h-56 w-100pre flex align-center justify-between px-10 center-time">
                <div class="py-10 flex flex-column justify-between">
                  <div>
                    <span>开始时间：</span>
                    <span class="font-w-700">{{ item.startTime ? item.startTime.substring(0,16) : '--' }}</span>
                  </div>
                  <div>
                    <span>结束时间：</span>
                    <span class="font-w-700">{{ item.endTime ? item.endTime.substring(0,16) : '--' }}</span>
                  </div>
                </div>
                <div style="border-left: 1px solid #c9cdd4" class="h-47 w-100 flex align-end justify-center font-w-600">
                  <div
                    :style="
                      item.startTime
                        ? item.theoryHours >= item.useTime
                          ? 'color: #00b42a'
                          : 'color: #ff7d00'
                        : 'color: #000000'
                    "
                  >
                    <span class="mr-5">耗时</span><span class="font-24">{{ item.useTime ? item.useTime : '--' }}h</span>
                  </div>
                </div>
              </div>
              <div class="double-line my-10"></div>
              <div>
                <a-row :gutter="[16, 0]">
                  <a-col :span="12">
                    <div style="color: #86909c" class="mb-5 font-14">耗时最高{{ item.pbsType }}</div>
                    <a-tooltip >
                      <template slot="title">
                        <span>{{ item.maxName }}</span>
                      </template>
                      <div
                        class="w-100pre h-24 font-w-700 line-24 px-5 text-ellipsis haoshi"
                        style="border: 1px solid #c9cdd4"
                      >
                        <span> {{ item.maxName }}</span>
                      </div>
                    </a-tooltip>
                  </a-col>
                  <a-col :span="12">
                    <div style="color: #86909c" class="mb-5 font-14">耗时最低{{ item.pbsType }}</div>

                    <a-tooltip >
                      <template slot="title">
                        <span> {{ item.minName }}</span>
                      </template>
                      <div
                        class="w-100pre h-24 font-w-700 line-24 px-5 text-ellipsis haoshi"
                        style="border: 1px solid #c9cdd4"
                      >
                        {{ item.minName }}
                      </div>
                    </a-tooltip>
                  </a-col>
                </a-row>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DonutChart',
  data() {
    return {
      formData: [],
      buttonWidth: 70,
      schedule: 0,
      sumUseTime: 0,
      strokeColor: {
        '0%': '#5F96FF',
        '100%': '#3254FF',
      },
    }
  },
  mounted() {},
  computed: {},
  methods: {
    styleObject() {
      return {
        transform: this.isHovered ? 'translateY(-10px)' : 'translateY(0)',
        transition: 'transform 0.5s',
      }
    },
    mouseenter(e, record) {
      console.log('mouseenter', e, record)
      this.$set(record, 'mouseenter', true)
    },
    mouseleave(e, record) {
      console.log('mouseleave', e, record)
      this.$set(record, 'mouseenter', false)
    },
    init(data) {
      this.formData = [...data.processList]
      this.schedule = data.schedule
      this.sumUseTime = data.sumUseTime
    },
  },
}
</script>
<style lang="scss" scoped>
.item {
  border: 1px solid #e5e6eb;
  border-radius: 6px 6px 6px 6px;
  height: 100%;
  padding: 16px;
}
.avatar {
  width: 48px;
  height: 48px;
}

::v-deep .ant-progress-text {
  color: #3254ff;
}
::v-deep .ant-progress-inner {
  background-color: #d7ddfd;
}
.lilun {
  width: 140px;
}

.h1 {
  background: -webkit-linear-gradient(
    135deg,
    #0eaf6d,
    #ff6ac6 25%,
    #147b96 50%,
    #e6d205 55%,
    #2cc4e0 60%,
    #8b2ce0 80%,
    #ff6384 95%,
    #08dfb4
  );
  /* 文字颜色填充设置为透明 */
  -webkit-text-fill-color: transparent;
  /* 背景裁剪，即让文字使用背景色 */
  -webkit-background-clip: text;
  /* 背景图放大一下，看着柔和一些 */
  -webkit-background-size: 200% 100%;
  /* 应用动画flowCss 12秒速度 无限循环 线性匀速动画*/
  -webkit-animation: flowCss 12s infinite linear;
}

@-webkit-keyframes flowCss {
  0% {
    /* 移动背景位置 */
    background-position: 0 0;
  }

  100% {
    background-position: -400% 0;
  }
}

.h1:hover {
  -webkit-animation: flowCss 4s infinite linear;
}
.item-active {
  background-color: #e0e9ff;
  .double-line {
    border: 1px dashed #86909c;
  }
  .center-time {
    background-color: #fff;
  }
  .lilun {
    background-color: #00b42a;
  }
  .title {
    color: #333;
  }
  .haoshi {
    background: linear-gradient(180deg, #5f96ff 0%, #3254ff 100%) !important;
    color: #fff;
    border-radius: 2px !important;
  }
}

.item-default {
  background-color: #fff;
  .double-line {
    border: 1px dashed #e5e6eb;
  }
  .center-time {
    background-color: #f2f3f5;
  }
  .lilun {
    background-color: #86909c;
  }
  .title {
    color: #86909c;
  }
  .haoshi {
    background: #f2f3f5 !important;
    border-radius: 2px !important;
  }
}
.hover-active {
  transform: translateY(-10px);
  transition: transform 0.3s;
}
.hover-default {
  transform: translateY(0px);
  transition: transform 0.3s;
}

// 处理偏移 导致被覆盖
::v-deep .ant-row {
  padding-top: 15px;
}
</style>
