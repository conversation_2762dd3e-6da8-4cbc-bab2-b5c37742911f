<template>
  <div class="list-page sea-weather-live-list">
    <table-layout
      :rightTitle="'进度填报列表'"
      :searchProps="searchProps"
      @search-submit="loadData"
      @table-change="loadData"
      @search-reset="searchReset"
    >
      <template slot="table">
        <div class="flex mb-10 w-100pre">
          <span class="left-auto flex">
            <div class="left-auto flex align-center mr-80 font-14" style="color: #4e5969">
              <div class="">图例：</div>
              <div class="plan mr-5"></div>
              <div class="mr-20">计划</div>
              <div class="normal mr-5"></div>
              <div class="mr-20">正常</div>
              <div class="delay mr-5"></div>
              <div>延期</div>
            </div>
            <a-button @click="changeList" class="mr-10 button-show">
              <a-icon :type="showList ? 'eye-invisible' : 'eye'" />{{ showList ? '展开' : '收起' }}列表</a-button
            >
            <a-button class="left-auto button-show" @click="handleClick"> 进度分析</a-button></span
          >
        </div>
        <GanttChart ref="gantt" :showList="showList" @add="add" class="GanttChart"> </GanttChart>
      </template>
    </table-layout>
    <modal ref="modalForm" @ok="modalOk"></modal>
    <scheduleModel ref="scheduleModel"></scheduleModel>
  </div>
</template>
<script>
import TableLayout from '@/components/tableLayout/TableLayout.vue'
import GanttChart from './GanttChart.vue'
import Modal from './components/Modal'
import scheduleModel from './components/scheduleModel'
import moment from 'moment'
import { getAction } from '@/api/manage'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import Vue from 'vue'
export default {
  name: 'VueChart',
  components: {
    GanttChart,
    TableLayout,
    Modal,
    scheduleModel,
  },
  data() {
    return {
      showList: false,
      headers: {},
      uploadAction: window._CONFIG['domianURL'] + '/project/projectPlan/importExcel',
      // 搜索组件的props
      searchProps: {
        formModel: {
          name: '',
          status: '',
          version: '',
        },
        formItems: [
          { key: 'name', label: '任务名称' },
          {
            key: 'version',
            label: '版本号',
            type: 'select',
            options: [],
          },
          {
            key: 'status',
            label: '任务状态',
            type: 'select',
            options: [
              { label: '未开始', value: '0' },
              { label: '进行中', value: '1' },
              { label: '已完成', value: '2' },
            ],
          },
        ],
      },
      versionList: [],
      version: 0,
      show: false,
      task: null,
      listData: [],
    }
  },
  created() {
    const token = Vue.ls.get(ACCESS_TOKEN)
    this.headers = { 'X-Access-Token': token }
  },
  mounted() {
    this.show = false
    this.getTableData()
    getAction('/project/projectPlan/version').then((res) => {
      this.searchProps.formItems[1].options = res.result.map((x) => {
        return {
          label: `V${x.version}.0`,
          value: x.version,
        }
      })
    })
  },
  // destroyed() {
  //   this.$refs.gantt.destructorGantt()
  // },
  methods: {
    changeList() {
      this.showList = !this.showList
    },
    searchReset(params) {
      this.loadData(params)
    },
    loadData(e) {
      this.getTableData(e.params)
    },
    async getTableData(params = {}) {
      let { version, status, name } = params
      const { result, success } = await getAction('/project/projectPlanReport/list', { version })
      this.show = true
      if (!success) {
        this.$message.error(result.message)
        return
      }
      if (result && result.length > 0) {
        this.searchProps.formModel.version = result[0].version
        this.listData = result
        this.$nextTick(() => {
          this.initGnnt(result, { status, name })
        })
      }
    },
    initGnnt(arr, params) {
      console.log('元数据', JSON.parse(JSON.stringify(arr)))
      // key是不能更改的，比如id，parent，start_date、end_date、progress、open
      let cellWidth = 50
      arr.forEach((item, index) => {
        item.start_date = item.startDate.split('-').reverse().join('-')
        item.progress = 0 //这个展示为0  现在没有工期进度
        item.parent = item.pid
        item.open = true
        item.index = index + 1
        item.duration = item.planDay

        // 计算起点
        // 修改方案使用百分比计算 要计算出计划的开始时间 和计划结束时间  计算出相对的长度 即 计算出相对于白头的占比
        // if (item.projectPlanReport && item.projectPlanReport.actualStartDate) {
        //   item.left = moment(item.projectPlanReport.actualStartDate).diff(moment(item.startDate), 'days')*cellWidth
        //   let actualEndDate = item.projectPlanReport.actualEndDate
        //   if (!actualEndDate) {
        //     actualEndDate = moment().format('YYYY-MM-DD')
        //   }
        //   // 计算宽度
        //   item.width = moment(actualEndDate).diff(moment(item.projectPlanReport.actualStartDate), 'days') + 1*cellWidth
        //   // 实际结束等于计划 结束：橙色，实际工期大于计划工期：红色，实际工期小于计划工期：绿色。
        //   //计算计划的天数
        //   if (actualEndDate > item.endDate) {
        //     item.progressBg = '#F53F3F'
        //   } else {
        //     item.progressBg = '#00B42A'
        //   }
        // } else {
        //   item.left = 0
        //   item.width = 0
        //   item.progressBg = '#00B42A'
        // }
        // 方案二 使用百分比

        if (item.projectPlanReport && item.projectPlanReport.actualStartDate) {
          // 计算出开始结束事件的格
          let palnCell = moment(item.endDate).diff(moment(item.startDate), 'days') + 1
          let left = moment(item.projectPlanReport.actualStartDate).diff(moment(item.startDate), 'days')
          // 计算开始位置
          item.left = (left / palnCell) * 100

          let actualEndDate = item.projectPlanReport.actualEndDate
          if (!actualEndDate) {
            actualEndDate = moment().format('YYYY-MM-DD')
          }
          // 计算宽度
          let width = moment(actualEndDate).diff(moment(item.projectPlanReport.actualStartDate), 'days') + 1
          // 实际结束等于计划 结束：橙色，实际工期大于计划工期：红色，实际工期小于计划工期：绿色。
          // 计算宽度
          item.width = (width / palnCell) * 100
          console.log('计算开始位置', item.left, item.width)
          //计算计划的天数
          if (actualEndDate > item.endDate) {
            item.progressBg = '#F53F3F'
          } else {
            item.progressBg = '#00B42A'
          }
        } else {
          item.startCell = 0
          item.widthCell = 0
          item.progressBg = '#00B42A'
        }
        let { status, name } = params
        let projectPlanReport = item.projectPlanReport || {}
        if (status && name) {
          item.highlight =
            item.name.toLowerCase().includes(name.toLowerCase()) && (projectPlanReport.status || '0') == status
        } else {
          item.highlight =
            (name ? item.name.toLowerCase().includes(name.toLowerCase()) : false) ||
            (!status ? false : (projectPlanReport.status || '0') == status)
        }
      })
      console.log('最终的数据', JSON.parse(JSON.stringify(arr)))
      this.$refs.gantt.initGanttConfig(JSON.parse(JSON.stringify(arr))) //这里需要优化一下
      this.$refs.gantt.setGanttConfig(JSON.parse(JSON.stringify(arr)))
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    /**
     * @description 进度填报
     * @param {*} name
     * @param {*} taskType
     */
    add(task) {
      this.task = task
      let { name, taskType, code, projectPlanReport } = task
      let { actualStartDate, actualEndDate, remark, id, status } = projectPlanReport || {}
      this.$refs.modalForm.edit({
        baseCode: code,
        name,
        taskType,
        actualStartDate,
        actualEndDate,
        remark,
        id,
        status,
      })
    },
    /**
     * @description 进度分析
     */
    handleClick() {
      this.$refs.scheduleModel.show()
    },
    modalOk(data) {
      if (this.task.projectPlanReport) this.task.projectPlanReport = Object.assign(this.task.projectPlanReport, data)
      else this.task.projectPlanReport = data
      this.$refs.gantt.updateTask(this.task.id, this.task)
    },
  },
}
</script>

<style lang="scss" scoped>
.GanttChart {
  width: 100%;
}
.button-show {
  background: #3254ff;
  color: #fff;
}
.plan {
  background-color: #3254ff;
  width: 28px;
  height: 20px;
  border-radius: 4px 4px 4px 4px;
}
.normal {
  background-color: #00b42a;
  width: 28px;
  height: 20px;
  border-radius: 4px 4px 4px 4px;
}
.delay {
  background-color: #f53f3f;
  width: 28px;
  height: 20px;
  border-radius: 4px 4px 4px 4px;
}
</style>
