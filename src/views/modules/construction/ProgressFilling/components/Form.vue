<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-form-item style="display: none">
          <a-input v-decorator="['id']"></a-input>
        </a-form-item>
        <a-row>
          <a-col :span="12">
            <a-form-item label="任务名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['name']" placeholder="请输入姓名"></a-input>
            </a-form-item>
          </a-col>
          <!-- <a-col :span="12">
            <a-form-item label="任务类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['taskType']" placeholder="请输入任务类型"></a-input>
            </a-form-item>
          </a-col> -->
          <a-col :span="12">
            <a-form-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-decorator="['status']" disabled>
                <a-select-option :value="0"> 未开始 </a-select-option>
                <a-select-option :value="1"> 进行中 </a-select-option>
                <a-select-option :value="2"> 已完成 </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="实际开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                placeholder="请选择开始时间"
                style="width: 100%"
                :disabled-date="disabledStartDate"
                v-decorator="['actualStartDate', validatorRules.actualStartDate]"
                @change="(date) => onChange(date, 'str')"
                format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="实际完成时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker
                style="width: 100%"
                placeholder="请选择结束时间"
                :disabled-date="disabledEndDate"
                @change="(date) => onChange(date, 'end')"
                v-decorator="['actualEndDate']"
                format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="备注"
              :labelCol="{
                xs: { span: 24 },
                sm: { span: 3 },
              }"
              :wrapperCol="{
                xs: { span: 24 },
                sm: { span: 20 },
              }"
            >
              <a-textarea placeholder="请输入备注" :autosize="{ minRows: 3, maxRows: 6 }" v-decorator="['remark']" />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import moment from 'dayjs'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'PersonInfoForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      fileList1: '',
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        actualStartDate: {
          rules: [{ required: true, message: '请选择实际开始时间!' }],
        },
      },
      url: {
        add: '/project/projectPlanReport/add',
        edit: '/project/projectPlanReport/edit',
      },
      actualStartDate: '',
      actualEndDate: '',
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    handleDepartmentChange(value) {
      console.log('handleDepartmentChange', value)
      this.shipId = value
      getAction(this.url.queryUnitById, { id: value }).then((res) => {
        if (res.success) {
          this.form.setFieldsValue({ departmentType: res.result.unitType })
        }
      })
    },
    onChange(date, type) {
      if (type == 'str') {
        this.actualStartDate = date
      } else {
        this.actualEndDate = date
      }
      if (this.actualStartDate && this.actualEndDate) {
        this.form.setFieldsValue({ status: 2 })
      } else if (this.actualStartDate && !this.actualEndDate) this.form.setFieldsValue({ status: 1 })
      else if (!this.actualStartDate) this.form.setFieldsValue({ status:0 })
    },
    disabledStartDate(startValue) {
      const endValue = this.actualEndDate
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf()
    },
    disabledEndDate(endValue) {
      const startValue = this.actualStartDate
      if (!endValue || !startValue) {
        return false
      }
      return startValue.valueOf() >= endValue.valueOf()
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.task = record
      this.form.resetFields()
      this.model = Object.assign({}, record)
      if(!this.model.status) this.model.status=0
      // let { actualStartDate, actualEndDate, remark, id, status } = record.projectPlanReport || {}
      // Object.assign(this.model, {
      //   reportId: id,
      //   actualStartDate,
      //   actualEndDate,
      //   remark,
      //   status: status || '0',
      // })
      this.actualStartDate = record.actualStartDate
      this.actualEndDate = record.actualEndDate
      this.visible = true
      console.log('🚀 ~ edit ~ this.model :', this.model)
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'taskType',
            'name',
            'remark',
            'status',
            'baseCode',
            'actualStartDate',
            'actualEndDate',
            'remark',
            'id'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证

      this.form.validateFields((err, values) => {
        if (!err) {
          // that.confirmLoading = true
          let httpurl = ''
          let method = ''

          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          formData.actualStartDate = moment(formData.actualStartDate).format('YYYY-MM-DD')
          if (formData.actualEndDate) {
            formData.actualEndDate = moment(formData.actualEndDate).format('YYYY-MM-DD')
            if (formData.status == 2) {
              formData.actualDay = moment(formData.actualEndDate).diff(moment(formData.actualStartDate), 'days') + 1
            }
            if (moment(formData.actualEndDate).diff(moment(formData.actualStartDate), 'days') < 0) {
              that.$message.warning('实际结束时间不能晚于实际开始时间')
              return
            }
          } else {
            formData.actualDay = null
          }

          httpAction(httpurl, formData, method)
            .then((res) => {
              that.confirmLoading = false
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok', formData)
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'name', 'taskType'))
    },
  },
}
</script>