<template>
  <j-modal
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
    :footer="null"
    title="进度分析"
  >
    <div>施工进度： <a-progress :percent="percent" /></div>
    <table-layout :rightTitle="rightTitle" :table-props="tableProps">
      <template #risk="{ record }">
        <a-tag :color="risk(record) == '正常' ? '#87d068' : '#f50'">
          {{ risk(record) }}
        </a-tag>
      </template>
    </table-layout>
  </j-modal>
</template>
  
  <script>
import { postAction } from '@/api/manage'
import tableLayout from '@/components/tableLayout/TableLayout'
import moment from 'dayjs'
import { getAction } from '@/api/manage'

export default {
  name: 'constructionLogModal',
  components: { tableLayout },
  data() {
    return {
      rightTitle: '关键任务列表',
      // 表格组件的props
      tableProps: {
        dataSource: [],
        columns: [
          {
            title: 'WBS编号',
            align: 'center',
            dataIndex: 'code',
          },
          {
            title: '任务名称',
            align: 'center',
            dataIndex: 'name',
          },
          {
            title: '计划开始时间',
            align: 'center',
            dataIndex: 'startDate',
          },
          {
            title: '计划完成时间',
            align: 'center',
            dataIndex: 'endDate',
          },
          {
            title: '实际开始时间',
            align: 'center',
            dataIndex: 'actualStartDate',
            customRender: (t, r, i) => {
              if (r.projectPlanReport) return r.projectPlanReport.actualStartDate
            },
          },
          {
            title: '实际完成时间',
            align: 'center',
            dataIndex: 'actualEndDate',
            customRender: (t, r, i) => {
              if (r.projectPlanReport) return r.projectPlanReport.actualEndDate
            },
          },
          {
            title: '风险评估',
            align: 'center',
            dataIndex: 'risk',
            scopedSlots: { customRender: 'risk' },
          },
        ],
      },
      width: '70%',
      visible: false,
      disableSubmit: false,
      percent: 50,
    }
  },
  methods: {
    disabledStartDate(endTime) {
      if (!endTime) {
        return false
      }
      return (date) => {
        const end = new Date(endTime) // 将结束时间转换为日期对象
        return date.valueOf() >= end.valueOf() // 如果日期大于等于结束时间，则不可选
      }
    },
    disabledEndDate(startTime) {
      return (date) => {
        if (!startTime) {
          return false // 如果没有选择开始时间，则结束时间可选
        }
        const now = new Date() // 获取当前时间
        const start = new Date(startTime) // 将开始时间转换为日期对象
        return date.valueOf() <= start.valueOf() // 如果日期小于等于开始时间，则不可选
      }
    },
    changeTime(date, dateString) {
      console.log('date', date, dateString)
    },
    operation(type, record) {
      console.log('type, record', type, record)
      // 如果选中是自己 就直接保存
      if (record.editable) {
        this.$set(record, 'editable', false)
        // 计算耗时 useTime
        // 如果 开始结束时间都有 就计算两个时间差
        console.log('editable', record)
        if (record.startTime && record.endTime) {
          // 计算小时
          const start = new Date(record.startTime)
          const end = new Date(record.endTime)
          const hour = parseInt((end - start) / 360000)
          this.$set(record, 'useTime', hour)
        }
        return
      }
      if (type == 'edit') {
        console.log('this.tableData', this.tableData)
        this.tableData.forEach((item, i) => {
          if (item.id === record.id) {
            this.$set(item, 'editable', true)
          } else {
            this.$set(item, 'editable', false)
            // 计算耗时 useTime
            // 如果 开始结束时间都有 就计算两个时间差
            console.log('editable', item)
            if (item.startTime && item.endTime) {
              // 计算小时
              const start = new Date(item.startTime)
              const end = new Date(item.endTime)
              const hour = parseInt((end - start) / 360000)
              this.$set(item, 'useTime', hour)
            }
          }
        })
      }
    },
    editRow(key) {
      const newData = [...this.tableData]
      const target = newData.find((item) => key === item.key)
      this.editingKey = key
      if (target) {
        target.editable = true
        this.tableData = newData
        this.$set(this, 'tableData', newData)
      }
    },
    save(key) {
      const newData = [...this.tableData]
      const newCacheData = [...this.cacheData]
      const target = newData.find((item) => key === item.key)
      const targetCache = newCacheData.find((item) => key === item.key)
      if (target && targetCache) {
        delete target.editable
        this.tableData = newData
        Object.assign(targetCache, target)
        this.cacheData = newCacheData
      }
      this.editingKey = ''
    },
    cancel(key) {
      const newData = [...this.tableData]
      const target = newData.find((item) => key === item.key)
      this.editingKey = ''
      if (target) {
        Object.assign(
          target,
          this.cacheData.find((item) => key === item.key)
        )
        delete target.editable
        this.tableData = newData
      }
    },

    saveRow(record) {
      record.editable = false // 保存后将 editable 设置为 false
    },
    handleOk() {
      // this.$refs.realForm.submitForm()
      console.log('提交', this.tableData)
      // getAction
      postAction('/construction/constructionLog/addList', {
        pbsId: this.pbsId,
        constructionLogs: this.tableData,
      })
        .then((result) => {
          console.log('result', result)
          if (!result.success) {
            this.$message.error('保存失败')
            return
          }
          this.$message.success('保存成功')
          this.visible = false
        })
        .catch((err) => {})
    },
    handleCancel() {
      this.visible = false
    },
    show() {
      this.visible = true
      this.tableProps.dataSource = []
      getAction('/project/projectPlanReport/analysis').then((res) => {
        const { result, success } = res
        if (!success) {
          this.$message.error(result.message)
          return
        }
        if (result && result.length > 0) {
          this.tableProps.dataSource = result
        }
      })
      getAction('/project/projectPlanReport/schedule').then((res) => {
        const { result, success } = res
        if (!success) {
          this.$message.error(result.message)
          return
        }
        if (result) {
          this.percent = (result*100).toFixed(2)
        }
      })
    },
    risk(r) {
      const projectPlanReport = r.projectPlanReport
      if (projectPlanReport && projectPlanReport.actualEndDate) {
        if (
          moment(projectPlanReport.actualEndDate).isBefore(moment(r.endDate)) ||
          moment(projectPlanReport.actualEndDate).diff(moment(r.endDate)) == 0
        )
          return '正常'
        else return '已延期(' + moment(projectPlanReport.actualEndDate).diff(r.endDate, 'days') + ')'
      } else {
        if (
          moment(new Date()).isBefore(moment(r.endDate)) ||
          moment(new Date()).diff(moment(r.endDate)) == 0
        )
          return '正常'
        else return '已延期(' + moment(new Date()).diff(r.endDate, 'days') + ')'
      }
    },
  },
}
</script>
  
  <style lang="scss" scoped>
.ant-progress {
  display: inline-block;
  width: 1100px;
}
.table-layout {
  ::v-deep .ant-pagination {
    display: none !important;
  }
}
</style>
  