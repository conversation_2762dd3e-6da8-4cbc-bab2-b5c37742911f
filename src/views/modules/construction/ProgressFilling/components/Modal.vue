<template>
  <j-modal
    :title="title"
    :width="width"
    :maskClosable="false"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <data-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></data-form>
  </j-modal>
</template>
  
  <script>
import DataForm from './Form'
export default {
  name: 'ProjectFenceModal',
  components: {
    DataForm,
  },
  data() {
    return {
      title: '进度填报',
      width: 896,
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback(data) {
      this.$emit('ok', data)
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>