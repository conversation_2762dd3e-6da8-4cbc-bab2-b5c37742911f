<template>
  <div class="pageMain flex">
    <Map @clickMap="clickMap" class="flex-1" @initMapCompleted="initMapCompleted" :showRightBox="false" ref="Map"></Map>
    <div class="w-400 index-999 bg-fff py-10 px-10 h-100pre over-h flex flex-column">
      <!-- 日历 -->
      <div class="w-100pre h-320 rounded-2" style="border: 1px solid #e5e6eb">
        <Calendar :fullscreen="false" class="w-100pre h-100pre" @panelChange="changeCalendar" @select="selectCalendar">
          <template slot="dateFullCellRender" slot-scope="value">
            <div
              class="text-center flex flex-column align-center pointer date-Calendar"
              :class="{ active: isActive(value) }"
              @click="handleClick(value)"
            >
              <span>{{ getDate(value) }}</span>
              <div :class="{ 'red-dot': hasEvent(value) }"></div>
            </div>
          </template>
        </Calendar>
      </div>
      <div class="line my-15"></div>
      <!-- 玫瑰 -->
      <div class="w-100pre flex-1 flex flex-column">
        <div class="flex align-center mb-10">
          <div class="line1 mr-8"></div>
          <div class="home" style="color: #444; font-weight: bolder">观测统计分析（累计）</div>
        </div>
        <div class="flex-1">
          <div class="flex h-100pre">
            <div style="width: 50%" class="flex align-center justify-center">
              <div id="positionalAnalysis" style="height: 100%; width: 80%"></div>
            </div>
            <div style="height: 100%; width: 50%" class="pl-10 flex align-center">
              <div class="">
                <div v-for="item in legend" :key="item.index" class="flex mb-10">
                  <div class="circel">{{ item.index }}</div>
                  <div class="font-14">{{ item.value }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="line mb-15"></div>
      <!-- 折线 -->
      <div class="w-100pre border h-220 flex flex-column">
        <div class="flex align-center mb-10">
          <div class="line1 mr-8"></div>
          <div class="home" style="color: #444; font-weight: bolder">影响施工次数分析</div>
        </div>
        <div id="impactAnalysis" class="w-100pre flex-1"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import Map from '@/components/Map'
import { getAction, postAction } from '@/api/manage'
import { Calendar } from 'ant-design-vue'
import moment from 'moment'
export default {
  components: { Map, Calendar },
  data() {
    return {
      mapDefaultZoom: 12,
      map: null,
      mousemoveLnglat: null,
      legend: [
        {
          index: '1',
          value: '中心表示升压站',
        },
        {
          index: '2',
          value: '图块表示8个方位',
        },
        {
          index: '3',
          value: '半径代表每个方位观测到海豚的次数',
        },
      ],
      url: {
        // list: '/construction/monitorDolphinLog/list',
        list: '/construction/monitorDolphinLog/queryByMonth',
        PolarPlot: '/construction/watchDolphinLogList/getPolarPlotData',
        report: '/construction/watchDolphinLogList/getReport',
      },
      pageParams: {
        pageNo: 1,
        pageSize: 200,
      },
      InfoWindow: null,
      eventDates: [], // 这里存储带有事件的日期
      activeDate: '',
    }
  },
  mounted() {},
  methods: {
    handleClick(value) {
      this.activeDate = value.format('YYYY-MM-DD')
    },
    isActive(value) {
      return this.activeDate === value.format('YYYY-MM-DD')
    },
    getDate(date) {
      return moment(date).format('DD')
    },
    changeEcharts(fontSize) {
      let ratio = parseInt(document.documentElement.clientWidth) / 1920
      console.log('ratio', ratio, fontSize * ratio)
      return fontSize * ratio
    },
    async initMapCompleted(map) {
      this.activeDate = moment(new Date()).format('YYYY-MM-DD')
      await this.getLog(moment(new Date()).format('YYYY-MM'))
      this.selectCalendar(this.activeDate)
      let mapCenter = map.getCenter()
      console.log('mapCenter', mapCenter)
      this.getPolarPlotData(mapCenter) //玫瑰图
      this.getReport() //折线图
    },
    // 获取玫瑰图数据
    async getPolarPlotData(mapCenter) {
      let params = {
        lng: mapCenter.lng,
        lat: mapCenter.lat,
      }
      let res = await postAction(this.url.PolarPlot, params)
      console.log('获取玫瑰图数据', res)
      if (!res.success) {
        this.$message.error('获取玫瑰图数据失败')
        return
      }
      let dataObj = res.result
      console.log('dataObj', dataObj)
      this.initPositional('positionalAnalysis', (option, charts) => {
        // 找出最大值
        const maxValue = Math.max(...Object.values(dataObj))
        option.series[0].data.forEach((item, index) => {
          item.value = dataObj[index] || 0
          item.name = dataObj[index]
          option.series[1].data[index].value = maxValue
        })
        console.log('option', option)
        charts.setOption(option)
      })
    },

    async getReport() {
      let { success, result } = await getAction(this.url.report)
      console.log('折线图', result)
      if (!success) {
        this.$message.error('最近一年数据 获取失败')
        return
      }
      // result = result.reverse()
      console.log('result', result)
      this.initImpactAnalysis('impactAnalysis', (option, charts) => {
        let totalCount = []
        let affectedCount = []
        let month = []
        console.log('result', result)
        result.forEach((item) => {
          totalCount.push(item.totalCount)
          affectedCount.push(item.affectedCount)
          month.push(item.month.split('-')[1] + '月')
        })
        option.series[0].data = totalCount
        option.series[1].data = affectedCount
        option.xAxis.data = month
        console.log('option', option)
        charts.setOption(option)
      })
    },

    hasEvent(value) {
      const dateString = value.format('YYYY-MM-DD')
      return this.eventDates.includes(dateString)
    },
    async getLog(monthString) {
      let params = {
        ...this.pageParams,
        monthString: monthString,
      }
      const { success, result } = await getAction(this.url.list, params)
      console.log('日志-----', result)
      if (!success) {
        this.$message.error('获取数据失败')
        return
      }
      // 处理日志
      if (result && Array.isArray(result)) {
        this.eventDates = result.map((item) => {
          if (Array.isArray(item.details) && item.details.length > 0) {
            return item.watchDate
          }
        }) // 假设每个item中有一个date字段
      }
      this.dolphinList = result
    },

    dolphinClick(mark, e) {
      console.log('点击了海豚', mark)
      const InfoWindow = new T.InfoWindow({
        // offset: new T.Point(50, -5),
        // closeOnClick: true,
      })
      let src = 'static/dolphinBg.png'
      if (mark.pictures) {
        src = JSON.parse(mark.pictures)[0].url
      }
      let sContent = `
      <div class="h-182 flex w-650">
      <div class="mr-10">
        <img src="${src}" class="w-140 h-182" alt="" />
      </div>
      <div class="right flex flex-column justify-around py-10" style="color: #86909C">
        <div class="flex align-center ">
          <div class="line1 mr-8" style="  width: 3px;
  height: 10px;
  background: #3254ff;"></div>
          <div class="text-000 bolder">观测详情</div>
        </div>
        <div class="item">
          <span class="mr-5">出现时间：</span>
          <span>${moment(mark.startDate * 1000).format('YYYY-MM-DD HH:mm')}</span>
        </div>
        <div class="item flex">
          <div class="mr-5">天气情况：</div>
          <div class='text-ellipsis w-100' title="${mark.weather || ''}">${mark.weather || ''}</div>
        </div>
        <div class="item">
          <span class="mr-5">观测人员：</span>
          <span>${mark.username || ''}</span>
        </div>
        <div class="item">
          <span class="mr-5">是否影响施工：</span>
          <span>${mark.isConstructionAffected == '1' ? '是' : '否'}</span>
        </div>
        <div class="item flex">
          <div class="mr-5">采取措施：</div>
          <div class='text-ellipsis w-100' title=${mark.statusAction}>${mark.statusAction || ''}</div>
        </div>
      </div>
    </div>
      `
      InfoWindow.setContent(sContent)
      this.$nextTick(() => {
        e.openInfoWindow(InfoWindow)
      })
    },
    // 玫瑰图
    initPositional(id, callback) {
      let chartDom = document.getElementById(id)
      let myChart = echarts.init(chartDom)
      let option
      option = {
        legend: {
          right: 'center',
          show: false,
        },
        series: [
          {
            name: 'Nightingale Chart',
            type: 'pie',
            zlevel: 2,
            radius: ['30%', '100%'],
            center: ['50%', '50%'],
            roseType: 'area',
            itemStyle: {
              borderRadius: 1,
            },
            label: {
              position: 'inside',
              color: '#fff',
            },
            data: [
              {
                value: 0,
                name: '0',
                itemStyle: {
                  color: '#FF9D4E',
                  borderWidth: 1,
                  borderColor: '#fff',
                },
              },
              {
                value: 0,
                name: '0',
                itemStyle: {
                  color: '#1E74F6',
                  borderWidth: 1,
                  borderColor: '#fff',
                },
              },
              {
                value: 0,
                name: '0',
                itemStyle: {
                  color: '#5999FA',
                  borderWidth: 1,
                  borderColor: '#fff',
                },
              },
              {
                value: 0,
                name: '0',
                itemStyle: {
                  color: '#5EC284',
                  borderWidth: 1,
                  borderColor: '#fff',
                },
              },
              {
                value: 0,
                name: '0',
                itemStyle: {
                  color: '#E96C5B',
                  borderWidth: 1,
                  borderColor: '#fff',
                },
              },
              {
                value: 0,
                name: '0',
                itemStyle: {
                  color: '#73CAEC',
                  borderWidth: 1,
                  borderColor: '#fff',
                },
              },
              {
                value: 0,
                name: '0',
                itemStyle: {
                  color: '#F6C022',
                  borderWidth: 1,
                  borderColor: '#fff',
                },
              },
              {
                value: 0,
                name: '0',
                itemStyle: {
                  color: '#FD9CC4',
                  borderWidth: 1,
                  borderColor: '#fff',
                },
              },
            ],
          },
          {
            name: 'Nightingale Chart',
            type: 'pie',
            radius: ['100%', '30%'],
            center: ['50%', '50%'],
            roseType: 'area',
            zlevel: 1,
            itemStyle: {
              borderRadius: 0,
            },
            labelLine: {
              show: false,
            },

            data: [
              {
                value: 1,
                name: '',
                itemStyle: {
                  color: '#FFE2CB',
                },
              },
              {
                value: 1,
                name: '',
                itemStyle: {
                  color: '#BCD5FC',
                },
              },
              {
                value: 1,
                name: '',
                itemStyle: {
                  color: '#CDE0FE',
                },
              },
              {
                value: 1,
                name: '',
                itemStyle: {
                  color: '#CBF1DF',
                },
              },
              {
                value: 1,
                name: '',
                itemStyle: {
                  color: '#F8D3CE',
                },
              },
              {
                value: 1,
                name: '',
                itemStyle: {
                  color: '#D5F0F9',
                },
              },
              {
                value: 1,
                name: '',
                itemStyle: {
                  color: '#FCECBE',
                },
              },
              {
                value: 1,
                name: '',
                itemStyle: {
                  color: '#9A3CD0',
                },
              },
            ],
          },
        ],

        graphic: {
          zlevel: 3,
          elements: [
            {
              type: 'image',
              style: {
                image: require('@/assets/map/img/legend/BoosterStation.png'), // 替换为你的图片路径
                width: this.changeEcharts(52),
                height: this.changeEcharts(52),
              },
              left: 'center',
              top: 'center',
            },
          ],
        },
      }

      option && myChart.setOption(option)
      if (callback) {
        callback(option, myChart)
      }
      myChart.resize()
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
    // 玫瑰图
    initImpactAnalysis(id, callback) {
      let chartDom = document.getElementById(id)
      let myChart = echarts.init(chartDom)
      let option
      option = {
        title: {
          text: '影响施工次数分析',
          show: false,
        },
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          show: true,
          data: ['海豚出现次数', '影响施工次数'],
          top: '2%',
          right: '5%',
        },
        grid: {
          top: '15%',
          left: '5%',
          right: '5%',
          bottom: '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          // boundaryGap: ['5px', '10%'], // 设置X轴两端的留白
          boundaryGap: true,
          axisTick: {
            alignWithLabel: true,
            length: 3, // 设置刻度线长度
            inside: true, // 将刻度线设置为在轴线外部
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#D3D3D3',
            },
          },
          axisLabel: {
            color: '#000',
          },
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          offset: 5,
        },
        yAxis: {
          type: 'value',
          name: '次',
          nameLocation: 'end',
          nameTextStyle: {
            padding: [0, 20, 0, 0],
          },
        },
        series: [
          {
            name: '海豚出现次数',
            type: 'line',
            stack: '',
            data: [],
            itemStyle: {
              color: '#3254FF',
            },
          },
          {
            name: '影响施工次数',
            type: 'line',
            stack: 'Total',
            data: [],
            itemStyle: {
              color: '#FF9733',
            },
          },
        ],
      }
      option && myChart.setOption(option)
      if (callback) {
        callback(option, myChart)
      }
      myChart.resize()
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
    changeCalendar(value, mode) {
      console.log('changeCalendar', value, mode)
      this.getLog(moment(value).format('YYYY-MM'))
    },
    selectCalendar(date) {
      // 判断是不是对象
      if (typeof date === 'object') {
        date = moment(date).format('YYYY-MM-DD')
      }
      console.log('selectCalendar', date, this.dolphinList)
      let dolphin = []
      let dateDetails = this.dolphinList.filter((item) => item.watchDate == date)
      console.log('dateDetails', dateDetails)
      if (dateDetails && Array.isArray(dateDetails)) {
        dateDetails.forEach((item) => {
          console.log('item.details', item.details)
          if (Array.isArray(item.details) && item.details.length > 0) {
            // details = details.concat(item.details)
            item.details.forEach((initem) => {
              if (initem.position) {
                let position = initem.position.split(',') || []
                dolphin.push({
                  ...initem,
                  lng: position[0] || '',
                  lat: position[1] || '',
                  type: 'dolphin',
                  username: item.username,
                })
              } 
              // else {
              //   this.$message.warning(
              //     `开始时间为${moment
              //       .unix(initem.startDate)
              //       .format('YYYY-MM-DD HH:mm')}的观豚日志，没有填写白海豚位置信息`
              //   )
              // }
            })
          }
        })
      }
      console.log('dolphin', dolphin)
      this.$nextTick(() => {
        console.log('wind')
        this.$refs.Map.clearPoits('dolphin')
        let icon = new T.Icon({
          iconUrl: require(`@/assets/map/img/legend/dolphin.png`),
          iconSize: new T.Point(32, 32), // 图标大小
        })
        this.$refs.Map.setPoits(dolphin, this.dolphinClick, icon)
      })
    },
    clickMap(mark, markerType) {
      console.log('🚀 ~ clickMap ~ mark:', mark)
    },
  },
}
</script>

<style lang="scss" scoped>
.red-dot {
  width: 6px;
  height: 6px;
  background: red;
  border-radius: 50%;
}

::v-deep .tdt-infowindow {
  width: 340px;
  overflow: hidden;
}
.line {
  width: 100%;
  height: 1px;
  border: 1px solid #e5e6eb;
}
.pageMain {
  --index: 500;
  --left: 54px;
  --top: 50px;
  --right: 54px;
  --bottom: 16px;
  width: 100%;
  height: 100%;
  #map {
    height: calc(88vh - 5px);
  }
  .map-tool {
    display: none;
    position: absolute;
    bottom: var(--bottom);
    right: var(--right);
    z-index: var(--index);
    .tool-item {
      width: 36px;
      height: 36px;
      border-radius: 4px;
      margin-bottom: 10px;
      cursor: pointer;
    }
  }
}

::v-deep .ant-radio-group {
  display: none;
}
::v-deep .ant-fullcalendar-header {
  background-color: #f2f3f5;
}
.circel {
  background: linear-gradient(180deg, #5f96ff 0%, #3254ff 100%);
  border-radius: 50%;
  margin-right: 10px;
  min-width: 18px !important;
  height: 18px;
  text-align: center;
  line-height: 18px;
  color: #fff;
  font-size: 14px;
  margin-top: 4px;
}
.date-Calendar:hover {
  outline: 1px solid blue;
  border-radius: 8px; /* 保持圆角 */
}

.active {
  background-color: #0e5eff6b;
  border-radius: 8px; /* 保持圆角 */
  color: #fff;
}

.line1 {
  width: 3px;
  height: 10px;
  background: #3254ff;
}
</style>
