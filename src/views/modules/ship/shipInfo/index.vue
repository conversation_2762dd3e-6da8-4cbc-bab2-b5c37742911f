<template>
  <div class="list-page project-wbs-list">
    <table-layout :table-props="tableProps" :search-props="searchProps" @search-submit="handleSearchSubmit">
      <template #status="{ record }">
        <span :class="'belongTag belongTag-' + record.status">{{
              record.status == 0 ? '在场' : record.status == 1 ? '离场' : ''
            }}</span>
      </template>
      <template #qrcode="{ record }">
        <div @click="showVueQrcode(record)">
          <VueQrcode
            :value="{ name: record.name, mmsi: record.mmsi }"
            :options="{ width: 38, height: 38, color: '#000000', backgroundColor: '#ffffff' }"
          ></VueQrcode>
        </div>
      </template>
      <!-- <template #name="{ record }">
        <a @click="handleDetails(record)">{{ record.name }}</a>
      </template> -->
    </table-layout>
    <!-- 查看二维码 -->
    <a-modal
      :title="vueQrcodeData.name"
      okText="下载"
      @ok="downloadVueQrcode"
      :visible="vueQrcodeVisible"
      @cancel="vueQrcodeVisible = false"
    >
      <div style="text-align: center">
        <VueQrcode
          :value="{ name: vueQrcodeData.name, mmsi: vueQrcodeData.mmsi }"
          ref="qrcode"
          :options="{ width: 300, height: 300, color: '#000000', backgroundColor: '#ffffff' }"
        ></VueQrcode>
      </div>
    </a-modal>
    <!-- 编辑船舶 -->
    <ship-info-modal ref="modalForm" :shipTypeList="shipTypeList" @ok="modalFormOk"></ship-info-modal>
    <!-- 关联船舶 -->
    <associated-ship-modal
      :shipTypeList="shipTypeList"
      ref="AssociatedShipModal"
      @ok="modalFormOk"
    ></associated-ship-modal>
    <!-- <modal :title="title" :visible="false" ref="DataShipInfoModal" @ok="modalFormOk">
    </modal> -->
  </div>
</template>

<script>
import { getAction, postAction, deleteAction } from '@/api/manage'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import VueQrcode from '@chenfengyuan/vue-qrcode'
import { getDictItems } from '@/components/dict/JDictSelectUtil'
import '@/assets/less/TableExpand.less'
import ShipInfoModal from './components/ShipInfoModal'
// import DataShipInfoModal from '@views/data/DataShipInfo/components/Modal'
import AssociatedShipModal from './components/AssociatedShipModal'

export default {
  name: 'ProjectWbsList',
  mixins: [JeecgListMixin],
  components: {
    VueQrcode,
    ShipInfoModal,
    AssociatedShipModal,
    // DataShipInfoModal,
  },
  data() {
    return {
      title: '',
      shipTypeList: [],
      vueQrcodeVisible: false,
      vueQrcodeData: {},
      searchData: {},
      description: '船舶信息',
      // 搜索组件的props
      searchProps: {
        formModel: {
          name: null,
          typeValue: null,
        },
        formItems: [
          { key: 'name', label: '船舶名称', type: 'text' },
          { key: 'typeValue', label: '船舶类型', classType: 'sel_search', dictCode: 'ship_typ_code' },
        ],
      },
      tableProps: {
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        // 表头
        dataSource: [],
        isorter: {
          column: 'createTime',
          order: 'desc',
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '船舶名称',
            align: 'center',
            dataIndex: 'name',
            // scopedSlots: { customRender: 'name' },
          },
          {
            title: '船舶类型',
            align: 'center',
            dataIndex: 'typeValue',
            customRender: (text, record, index) => {
              return this.shipTypeList.find((x) => x.value === text).title
            },
          },
          {
            title: '船东',
            align: 'center',
            dataIndex: 'shipOwner',
          },
          {
            title: '在场/离场',
            align: 'center',
            dataIndex: 'status',
            scopedSlots: { customRender: 'status' },
          },
          // {
          //   title: '打卡二维码',
          //   align: 'center',
          //   dataIndex: 'qrcode',
          //   scopedSlots: { customRender: 'qrcode' },
          // },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 175,
            scopedSlots: { customRender: 'action' },
          },
        ],
        headerButtons: [
          {
            text: '关联船舶',
            icon: 'plus-circle',
            handler: this.AssociatedShip,
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '定位',
            handler: '',
          },
          {
            text: '编辑',
            handler: this.handleEdit,
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
            disabled: (record) => {
              console.log(record.name,record.status)
              return record.status==='0'
            },
          },
        ],
      },
      url: {
        list: '/departureWarning/shipInfo/list',
        delete: '/departureWarning/shipInfo/delete',
        deleteBatch: '/departureWarning/shipInfo/deleteBatch',
        exportXlsUrl: '/departureWarning/shipInfo/exportXls',
        importExcelUrl: 'departureWarning/shipInfo/importExcel',
        dataList: '/data/dataShipInfo/list',
      },
      superFieldList: [],
    }
  },
  created() {
    // 获取船舶类型字典
    getDictItems('ship_typ_code').then((resp) => {
      this.shipTypeList = resp
    })
  },
  computed: {},
  methods: {
    handleDetails(record) {
      let params = {}
      params.id = record.shipDataId
      getAction(this.url.dataList, params).then((res) => {
        if (res.success) {
          // this.dataSource = res.result.records || res.result;
          if (res.result.total) {
            console.log('res.result.records[0].shipName', res.result.records[0].shipName)
            // this.dataShip = res.result.records[0];
            this.title = res.result.records[0].shipName
            this.$refs.DataShipInfoModal.view(res.result.records[0])
          } else {
            return
          }
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },

    handleSearchSubmit(searchData) {
      this.searchData = searchData //备份搜索条件
      this.loadData(searchData.params)
    },
    /**
     * @description 查看二维码
     */
    showVueQrcode(record) {
      this.vueQrcodeVisible = true
      this.vueQrcodeData = record
    },
    /**
     * @description 下载二维码
     */
    downloadVueQrcode() {
      const canvas = this.$refs.qrcode.$el
      const url = canvas.toDataURL('image/png')
      const link = document.createElement('a')
      link.href = url
      link.download = this.vueQrcodeData.name + '.png'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    /**
     * @description 关联船舶
     */
    AssociatedShip() {
      this.$refs.AssociatedShipModal.add()
    },
    /**
     * @description 弹框关闭
     */
    modalFormOk() {
      this.loadData()
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';

.belongTag {
      width: 72px;
      height: 22px;
      border-radius: 11px;
      display: inline-block;
    }
    .belongTag-0 {
      background: #5c7cff;
      color: #fdfdfd;
      border: 1px solid #5c7cff;
    }
    .belongTag-1 {
      background: #e5e6eb;
      color: #8a8989;
      border: 1px solid #e5e6eb;
    }
</style>
