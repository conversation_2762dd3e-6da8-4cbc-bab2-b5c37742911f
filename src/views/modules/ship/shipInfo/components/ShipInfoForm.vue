<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="false">
      <div>
        <a-form :form="form" slot="detail">
          <a-form-item label="id" :labelCol="labelCol" :wrapperCol="wrapperCol" style="display: none">
            <a-input v-decorator="['id']" autocomplete="off"></a-input>
          </a-form-item>
          <a-row>
            <a-col :span="12">
              <a-form-item label="船舶名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-decorator="['name']" placeholder="请输入船舶名称" disabled
                autocomplete="off"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="船舶类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-select placeholder="请输入船舶类型" v-decorator="['typeValue']" disabled>
                  <a-select-option v-for="item in shipTypeList" :value="item.value" :key="item.value">
                    {{ item.title }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="开启出航预警" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-select
                  :disabled="disabled"
                  v-decorator="['isAlarm']"
                  @change="
                    (val) => {
                      showTable = val == 1
                    }
                  "
                >
                  <a-select-option :value="1"> 是 </a-select-option>
                  <a-select-option :value="0"> 否 </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
              <a-button @click="submitForm">提 交</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </j-form-container>
    <conditionTable
      :data="model.information"
      :disabled="formDisabled"
      ref="conditionTable"
      :title="'适宜出航天气'"
      :visible="showTable"
    ></conditionTable>
  </a-spin>
</template>

<script>
import { httpAction, getAction, putAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import { getDictItems } from '@/components/dict/JDictSelectUtil'
import conditionTable from './conditionTable.vue'
export default {
  components: { conditionTable },
  name: 'ShipInfoForm',
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {},
      url: {
        add: '/departureWarning/shipInfo/add',
        edit: '/departureWarning/shipInfo/edit',
        queryById: '/departureWarning/shipInfo/queryById',
      },
      showTable: false,
      shipTypeList: [],
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
    getDictItems('ship_typ_code').then((resp) => {
      this.shipTypeList = resp
    })
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.showTable = false
      console.log('🚀 ~ edit ~  this.showTable:', this.showTable)
      this.form.resetFields()
      this.showTable = record.isAlarm == 1
      console.log('🚀 ~ edit ~  this.showTable:', this.showTable)
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'id', 'name', 'typeValue', 'mmsi', 'status', 'isAlarm'))
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      this.form.validateFields((err, values) => {
        console.log(values)
        console.log(this.information)
        // console.log(this.model)
        let params = {}
        // Object.assign(params, this.model)
        Object.assign(params, values)
        params.information = this.$refs.conditionTable.getData()
        console.log(params)
        putAction('/departureWarning/shipInfo/edit', params).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.$emit('ok')
          } else {
            this.$message.warning(res.message)
          }
        })
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'name', 'typeValue', 'mmsi', 'status', 'isAlarm'))
    },
  },
}
</script>