<template>
  <j-modal
    title="施工船舶-新增"
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <div class="content">
      <div class="left">
        <div
          v-for="item in shipTypeList"
          :class="{ active: shipType == item.value }"
          :key="item.value"
          @click="clickType(item)"
        >
          {{ item.title }}
        </div>
      </div>
      <div class="right">
        <a-form layout="inline" :form="queryParam">
          <a-form-item label="船舶名称">
            <a-input placeholder="船舶名称" v-model="queryParam.shipName" />
          </a-form-item>
          <a-form-item label="船东">
            <a-input placeholder="船东" v-model="queryParam.shipOwner" />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="getList()"> 查询 </a-button>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="reset()"> 重置 </a-button>
          </a-form-item>
        </a-form>
        <a-table
          :columns="columns"
          rowKey="id"
          :rowSelection="{ type: 'radio', selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :data-source="dataSource"
        >
        </a-table>
      </div>
    </div>
  </j-modal>
</template>
<script>
import { getAction, postAction, deleteAction } from '@/api/manage'
export default {
  name: 'ShipInfoModal',
  components: {},
  data() {
    return {
      title: '',
      width: 960,
      visible: false,
      disableSubmit: false,
      dataSource: [],
      selectedRowKeys: [],
      pageNo: 1,
      pageSize: 10,
      shipType: '',
      queryParam: {},
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '船舶名称',
          dataIndex: 'shipName',
          key: 'shipName',
        },
        {
          title: '船东',
          dataIndex: 'shipOwner',
          key: 'shipOwner',
        },
        {
          title: '船东联系人',
          dataIndex: 'linkPhone',
          key: 'linkPhone',
        },
      ],
      url: {
        list: '/data/dataShipInfo/list',
        add: '/departureWarning/shipInfo/add',
      },
      form: this.$form.createForm(this),
    }
  },
  props: ['shipTypeList'],
  methods: {
    reset() {
      this.queryParam = {}
      this.form.resetFields()
      this.getList()
    },
    add() {
      this.visible = true
      this.selectedRowKeys = []
      this.getList()
    },
    /**
     * @description 获取关联船舶
     */
    getList() {
      const params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        shipType: this.shipType,
        isRelevance: 0,
        shipState: '1',
      }
      Object.assign(params, this.queryParam)
      getAction(this.url.list, params).then((res) => {
        this.dataSource = res.result.records
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const data = this.dataSource.find((x) => x.id === this.selectedRowKeys[0])
      const params = {
        shipDataId: data.id,
        shipOwner: data.shipOwner,
        name: data.shipName,
        typeValue: data.shipType,
        mmsi: data.mmsi,
        status: data.shiptSate,
        sysOrgCode: data.sysOrgCode,
      }
      postAction(this.url.add, params).then((res) => {
        this.$message.info(res.message)
        this.visible = false
        this.$emit('ok')
      })
    },
    /**
     * @description 点击类型
     * @param {*} item 船舶类型
     */
    clickType(item) {
      this.shipType = item.value
      this.getList()
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    /**
     * @description 选中
     * @param {*} selectedRowKeys 选中数据
     */
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>
<style lang="less" scoped>
.content {
  height: 500px;
  display: flex;
  .left {
    width: 200px;
    height: 100%;
    .active {
      color: #1890ff;
    }
    > div {
      height: 30px;
      line-height: 30px;
      padding: 0 10px;
      cursor: pointer;
      border-radius: 4px;
      &:hover {
        background-color: #e6f7ff;
        color: #1890ff;
      }
    }
  }
  .right {
    flex: 1;
  }
}
</style>