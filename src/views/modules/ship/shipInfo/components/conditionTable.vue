<template>
  <div v-show="visible">
    <div class="title">{{title}}</div>
    <a-table :columns="columns" :pagination="false" :data-source="dataSource">
      <span slot="state" slot-scope="text, record">
        <a-switch
          checked-children="开"
          un-checked-children="关"
          :disabled="disabled"
          :checked="text == '1'"
          @change="
            (checked) => {
              record.state = checked ? '1' : '0'
            }
          "
        />
      </span>
      <span slot="data1" slot-scope="text, record">
        <a-select style="width: 30%; margin: 0 10%" v-model="record.data[0].symbol" :disabled="disabled" :allowClear="true">
          <a-select-option value="gt"> ＞ </a-select-option>
          <a-select-option value="gte"> ≥ </a-select-option>
          <a-select-option value="eq"> ＝ </a-select-option>
          <a-select-option value="lt"> ＜ </a-select-option>
          <a-select-option value="lte"> ≤ </a-select-option>
        </a-select>
        <a-input-number style="width: 30%; margin: 0 10%" v-model="record.data[0].num" :disabled="disabled" />
      </span>
      <span slot="data2" slot-scope="text, record">
        <a-select style="width: 30%; margin: 0 10%" v-model="record.data[1].symbol" :disabled="disabled" :allowClear="true">
          <a-select-option value="gt"> ＞ </a-select-option>
          <a-select-option value="gte"> ≥ </a-select-option>
          <a-select-option value="eq"> ＝ </a-select-option>
          <a-select-option value="lt"> ＜ </a-select-option>
          <a-select-option value="lte"> ≤ </a-select-option>
        </a-select>
        <a-input-number style="width: 30%; margin: 0 10%" v-model="record.data[1].num" :disabled="disabled" />
      </span>
    </a-table>
  </div>
</template>
  
  <script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '适宜天气',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    data(newVal, oldVal) {
      if (this.data) {
        this.dataSource = JSON.parse(this.data)
      } else {
        this.dataSource = [
          {
            state: '0',
            name: '风速(m/s)',
            code: 'wind',
            data: [
              {
                symbol: '',
                num: '',
              },
              {
                symbol: '',
                num: '',
              },
            ],
          },
          {
            state: '0',
            name: '温度(℃)',
            code: 'tem',
            data: [
              {
                symbol: '',
                num: '',
              },
              {
                symbol: '',
                num: '',
              },
            ],
          },
          {
            state: '0',
            name: '浪高(m)',
            code: 'wave',
            data: [
              {
                symbol: '',
                num: '',
              },
              {
                symbol: '',
                num: '',
              },
            ],
          },
          {
            state: '0',
            name: '能见度(km)',
            code: 'vis',
            data: [
              {
                symbol: '',
                num: '',
              },
              {
                symbol: '',
                num: '',
              },
            ],
          },
          {
            state: '0',
            name: '降雨(mm)',
            code: 'water',
            data: [
              {
                symbol: '',
                num: '',
              },
              {
                symbol: '',
                num: '',
              },
            ],
          },
        ]
      }
    },
  },
  data() {
    return {
      dataSource: [
        {
          state: '0',
          name: '风速(m/s)',
          code: 'wind',
          data: [
            {
              symbol: '',
              num: '',
            },
            {
              symbol: '',
              num: '',
            },
          ],
        },
        {
          state: '0',
          name: '温度(℃)',
          code: 'tem',
          data: [
            {
              symbol: '',
              num: '',
            },
            {
              symbol: '',
              num: '',
            },
          ],
        },
        {
          state: '0',
          name: '浪高(m)',
          code: 'wave',
          data: [
            {
              symbol: '',
              num: '',
            },
            {
              symbol: '',
              num: '',
            },
          ],
        },
        {
          state: '0',
          name: '能见度(km)',
          code: 'vis',
          data: [
            {
              symbol: '',
              num: '',
            },
            {
              symbol: '',
              num: '',
            },
          ],
        },
        {
          state: '0',
          name: '降雨(mm)',
          code: 'water',
          data: [
            {
              symbol: '',
              num: '',
            },
            {
              symbol: '',
              num: '',
            },
          ],
        },
      ],
      columns: [
        {
          title: '是否开启',
          dataIndex: 'state',
          key: 'state',
          scopedSlots: { customRender: 'state' },
        },
        {
          title: '指标',
          dataIndex: 'name',
          key: 'name',
          scopedSlots: { customRender: 'name' },
        },
        {
          title: '条件一',
          dataIndex: 'data1',
          align: 'center',
          key: 'data1',
          scopedSlots: { customRender: 'data1' },
        },
        {
          title: '条件二',
          dataIndex: 'data2',
          align: 'center',
          key: 'data2',
          scopedSlots: { customRender: 'data2' },
        },
      ],
    }
  },
  created() {},
  beforeUpdate() {
    if (this.data) {
      this.dataSource = JSON.parse(this.data)
    } else {
      this.dataSource = [
        {
          state: '0',
          name: '风速(m/s)',
          code: 'wind',
          data: [
            {
              symbol: '',
              num: '',
            },
            {
              symbol: '',
              num: '',
            },
          ],
        },
        {
          state: '0',
          name: '温度(℃)',
          code: 'tem',
          data: [
            {
              symbol: '',
              num: '',
            },
            {
              symbol: '',
              num: '',
            },
          ],
        },
        {
          state: '0',
          name: '浪高(m)',
          code: 'wave',
          data: [
            {
              symbol: '',
              num: '',
            },
            {
              symbol: '',
              num: '',
            },
          ],
        },
        {
          state: '0',
          name: '能见度(km)',
          code: 'vis',
          data: [
            {
              symbol: '',
              num: '',
            },
            {
              symbol: '',
              num: '',
            },
          ],
        },
        {
          state: '0',
          name: '降雨(mm)',
          code: 'water',
          data: [
            {
              symbol: '',
              num: '',
            },
            {
              symbol: '',
              num: '',
            },
          ],
        },
      ]
    }
  },
  mounted() {
    console.log('记载--------------')
  },
  methods: {
    getData() {
      return JSON.stringify(this.dataSource)
    },
  },
}
</script>
  
<style scoped lang="less">
.title {
  font-size: 16px;
  color: #212121;
  padding-bottom: 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid #e5e6eb;
}
</style>
  