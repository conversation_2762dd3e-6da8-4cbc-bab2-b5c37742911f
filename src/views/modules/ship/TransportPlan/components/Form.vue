<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="运单号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['transportCode', validatorRules.transportCode]"
                placeholder="请输入单号"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="运输货物" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['transportGoods', validatorRules.transportGoods]"
                placeholder="请输入运输货物"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="船舶名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['shipName', validatorRules.shipName]"
                placeholder="请输入船舶名称"
              ></a-input>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="MMSI" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['mmsi', validatorRules.mmsi]"
                placeholder="请输入MMSI"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="出发港口" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['departPort', validatorRules.departPort]"
                placeholder="请输入出发港口"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="预计出发时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择预计出发时间"
                v-decorator="['startDate']"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="目的地港口" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['destinationPort', validatorRules.destinationPort]"
                placeholder="请输入目的地港口"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="预计到达时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择预计到达时间"
                v-decorator="['arriveDate']"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="承运人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['carrier', validatorRules.carrier]"
                placeholder="请输入承运人"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['phone', validatorRules.phone]"
                placeholder="请输入电话"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="备注"
              :labelCol="{
                xs: { span: 24 },
                sm: { span: 3 },
              }"
              :wrapperCol="{
                xs: { span: 24 },
                sm: { span: 20 },
              }"
            >
              <a-textarea
                autocomplete="off"
                v-decorator="['remark', validatorRules.remark]"
                rows="4"
                placeholder="请输入备注"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="附件" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload v-decorator="['file']" :trigger-change="true" number="3"></j-upload>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'TransportPlanForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        transportCode: {
          rules: [
            { required: true, message: '请输入运单号!' },
            { max: 50, message: '运单号不能超过50个字符' },
          ],
        },
        mmsi: {
          rules: [
            { required: true, message: '请输入MMSI!' },
            { max: 50, message: 'MMSI不能超过50个字符' },
          ],
        },
        phone: {
          rules: [
            // { required: true, message: '请输入手机号码!' },
            { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码!' },
          ],
        },
        carrier: {
          rules: [{ max: 10, message: '承运人不能超过10个字' }],
        },
        remark: {
          rules: [{ max: 500, message: '备注不能超过500个字' }],
        },
        destinationPort: {
          rules: [{ max: 10, message: '目的地港口不能超过10个字' }],
        },
        departPort: {
          rules: [{ max: 50, message: '出发港口不能超过50个字' }],
        },
        shipName: {
          rules: [{ max: 50, message: '船舶名称不能超过50个字符' }],
        },
        transportGoods: {
          rules: [{ max: 50, message: '运输货物不能超过50个字符' }],
        },
      },
      url: {
        add: '/ship/transportPlan/add',
        edit: '/ship/transportPlan/edit',
        queryById: '/ship/transportPlan/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'status',
            'transportCode',
            'transportGoods',
            'shipName',
            'departPort',
            'mmsi',
            'startDate',
            'destinationPort',
            'arriveDate',
            'carrier',
            'phone',
            'remark',
            'file'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'status',
          'transportCode',
          'transportGoods',
          'shipName',
          'departPort',
          'mmsi',
          'startDate',
          'destinationPort',
          'arriveDate',
          'carrier',
          'phone',
          'remark',
          'file'
        )
      )
    },
  },
}
</script>