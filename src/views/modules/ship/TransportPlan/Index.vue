<template>
  <div class="list-page transport-plan-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
      <template #status="{ record }">
        <a-switch
          checkedChildren="进行中"
          unCheckedChildren="已完成"
          :defaultChecked="record.status == 'underway' ? true : false"
          @change="(value) => onChange(value, record.id)"
        />
      </template>
      <template #arriveDate="{ record }">
        <span style="color:red" v-if="record.status=='underway'&&moment(record.arriveDate).valueOf() <moment(new Date()).valueOf()"> {{record.arriveDate}} </span>
        <span  v-else> {{record.arriveDate}} </span>
      </template>
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import { httpAction } from '@/api/manage'
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import moment from 'dayjs'

export default {
  name: 'TransportPlanList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {
      /* 排序参数 */
      isorter: {
        column: '',
        order: '',
      },
      rightTitle: '运输计划列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          transportCode: null,
        },
        formItems: [{ key: 'transportCode', label: '运单号', type: 'text' }],
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '运单号',
            align: 'center',
            dataIndex: 'transportCode',
          },
          {
            title: '运输状态',
            align: 'center',
            dataIndex: 'status_dictText',
            scopedSlots: { customRender: 'status' },
          },
          {
            title: '预计到达时间',
            align: 'center',
            dataIndex: 'arriveDate',
            // customRender: (text) => {
            //   return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
            // },
            scopedSlots: { customRender: 'arriveDate' },
          },
          {
            title: '运输货物',
            align: 'center',
            dataIndex: 'transportGoods',
          },
          {
            title: '承运人',
            align: 'center',
            dataIndex: 'carrier',
          },
          {
            title: '联系电话',
            align: 'center',
            dataIndex: 'phone',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],
      },

      url: {
        list: '/ship/transportPlan/list',
        delete: '/ship/transportPlan/delete',
        edit: '/ship/transportPlan/edit',
        deleteBatch: '/ship/transportPlan/deleteBatch',
        exportXlsUrl: '/ship/transportPlan/exportXls',
        importExcelUrl: 'ship/transportPlan/importExcel',
      },
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    moment,
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
    onChange(checked, id) {
      const that = this
      let formData = { id: id }
      if (checked) {
        formData.status = 'underway'
      } else {
        formData.status = 'over'
      }
      console.log("传递参数：：",formData)
      httpAction(this.url.edit, formData, 'put')
        .then((res) => {
          if (res.success) {
            that.$message.success(res.message)
            that.$emit('ok')
            this.searchQuery()
          } else {
            that.$message.warning(res.message)
          }
        })
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>