<template>
  <div class="pageMain">
    <Map
      @clickMap="clickMap"
      ref="Map"
      @initMap="initMap"
      :defaultFan="false"
      :defaultZoom="8"
      :defaultCable="false"
      :defaultBoosterStation="false"
      :defaultLegend="false"
      :defaultTypeChange="false"
      :defaultShip="false"
    >
      <!-- 搜索栏 -->
      <template slot="top-left"> </template>
      <template slot="right-top"> </template>
    </Map>
    <div class="tip-order-box">
      <div v-for="item in tableProps.dataSource" :key="item.id" class="pb-10 mb-10" @click="clickOrder(item)">
        <div class="p-6 pb-0">
          <div class="flex mb-8">
            <img :src="require('@/assets/transport-icon.png')" class="w-48 h-48 mr-8" />
            <div class="right-title">
              <div class="font-16 line-24 text-ellipsis" style="color: #1d2129">运单编号：{{ item.transportCode }}</div>
              <div class="font-14 line-22">
                预计到达时间：<span style="color: #3254ff">{{ item.arriveDate }}</span>
              </div>
            </div>
          </div>
          <div class="text-ellipsis-clamp">运输货物：{{ item.transportGoods }}</div>
        </div>
      </div>
    </div>
    <div class="tip-ship-box" v-show="visible">
      <div class="ship-box-title flex-between pb-16 font-16 line-19">
        <div class="title">运单号：{{ currTransOrder }}</div>
        <a-icon type="close" @click="closeOrder" />
      </div>
      <div class="ship-box-content">
        <div>
          <div class="title">运输船舶信息</div>
          <div class="row">
            <div class="col">
              <div class="label">MMSI：</div>
              <div class="data">{{ shipData.mmsi }}</div>
            </div>
            <div class="col">
              <div class="label">船首向：</div>
              <div class="data">{{ shipData.hdg }}</div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <div class="label">呼号：</div>
              <div class="data">{{ shipData.mmsi }}</div>
            </div>
            <div class="col">
              <div class="label">船向：</div>
              <div class="data">{{ shipData.hdg }}</div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <div class="label">IMO：</div>
              <div class="data">{{ shipData.mmsi }}</div>
            </div>
            <div class="col">
              <div class="label">航速：</div>
              <div class="data">{{ shipData.hdg }}</div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <div class="label">类型：</div>
              <div class="data">{{ shipData.type }}</div>
            </div>
            <div class="col">
              <div class="label">纬度：</div>
              <div class="data">{{ (shipData.lat * Math.pow(10, -6)).toFixed(6) }}</div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <div class="label">状态：</div>
              <div class="data">{{ shipData.tradetype }}</div>
            </div>
            <div class="col">
              <div class="label">经度：</div>
              <div class="data">{{ (shipData.lon * Math.pow(10, -6)).toFixed(6) }}</div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <div class="label">船长：</div>
              <div class="data">{{ shipData.length }}</div>
            </div>
            <div class="col">
              <div class="label">目的地：</div>
              <div class="data">{{ shipData.dest }}</div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <div class="label">船宽：</div>
              <div class="data">{{ shipData.width }}</div>
            </div>
            <div class="col">
              <div class="label">预到时间：</div>
              <div class="data">{{ shipData.eta }}</div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <div class="label">吃水：</div>
              <div class="data">{{ shipData.draught }}</div>
            </div>
            <div class="col">
              <div class="label">更新时间：</div>
              <div class="data">{{ shipData.update_time }}</div>
            </div>
          </div>
        </div>
        <a-divider />
        <div>
          <div class="title">所处地区天气情况({{ weather.update_time }})</div>
          <div class="row">
            <div class="col">
              <div class="label">经度：</div>
              <div class="data">{{ (shipData.lon * Math.pow(10, -6)).toFixed(6) }}</div>
            </div>
            <div class="col">
              <div class="label">纬度：</div>
              <div class="data">{{ (shipData.lat * Math.pow(10, -6)).toFixed(6) }}</div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <div class="label">气温：</div>
              <div class="data">{{ weather.temperature }}</div>
            </div>
            <div class="col">
              <div class="label">湿度：</div>
              <div class="data">{{ weather.relaHumidity }}</div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <div class="label">风速：</div>
              <div class="data">{{ weather.windspeed }}</div>
            </div>
            <div class="col">
              <div class="label">风向：</div>
              <div class="data">{{ weather.windDirection }}</div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <div class="label">降雨量：</div>
              <div class="data">{{ weather.precipitation }}</div>
            </div>
            <div class="col">
              <div class="label">能见度：</div>
              <div class="data">{{ weather.visibility }}</div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <div class="label">浪高：</div>
              <div class="data">{{ weather.waveheight }}</div>
            </div>
            <div class="col">
              <div class="label">浪向：</div>
              <div class="data">{{ weather.waveDirection }}</div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <div class="label">浪周期：</div>
              <div class="data">{{ weather.wavePeriod }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Map from '@/components/Map'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { getAction } from '@/api/manage'
import MapService from '@/utils/MapService'
import moment from 'moment'
export default {
  mixins: [JeecgTreeListMixin],
  components: { Map },
  data() {
    return {
      map: '',
      mapDefaultZoom: 8,
      mapTool: ['alert', 'menu', 'position', 'big', 'xiao'],
      type: '1',
      // 出海情况
      alertShipList: [],
      mousemoveLnglat: {
        lng: '0',
        lat: '0',
      },
      shipType: 'alarmShip',
      currTransOrder: '',
      visible: false,
      shipData: {},
      weather: {},
      order: {},
      infoWindow: {},
      url: {
        list: '/ship/transportPlan/underwayList',
      },
      tableProps: {
        ipagination: false,
        dataSource: [],
        columns: [
          {
            title: '运单号',
            dataIndex: 'transportCode',
            width: 100,
            align: 'center',
            scopedSlots: { customRender: 'installedIcon' },
          },
          {
            title: '运输货物',
            align: 'center',
            width: 100,
            dataIndex: 'transportGoods',
          },
          {
            title: '预计到达时间',
            align: 'center',
            dataIndex: 'arriveDate',
            width: 100,
          },
        ],
      },
    }
  },
  mounted() {},
  methods: {
    moment,
    clickMap(event, type) {},
    /**
     * @description 地图初始化完成
     */
    initMap(map, shipData) {
      this.shipData = shipData
      this.map = map
    },

    // ===============地图工具类操作===================
    handleMapToolClick(eventName) {
      if (!eventName) {
        return
      }
      this[eventName]()
    },
    alert() {
      console.log('alert')
      // 初始化轮播
      setTimeout(() => {
        // this.startAutoScroll('scrollList', 'shipAlert')
      })
    },
    menu() {
      console.log('menu')
    },
    position() {
      const { mapCenter } = mapData
      this.map.panTo(new T.LngLat(mapCenter[0], mapCenter[1]), this.mapDefaultZoom)
    },
    big() {
      this.map.zoomIn()
      console.log('big')
    },
    xiao() {
      this.map.zoomOut()
      console.log('xiao')
    },
    closeOrder() {
      this.visible = false
    },
    // 运单详情
    clickOrder(record) {
      // this.clearTip();
      let url = '/ship/transportPlan/orderDetail'
      let params = { order: record.id }
      getAction(url, params).then((res) => {
        const { success, result } = res
        if (!success) {
          this.$message.error('获取预警船舶数据失败')
          return
        }
        if (result) {
          this.order = record
          this.currTransOrder = record.transportCode
          this.visible = true
          let ship
          let weather
          if (typeof result == 'String') {
            let data = JSON.parse(result)
            ship = data.ship
            weather = data.weather
          } else {
            ship = result.ship
            weather = result.weather
          }
          const { lat, lon } = ship
          console.log('单船', ship)
          // 定位到船舶
          this.map.panTo(new T.LngLat((lon * Math.pow(10, -6)).toFixed(6), lat * Math.pow(10, -6)), this.mapDefaultZoom)
          MapService.setShipMarks(this.map, [ship], 'TransportShip', this.markerClick)
          // 让弹窗展示
          // 413267760
          this.windowInfo = ship
          // let decrypted = transShipDataType(ship)
          // console.log('转换', decrypted)
          this.shipData = ship
          this.weather = weather
        }
      })
    },
    markerClick(data) {
      const htmlContent = `<div class="infoWindow" >
        <div style="padding:8px 0;line-height:20px;border-bottom: 1px solid #E5E6EB;"> 运单编号：${this.currTransOrder}</div>
        <div  style="padding:8px 0;line-height:20px;border-bottom: 1px solid #E5E6EB;"> 运输货物：${this.order.transportGoods}</div> 
        <div  style="padding:8px 0;line-height:20px;border-bottom: 1px solid #E5E6EB;"> 计划到达：${this.order.arriveDate}</div> 
        <div  style="padding:8px 0;line-height:20px;border-bottom: 1px solid #E5E6EB;"> 承运人：  ${this.order.carrier}</div> 
        <div  style="padding:8px 0;line-height:20px"> 联系电话：${this.order.phone}</div>
        </div>`

      let infoWindow = MapService.showTip(data.item, htmlContent)
      this.infoWindow = infoWindow
    },
    clearTip() {
      if (this.infoWindow) {
        MapService.clearTip(this.map, this.infoWindow)
      }
    },
  },
  destroyed() {
    clearInterval(this.shipAlert)
  },
}
</script>

<style lang="less" scoped>
.pageMain {
  --index: 500;
  --left: 54px;
  --left1: 24px;
  --top: 50px;
  --top16: 16px;
  --right: 54px;
  --bottom: 16px;
  width: 100%;
  height: 100%;
  position: relative;
  #map {
    width: 100%;
    /* 先这样定义页面高度 后面需要动态计算 */
    // height: 100%; //有个bug  第一次进来地图会展示不全  但是再次进来就好了
    height: calc(88vh - 5px);
  }

  .ship-info-window {
    position: absolute;
    top: var(--top);
    left: var(--left);
    z-index: var(--index);
    width: 500px;
    height: 48px;
  }
  .alert-info-window {
    position: absolute;
    top: 80px;
    right: var(--right);
    z-index: var(--index);
    width: 500px;
    height: 48px;
  }
  .map-tool {
    position: absolute;
    bottom: 128px;
    right: var(--top);
    z-index: var(--index);
    .tool-item {
      width: 36px;
      height: 36px;
      border-radius: 4px;
      margin-bottom: 10px;
      cursor: pointer;
    }
    .position {
      background: url('../../../../assets/map/img/3.png') no-repeat;
      background-size: 100%;
    }
    .position:active {
      background: url('../../../../assets/map/img/3-active.png') no-repeat;
      background-size: 100%;
    }
    .big {
      background: url('../../../../assets/map/img/4.png') no-repeat;
      background-size: 100%;
    }
    .big:active {
      background: url('../../../../assets/map/img/4-active.png') no-repeat;
      background-size: 100%;
    }
    .xiao {
      background: url('../../../../assets/map/img/5.png') no-repeat;
      background-size: 100%;
    }
    .xiao:active {
      background: url('../../../../assets/map/img/5-active.png') no-repeat;
      background-size: 100%;
    }
  }
  .ship-search {
    .input-search {
      width: 350px;
      height: 36px;
      ::v-deep .ant-btn-primary {
        background-color: #3254ff;
      }
    }
  }
  .ship-status {
    width: 500px;
    height: 48px;
    position: absolute;
    bottom: var(--bottom);
    left: var(--left);
    z-index: var(--index);
    background: #f2f3f5;
    box-shadow: 0px 2px 4px 0px rgba(195, 195, 195, 0.25);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #e5e6eb;
    background: #f2f3f5;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #e5e6eb;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding-left: 20px;
    padding-right: 20px;
    .line {
      width: 1px;
      height: 12px;
      background-color: #d3d4d4;
    }
    .item {
      display: flex;
      .name {
        margin-right: 5px;
      }
      .value {
        font-weight: bold;
        color: #3254ff;
      }
    }
  }
  .tip-order-box {
    background: #ffffff;
    position: absolute;
    top: 16px;
    left: 24px;
    z-index: 500;
    width: 320px;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
    border-radius: 2px;
    padding: 10px;
    max-height: 470px;
    overflow: auto;
    > div {
      border-bottom: 1px solid #e5e6eb;
      > div {
        &:hover {
          cursor: pointer;
          background: #e8eeff;
        }
        .right-title {
          width: calc(100% - 56px);
        }
      }
    }
  }

  .tip-ship-box {
    background: #ffffff;
    position: absolute;
    top: 16px;
    right: 24px;
    z-index: 500;
    box-shadow: 0px 3px 8px 0px rgba(31, 49, 141, 0.15);
    border-radius: 2px;
    width: 520px;
    padding: 16px;
    .ship-box-title {
      border-bottom: 1px solid #e5e6eb;
      color: #1d2129;
      .title {
        &::before {
          content: '';
          width: 4px;
          height: 10px;
          background: #066fec;
          display: inline-block;
          margin-right: 8px;
        }
      }
    }

    .ship-box-content {
      // height: 220px;
      // overflow: auto;
      margin-top: 16px;
      .title {
        font-size: 16px;
        color: #1d2129;
        line-height: 20px;
        margin-bottom: 8px;
      }
      .row {
        display: flex;
        line-height: 28px;
        .col {
          width: 50%;
          display: flex;
          .label {
            color: #86909c;
            width: 100px;
            text-align: right;
          }
          .data {
            color: #1d2129;
            margin-left: 5px;
          }
        }
      }
      // padding: 10px;
      // box-shadow: 0px 3px 8px 0px rgba(31, 49, 141, 0.15);
      // .ship-box-row {
      //   display: flex;
      //   .ship-box-row-label {
      //     width: 60px;
      //     text-align: right;
      //     padding-right: 6px;
      //   }
      //   .ship-box-row-data {
      //     width: 120px;
      //     text-align: left;
      //     padding-left: 6px;
      //   }
      // }
      .noData {
        width: 100%;
        height: 100%;
        text-align: center;
      }
    }
  }
}

.map-tool-alert-title {
  background-color: #1890ff;
  color: #fff;
  height: 50px;
  line-height: 50px;
  border-radius: 8px 8px 0 0;
  padding: 0 20px;
  font-size: 16px;
  font-weight: bold;
}
.content {
  height: 203px;
  width: 500px;
  .th {
    display: flex;
    background-color: rgb(223, 235, 254);
    height: 40px;
    line-height: 40px;
    > div {
      width: 25%;
      text-align: center;
    }
  }
  .scroll-container {
    height: calc(100% - 60px); /* 设置容器高度，超出部分会出现滚动条 */
    overflow: auto; /* 显示滚动条 */
    .scrollBar {
      .td {
        display: flex;
        > div {
          width: 25%;
          text-align: center;
          border: 1px solid #f2f7ff;
          height: 30px;
          line-height: 30px;
          padding: 0 10px;
        }
      }
    }
  }
}

.scrollBar::-webkit-scrollbar {
  width: 8px;
  height: 5px !important;
  /**/
}
.scrollBar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}
.scrollBar::-webkit-scrollbar-thumb {
  background: #399df0;
  border-radius: 10px;
}
.scrollBar::-webkit-scrollbar-thumb:hover {
  background: #399df0;
}
.scrollBar::-webkit-scrollbar-corner {
  background: #179a16;
}
.infoWindow {
  background-color: red;
}
</style>
