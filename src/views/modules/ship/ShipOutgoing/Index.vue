<template>
  <div class="list-page ship-outgoing-list">
    <table-layout
      :search-props="searchProps"
      :table-props="tableProps"
      @search-submit="handleSearchSubmit"
      @table-change="handleSearchSubmit"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import extension from './extension'

export default {
  name: 'ShipOutgoingList',
  mixins: [JeecgTreeListMixin, extension],
  components: {
    Modal,
  },
  data() {
    return {
      searchData: {},
      // 搜索组件的props
      searchProps: {
        formModel: {
          name: null,
          typeValue: null,
          date: null,
        },
        formItems: [
          { key: 'name', label: '船舶名称', classType: 'input' },
          { key: 'typeValue', label: '船舶类型', classType: 'list', dictCode: 'ship_typ_code' },
          // { key: 'date', label: '日期', classType: 'date' },
          {
            key: 'date', 
            label: '数据更新时间', 
            type: 'datetime_range',
            keyParams: ['startTime', 'endTime'] ,
            format:"YYYY-MM-DD",
            showTime: false,
            // colConfig: { sm: 24, md: 12, lg: 8, xl: 12 },
            labelCol: {
              xs: { span: 24 },
              sm: { span: 7 },
            },
            wrapperCol: {
              xs: { span: 24 },
              sm: { span: 17 },
            },
          },
        ],
      },
      // 表格组件的props
      tableProps: {
        //此处要求排序方式为  先按进退场排序,同状态 按创建时间倒序 ,已在后端处理,前端不要传排序方式
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '船舶名称',
            align: 'center',
            dataIndex: 'name',
          },
          {
            title: '船舶类型',
            align: 'center',
            dataIndex: 'typeValue_dictText',
          },
          {
            title: '船东',
            align: 'center',
            dataIndex: 'shipownerName',
          },
          {
            title: '进/退场',
            align: 'center',
            dataIndex: 'isOut_dictText',
          },
          {
            title: '进/退场日期',
            align: 'center',
            dataIndex: 'date',
            customRender: (text) => {
              return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
            },
          },
          {
            title: '使用单位',
            align: 'center',
            dataIndex: 'unit_dictText',
            customRender: (text) => {
              return text === '' ? '-' : text
            },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 175,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],
      },

      url: {
        list: '/ship/shipOutgoing/list',
        delete: '/ship/shipOutgoing/delete',
        deleteBatch: '/ship/shipOutgoing/deleteBatch',
        exportXlsUrl: '/ship/shipOutgoing/exportXls',
        importExcelUrl: 'ship/shipOutgoing/importExcel',
      },
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    modalFormOk() {
      const formModel = { ...this.searchData.formModel }
      delete formModel.date
      this.loadData(formModel)
    },

    handleSearchSubmit(searchData) {
      this.searchData = searchData || {}
      const formModel = { ...this.searchData.formModel }
      delete formModel.date
      this.loadData(formModel)
    },
  },
}
</script>
