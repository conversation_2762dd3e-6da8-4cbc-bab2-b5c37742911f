<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="船舶名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['name', validatorRules.name]"
                :trigger-change="true"
                dictCode="ship_info,name,id"
                placeholder="请选择船舶名称"
                @change="handleShipChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="船舶类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['typeValue', validatorRules.typeValue]"
                :trigger-change="true"
                dictCode="ship_typ_code"
                placeholder="请选择船舶类型"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="进/退场状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['isOut', validatorRules.isOut]"
                :trigger-change="true"
                dictCode="ship_exit_status"
                placeholder="请选择进/退场状态"
                @change="handleIsOutChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="isShow">
            <a-form-item label="使用单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['unit', validatorRules.unit]"
                :trigger-change="true"
                dictCode="project_unit,name,id"
                placeholder="请选择使用单位"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="进退场时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择进退场时间"
                v-decorator="['date', validatorRules.date]"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="备注"
              :labelCol="{
                xs: { span: 24 },
                sm: { span: 3 },
              }"
              :wrapperCol="{
                xs: { span: 24 },
                sm: { span: 20 },
              }"
            >
              <a-textarea v-decorator="['remark', validatorRules.remark]" rows="4" placeholder="请输入备注" />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'ShipOutgoingForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      isShow: true,
      shipId: '',
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        name: {
          rules: [{ required: true, message: '请选择船舶名称!' }],
        },
        typeValue: {
          rules: [{ required: true, message: '请选择船舶类型!' }],
        },
        isOut: {
          rules: [{ required: true, message: '请选择进/退场状态!' }],
        },
        unit: {
          rules: [{ required: true, message: '请选择使用单位!' }],
        },
        date: {
          rules: [{ required: true, message: '请选择进退场时间!' }],
        },
        remark: {
          rules: [{ max: 500, message: '备注不能超过500个字符' }],
        },
      },
      url: {
        add: '/ship/shipOutgoing/add',
        edit: '/ship/shipOutgoing/edit',
        queryById: '/ship/shipOutgoing/queryById',
        queryByShip: '/departureWarning/shipInfo/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    handleIsOutChange(value) {
      if (value == 1) {
        this.isShow = false
      } else {
        this.isShow = true
      }
    },
    handleShipChange(value) {
      console.log('handleShipChange', value)
      this.shipId = value
      getAction(this.url.queryByShip, { id: value }).then((res) => {
        if (res.success) {
          this.form.setFieldsValue({ name: res.result.name, typeValue: res.result.typeValue })
        }
      })
    },
    add() {
      this.edit({})
    },
    edit(record) {
      console.log(record)
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(this.model, 'shipownerName', 'name', 'typeValue', 'isOut', 'unit', 'date', 'remark')
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }

          let formData = Object.assign(this.model, values, { shipDataId: this.shipId })
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                that.isShow = true
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(row, 'shipownerName', 'name', 'typeValue', 'isOut', 'unit', 'date', 'remark', 'shipDataId')
      )
    },
  },
}
</script>
