<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="qbs名称">
              <a-input placeholder="请输入qbs名称" v-model="queryParam.qbsName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="qbs编码">
              <a-input placeholder="请输入qbs编码" v-model="queryParam.qbsCode"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="验评结果">
                <j-dict-select-tag placeholder="请选择验评结果" v-model="queryParam.evaluationState" dictCode="evaluation_state"/>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->
    
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('验评资料')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        class="j-table-force-nowrap"
        :scroll="{x:true}"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <modal ref="modalForm" @ok="modalFormOk"/>
  </a-card>
</template>

<script>

  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import Modal from './components/Modal'
  import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'
  import '@/assets/less/TableExpand.less'

  export default {
    name: "QualityEvaluationInformationList",
    mixins:[JeecgListMixin],
    components: {
      Modal
    },
    data () {
      return {
        description: '验评资料管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'qbs名称',
            align:"center",
            dataIndex: 'qbsName'
          },
          {
            title:'qbs编码',
            align:"center",
            dataIndex: 'qbsCode'
          },
          {
            title:'验评结果',
            align:"center",
            dataIndex: 'evaluationState_dictText'
          },
          {
            title:'单位工程量',
            align:"center",
            dataIndex: 'quantityWork'
          },
          {
            title:'单位工程量单位',
            align:"center",
            dataIndex: 'quantityWorkUnit_dictText'
          },
          {
            title:'施工开始日期',
            align:"center",
            dataIndex: 'constructionStartDate',
            customRender:function (text) {
              return !text?"":(text.length>10?text.substr(0,10):text)
            }
          },
          {
            title:'施工结束日期',
            align:"center",
            dataIndex: 'constructionEndDate',
            customRender:function (text) {
              return !text?"":(text.length>10?text.substr(0,10):text)
            }
          },
          {
            title:'施工单位',
            align:"center",
            dataIndex: 'constructionUnit_dictText'
          },
          {
            title:'图片资料',
            align:"center",
            dataIndex: 'pictureInformation',
            scopedSlots: {customRender: 'imgSlot'}
          },
          {
            title:'视频资料',
            align:"center",
            dataIndex: 'videoInformation',
            scopedSlots: {customRender: 'fileSlot'}
          },
          {
            title:'验评附件资料',
            align:"center",
            dataIndex: 'attachment',
            scopedSlots: {customRender: 'fileSlot'}
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' },
          }
        ],
        url: {
          list: "/quality/qualityEvaluationInformation/list",
          delete: "/quality/qualityEvaluationInformation/delete",
          deleteBatch: "/quality/qualityEvaluationInformation/deleteBatch",
          exportXlsUrl: "/quality/qualityEvaluationInformation/exportXls",
          importExcelUrl: "quality/qualityEvaluationInformation/importExcel",
          
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      }
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
         fieldList.push({type:'string',value:'qbsName',text:'qbs名称',dictCode:''})
         fieldList.push({type:'string',value:'qbsCode',text:'qbs编码',dictCode:''})
         fieldList.push({type:'string',value:'evaluationState',text:'验评结果',dictCode:'evaluation_state'})
         fieldList.push({type:'string',value:'quantityWork',text:'单位工程量',dictCode:'	'})
         fieldList.push({type:'string',value:'quantityWorkUnit',text:'单位工程量单位',dictCode:'work_unit'})
         fieldList.push({type:'date',value:'constructionStartDate',text:'施工开始日期'})
         fieldList.push({type:'date',value:'constructionEndDate',text:'施工结束日期'})
         fieldList.push({type:'sel_depart',value:'constructionUnit',text:'施工单位'})
         fieldList.push({type:'string',value:'pictureInformation',text:'图片资料',dictCode:''})
         fieldList.push({type:'string',value:'videoInformation',text:'视频资料',dictCode:''})
         fieldList.push({type:'string',value:'attachment',text:'验评附件资料',dictCode:''})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>