<template>
  <j-modal
    :title="title"
    :width="width"
    :loadId="loadId"
    :visible="visible"
    :confirmLoading="confirmLoading"
    :maskClosable="false"
    :destroyOnClose="true"
    switchFullscreen
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <a-spin :spinning="confirmLoading">
      <j-form-container :disabled="disableSubmit">
        <a-form :form="form" slot="detail">
          
          <a-form-item label="父级节点" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <j-tree-select
              ref="treeSelect"
              placeholder="请选择父级节点"
              v-decorator="['pid']"
              dict="quality_catalogue_tree,name,id"
              pidField="pid"
              pidValue="0"
              hasChildField="has_child"
            >
            </j-tree-select>
          </a-form-item>
          <a-form-item label="节点编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input v-decorator="['code', validatorRules.code]" placeholder="请输入节点编码"></a-input>
          </a-form-item>
          <a-form-item label="节点名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input v-decorator="['name', validatorRules.name]" placeholder="请输入节点名"></a-input>
          </a-form-item>

          <a-form-item label="层级" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <j-dict-select-tag
              type="list"
              v-decorator="['qbsLayer', validatorRules.qbsLayer]"
              :trigger-change="true"
              dictCode="qbs_layer"
              placeholder="请选择层级"
            />
          </a-form-item>
          <a-form-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-textarea v-decorator="['remark', validatorRules.remark]" :maxlengh="500" placeholder="请输入节点名" />
          </a-form-item>
        </a-form>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>
import { httpAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
export default {
  name: 'QualityCatalogueTreeModal',
  components: {},
  props: {
    loadId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      title: '操作',
      width: 800,
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },

      confirmLoading: false,
      validatorRules: {
        code: {
          rules: [{ required: true, message: '请输入节点编码!' }],
        },
        name: {
          rules: [{ required: true, message: '请输入节点名!' }],
        },
        qbsLayer: {
          rules: [{ required: true, message: '请输入层级!' }],
        },
      },
      url: {
        add: '/quality/qualityCatalogueTree/add',
        edit: '/quality/qualityCatalogueTree/edit',
      },
      expandedRowKeys: [],
      pidField: 'pid',
      disableSubmit: false, // 是否禁止编辑
    }
  },
  created() {},
  methods: {
    add(obj) {
      this.edit(obj)
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.model.pid = this.loadId
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'code', 'name', 'pid', 'qbsLayer','remark'))
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let old_pid = this.model[this.pidField]
          let formData = Object.assign(this.model, values)
          let new_pid = this.model[this.pidField]
          if (this.model.id && this.model.id === new_pid) {
            that.$message.warning('父级节点不能选择自己')
            that.confirmLoading = false
            return
          }
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                this.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
              that.close()
            })
        }
      })
    },
    handleCancel() {
      this.close()
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'code', 'name', 'pid', 'qbsLayer','remark'))
    },
    submitSuccess(formData, flag) {
      if (!formData.id) {
        let treeData = this.$refs.treeSelect.getCurrTreeData()
        this.expandedRowKeys = []
        this.getExpandKeysByPid(formData[this.pidField], treeData, treeData)
        this.$emit('ok', formData, this.expandedRowKeys.reverse())
      } else {
        this.$emit('ok', formData, flag)
      }
    },
    getExpandKeysByPid(pid, arr, all) {
      if (pid && arr && arr.length > 0) {
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].key == pid) {
            this.expandedRowKeys.push(arr[i].key)
            this.getExpandKeysByPid(arr[i]['parentId'], all, all)
          } else {
            this.getExpandKeysByPid(pid, arr[i].children, all)
          }
        }
      }
    },
  },
}
</script>