<template>
  <div class="list-page quality-catalogue-tree-list">
    <table-layout
      ref="tableLayout"
      :search-props="searchProps"
      :table-props="tableProps"
      :tree-props="treeProps"
      @tree-select="onTreeSelect"
      @search-submit="searchQuery"
      @table-change="onTableChange"
      @table-expand="onTableExpand"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk" :loadId = "loadId"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { getAction ,deleteAction} from '@/api/manage'

export default {
  name: 'QualityCatalogueTreeList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {
      rightTitle: 'quality_catalogue_tree列表',
      loadId: "",
      // 搜索组件的props
      searchProps: {
        formModel: {},
        formItems: [],
      },
      treeProps: {
        treeField: 'pid',
        selectedKeys: [],
        isSelectParentNodes: true,
        replaceFields: { title: 'name', key: 'id' },
        treeData: [],
        internalTreeLoading: true,
        checkStrictly: true,
        defaultExpandParent: true,
        autoExpandParent: false,
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: false,
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '节点名称',
            align: 'center',
            dataIndex: 'name',
          },
          {
            title: '节点编码',
            align: 'center',
            dataIndex: 'code',
          },

          // {
          //   title: '父级节点id',
          //   align: 'center',
          //   dataIndex: 'pid',
          // },
          {
            title: '层级',
            align: 'center',
            dataIndex: 'qbsLayer_dictText',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus',
            handler: this.handleAdd,
          },
        ],
        expandedRowKeys: [], // 用于控制哪些行展开
      },

      url: {
        list: '/quality/qualityCatalogueTree/rootList',
        childList: '/quality/qualityCatalogueTree/childList',
        queryTreeList: '/quality/qualityCatalogueTree/childList', //查询树结构 传code
        getChildListBatch: '/quality/qualityCatalogueTree/getChildListBatch',
        delete: '/quality/qualityCatalogueTree/delete',
        deleteBatch: '/quality/qualityCatalogueTree/deleteBatch',
        exportXlsUrl: '/quality/qualityCatalogueTree/exportXls',
        importExcelUrl: 'quality/qualityCatalogueTree/importExcel',
      },
    }
  },
  created() {
    this.loadData()
  },
  computed: {},
  watch: {},
  methods: {
    onTreeSelect(load) {
      this.loadId = load.params.pid
      this.selectChid(this.loadId)
      // this.onSearchSubmit(params)
    },

    selectChid(pid){
      getAction(this.url.childList, { pid: pid }).then((res2) => {
        if (res2.success) {
          this.tableProps.dataSource = res2.result.records
        }
      })
    },
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
    loadData(params) {
      getAction(this.url.list, { pid: '0' }).then((res) => {
        if (res.success) {
          // 处理数据 处理成树形结构
          this.treeProps.treeData = res.result
          let pid = res.result[0].id
          getAction(this.url.childList, { pid: pid }).then((res2) => {
            if (res2.success) {
              this.tableProps.dataSource = res2.result.records
            }
          })
        }
      })
    },
    handleDelete: function (id) {
      if (typeof id === 'object') {
        id = id.id
      }
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      let that = this
      this.$confirm({
        title: '提醒',
        content: '确认要删除吗?',
        okText: '确认',
        cancelText: '取消',
        onOk() {
          deleteAction(that.url.delete, { id: id }).then((res) => {
            if (res.success) {
              that.$message.success('删除成功！')
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        },
        onCancel() {},
      })
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>