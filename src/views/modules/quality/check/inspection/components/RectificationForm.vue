<template>
  <a-modal
    :title="title"
    :width="1024"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="整改单号">
              <a-input
                v-decorator="['code', validatorRules.code]"
                placeholder="请输入整改单号"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="整改工区">
              <a-input
                v-decorator="['workArea', validatorRules.workArea]"
                placeholder="请输入整改工区"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="整改部位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-tree-select
                ref="treeSelect"
                placeholder="请选择父级节点"
                v-decorator="['rectificationPart']"
                dict="quality_catalogue_tree,name,id"
                pidField="pid"
                pidValue="0"
                hasChildField="has_child"
              >
              </j-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="整改期限">
              <j-date
                v-decorator="['rectificationDeadline', validatorRules.rectificationDeadline]"
                placeholder="请选择整改期限"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="整改内容" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
              <a-textarea
                v-decorator="['rectificationContent', validatorRules.rectificationContent]"
                placeholder="请输入整改内容"
                :rows="3"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="整改建议" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
              <a-textarea
                v-decorator="['rectificationSuggestions', validatorRules.rectificationSuggestions]"
                placeholder="请输入整改建议"
                :rows="3"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="质量问题审核人" :label-col="labelCol" :wrapper-col="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['reviewer', validatorRules.reviewer]"
                :trigger-change="true"
                dictCode="person_info,name,id"
                placeholder="请选择审核人"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="影像资料" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
              <j-upload
                v-decorator="['mediaFiles']"
                :multiple="true"
                :file-type="'all'"
                text="上传影像资料"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import pick from 'lodash.pick'

export default {
  name: 'RectificationForm',
  data() {
    return {
      title: '新增整改记录',
      visible: false,
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        code: {
          rules: [{ required: true, message: '请输入整改单号!' }]
        },
        workArea: {
          rules: [{ required: true, message: '请输入整改工区!' }]
        },
        rectificationPart: {
          rules: [{ required: true, message: '请选择整改部位!' }]
        },
        rectificationContent: {
          rules: [{ required: true, message: '请输入整改内容!' }]
        },
        rectificationDeadline: {
          rules: [{ required: true, message: '请选择整改期限!' }]
        },
        reviewer: {
          rules: [{ required: true, message: '请选择质量问题审核人!' }]
        }
      }
    }
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.title = record.id ? '编辑' : '新增'

      this.$nextTick(() => {
        if (record.id) {
          this.form.setFieldsValue(
            pick(
              this.model,
              'code',
              'workArea',
              'rectificationPart',
              'rectificationContent',
              'mediaFiles',
              'rectificationSuggestions',
              'reviewer',
              'rectificationDeadline'
            )
          )
        }
      })
    },
    handleOk() {
      const that = this
      this.form.validateFields((err, values) => {
        if (!err) {
          // 将表单数据提交到父组件，而不是直接发送请求
          if (this.model.id) {
            values.id = this.model.id
          }
          that.$emit('submit', values)
          that.handleCancel()
        }
      })
    },
    handleCancel() {
      this.visible = false
      this.form.resetFields()
    }
  }
}
</script>

<style scoped>
</style>
