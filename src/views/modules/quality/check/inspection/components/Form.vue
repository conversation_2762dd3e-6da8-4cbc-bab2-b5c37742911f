<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="检查编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['code', validatorRules.code]"
                placeholder="请输入检查编号"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查工区" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['workArea', validatorRules.workArea]"
                placeholder="请输入检查工区"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['inspectionType', validatorRules.inspectionType]"
                :trigger-change="true"
                dictCode="inspection_type"
                placeholder="请选择检查类型"
                @change="handleInspectionTypeChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择检查日期"
                v-decorator="['inspectionDate', validatorRules.inspectionDate]"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查结果" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['inspectionResult', validatorRules.inspectionResult]"
                :trigger-change="true"
                dictCode="inspection_result"
                @change="handleSelectedInspectionResult"
                placeholder="请选择检查结果"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查人员" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-multi-select-tag
                v-decorator="['inspector', validatorRules.inspector]"
                dictCode="person_info,name,id"
                placeholder="请选择"
              >
              </j-multi-select-tag>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查上报人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['reporter', validatorRules.reporter]"
                placeholder="请输入检查编号"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="检查单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['reportingUnit', validatorRules.reportingUnit]"
                :trigger-change="true"
                dictCode="project_unit,name,id"
                placeholder="请选择所属单位"
                @change="handleDepartmentChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="检查内容" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <a-input
                type="textarea"
                autocomplete="off"
                v-decorator="['inspectionContent', validatorRules.inspectionContent]"
                placeholder="请输入检查内容"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="检查依据" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <a-input
                type="textarea"
                autocomplete="off"
                v-decorator="['inspectionBasis']"
                placeholder="请输入检查依据"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
        <!-- 调试信息 -->

        <a-row v-if="selectedInspectionResult === 'ywt'">
          <a-col :span="24">
            <div class="detail-container">
              <div class="record-header">
                <span class="detail-title">质量问题整改列表</span>
                <a-button type="primary" icon="plus" @click="addRectificationRecord">新增</a-button>
              </div>
              <a-table
                :dataSource="trainRecords"
                :columns="recordColumns"
                :pagination="false"
                bordered
                size="small"
                :rowKey="(record, index) => index"
              >
                <template slot="rectificationPart" slot-scope="text, record">
                  <j-tree-select
                    :value="record.rectificationPart"
                    placeholder="请选择父级节点"
                    dict="quality_catalogue_tree,name,id"
                    pidField="pid"
                    pidValue="0"
                    hasChildField="has_child"
                    :disabled="true"
                    style="width: 100%"
                  >
                  </j-tree-select>
                </template>
                <template slot="reviewer" slot-scope="text, record">
                  <j-dict-select-tag
                    :value="record.reviewer"
                    type="list"
                    :trigger-change="false"
                    dictCode="person_info,name,id"
                    placeholder="请选择质量问题审核人"
                    :disabled="true"
                    style="width: 100%"
                  />
                </template>
                <template slot="action" slot-scope="text, record, index">
                  <a
                    @click="editRectificationRecord(record, index)"
                    style="margin-right: 8px;"
                    :disabled="!!record.bpmStatus"
                  >
                    编辑
                  </a>
                  <a-popconfirm
                    :disabled="!!record.bpmStatus"
                    title="确定删除吗?"
                    @confirm="deleteRectificationRecord(index)"
                  >
                    <a style="color: #ff4d4f;">删除</a>
                  </a-popconfirm>
                </template>
              </a-table>
            </div>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>

      </a-form>
    </j-form-container>

    <!-- 整改记录表单弹窗 -->
    <rectification-form ref="rectificationForm" @submit="handleRectificationSubmit" />
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import RectificationForm from './RectificationForm.vue'

export default {
  name: 'QualityInspectionRecordsForm',
  components: {
    RectificationForm
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      // 检查类型选择结果
      selectedInspectionResult: null,
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      // 整改记录相关
      trainRecords: [],
      recordColumns: [
        {
          title: '序号',
          width: 60,
          align: 'center',
          customRender: (text, record, index) => index + 1
        },
        {
          title: '整改单号',
          dataIndex: 'code',
          align: 'center'
        },
        {
          title: '整改工区',
          dataIndex: 'workArea',
          align: 'center'
        },
        {
          title: '整改部位',
          align: 'center',
          scopedSlots: { customRender: 'rectificationPart' }
        },
        {
          title: '整改期限',
          dataIndex: 'rectificationDeadline',
          align: 'center'
        },
        {
          title: '审核人',
          align: 'center',
          scopedSlots: { customRender: 'reviewer' }
        },
        {
          title: '整改建议',
          dataIndex: 'rectificationSuggestions',
          align: 'center'
        },
        // {
        //   title: '整改措施',
        //   dataIndex: 'rectificationMeasure',
        //   align: 'center',
        //   width: 150,
        //   customRender: (text) => {
        //     if (!text || text.trim() === '') {
        //       return '-'
        //     }
        //     return text.length > 20 ? text.substring(0, 20) + '...' : text
        //   }
        // },
        {
          title: '状态',
          align: 'center',
          width: 80,
          customRender: (text, record) => {
            if (record.rectificationMeasure && record.rectificationMeasure.trim() !== '') {
              return <a-tag color="green">已整改</a-tag>
            }
            return <a-tag color="orange">待整改</a-tag>
          }
        },
        {
          title: '操作',
          width: 120,
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      validatorRules: {
        code: {
          rules: [{ required: true, message: '请输入检查编号，自动生成!' }]
        },
        workArea: {
          rules: [{ required: true, message: '请输入检查工区，PBS树形列表!' }]
        },
        inspectionType: {
          rules: [{ required: true, message: '请输入检查类型，引用数据字典!' }]
        },
        inspectionDate: {
          rules: [{ required: true, message: '请输入检查日期，格式为yyyy-mm-dd!' }]
        },
        inspectionResult: {
          rules: [{ required: true, message: '请输入检查结果，引用数据字典!' }]
        },
        inspector: {
          rules: [{ required: true, message: '请输入检查人员，引用基础数据!' }]
        },
        reporter: {
          rules: [{ required: true, message: '请输入检查上报人，默认为当前系统用户名称!' }]
        },
        reportingUnit: {
          rules: [{ required: true, message: '请输入检查单位，根据用户自动带出!' }]
        },
        inspectionContent: {
          rules: [{ required: true, message: '请输入检查内容，必填，最多1000字!' }]
        }
      },
      url: {
        add: '/quality/qualityInspectionRecords/add',
        edit: '/quality/qualityInspectionRecords/edit',
        queryById: '/quality/qualityInspectionRecords/queryById'
      }
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    handleSelectedInspectionResult(value) {
      this.selectedInspectionResult = value
      console.log('handleSelectedInspectionResult', this.handleSelectedInspectionResult)
    },
    // 处理检查类型变化
    handleInspectionTypeChange(value) {
      console.log('检查类型变化:', value)
      this.selectedInspectionType = value
      console.log('selectedInspectionType 设置为:', this.selectedInspectionType)
      // 如果不是问题整改类型，清空已有的整改记录
      if (value !== 'ywt') {
        this.trainRecords = []
      }
    },
    handleDepartmentChange(value) {
      console.log('handleDepartmentChange', value)
      this.shipId = value
      getAction(this.url.queryUnitById, { id: value }).then((res) => {
        if (res.success) {
          this.form.setFieldsValue({ departmentType: res.result.unitType })
        }
      })
    },
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true

      // 初始化检查类型选择状态
      this.selectedInspectionType = record.inspectionType || null
      this.selectedInspectionResult = record.inspectionResult || null
      console.log('edit 方法中设置 selectedInspectionType:', this.selectedInspectionType)

      // 处理整改记录数据
      if (record.details && Array.isArray(record.details)) {
        this.trainRecords = [...record.details]
      } else {
        this.trainRecords = []
      }

      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'code',
            'workArea',
            'inspectionType',
            'inspectionDate',
            'inspectionResult',
            'inspector',
            'reporter',
            'reportingUnit',
            'inspectionContent',
            'inspectionBasis'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          // 将整改记录作为 details 字段添加到提交数据中
          formData.details = this.trainRecords
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'code',
          'workArea',
          'inspectionType',
          'inspectionDate',
          'inspectionResult',
          'inspector',
          'reporter',
          'reportingUnit',
          'inspectionContent',
          'inspectionBasis'
        )
      )
    },
    // 新增整改记录
    addRectificationRecord() {
      this.$refs.rectificationForm.add()
    },
    // 编辑整改记录
    editRectificationRecord(record, index) {
      this.$refs.rectificationForm.edit(record)
    },
    // 删除整改记录
    deleteRectificationRecord(index) {
      this.trainRecords.splice(index, 1)
      this.$message.success('删除成功')
    },
    // 处理整改记录表单提交
    handleRectificationSubmit(formData) {
      // 检查是否为编辑模式
      if (formData.id) {
        // 编辑模式：查找并更新现有记录
        const index = this.trainRecords.findIndex(record => record.id === formData.id)
        if (index !== -1) {
          this.trainRecords.splice(index, 1, formData)
          this.$message.success('编辑成功')
        }
      } else {
        // 新增模式：添加到记录列表
        this.trainRecords.push(formData)
        this.$message.success('新增成功')
      }
    }
  }
}
</script>
<style scoped>
/* 质量问题整改列表容器 */
.detail-container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
  margin-top: 16px;
}

/* 记录表头样式 */
.record-header {
  margin-bottom: 16px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

/* 标题样式 */
.detail-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}
</style>