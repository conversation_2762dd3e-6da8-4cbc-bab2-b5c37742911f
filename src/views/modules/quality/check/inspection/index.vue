<template>
  <div class="list-page quality-inspection-records-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'

export default {
  name: 'QualityInspectionRecordsList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {
      rightTitle: '检查列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          workArea: null,
          inspectionType: null,
          inspectionDate: null,
          inspectionResult: null,
          reportingUnit: null,
        },
        formItems: [
          { key: 'workArea', label: '检查工区', type: 'text' },
          { key: 'inspectionType', label: '检查类型', type: 'list', dictCode: 'inspection_type' },
          { key: 'inspectionDate', label: '检查日期', type: 'date' },
          { key: 'inspectionResult', label: '检查结果', type: 'list', dictCode: 'inspection_result' },
          { key: 'reportingUnit', label: '检查单位', type: 'list' },
        ],
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '检查单位',
            align: 'center',
            dataIndex: 'reportingUnit_dictText',
          },
          {
            title: '检查编号',
            align: 'center',
            dataIndex: 'code',
          },
          {
            title: '检查类型',
            align: 'center',
            dataIndex: 'inspectionType_dictText',
          },
          {
            title: '检查工区',
            align: 'center',
            dataIndex: 'workArea',
          },

          {
            title: '检查日期',
            align: 'center',
            dataIndex: 'inspectionDate',
            customRender: (text) => {
              return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
            },
          },
          {
            title: '检查结果',
            align: 'center',
            dataIndex: 'inspectionResult_dictText',
          },
          {
            title: '检查上报人',
            align: 'center',
            dataIndex: 'reporter',
          },

          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
            disabled: (record) => {
              return record.bpmStatus == '2' || record.bpmStatus == '3'
            },
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
            disabled: (record) => {
              return record.bpmStatus == '2' || record.bpmStatus == '3'
            },
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus',
            handler: this.handleAdd,
          },
        ],
      },

      url: {
        list: '/quality/qualityInspectionRecords/list',
        delete: '/quality/qualityInspectionRecords/delete',
        deleteBatch: '/quality/qualityInspectionRecords/deleteBatch',
        exportXlsUrl: '/quality/qualityInspectionRecords/exportXls',
        importExcelUrl: 'quality/qualityInspectionRecords/importExcel',
      },
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>