<template>
  <div class="check-page">
    <a-tabs type="card" default-active-key="1" @change="callback">
      <a-tab-pane key="1" tab="质量检查">
        <inspection />
      </a-tab-pane>
      <a-tab-pane key="2" tab="质量整改">
        <rectification />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>
import inspection from './inspection/index.vue'
import rectification from './rectification/index.vue'
export default {
  components: { inspection, rectification },
  data() {
    return {}
  },
  methods: {
    callback(key) {
      console.log(key)
    },
  },
}
</script>

<style lang="scss" scoped>
.check-page {
  height: 100%;
  background-color: #fff;
  ::v-deep .ant-tabs {
    height: 100%;
    .ant-tabs-content {
      height: calc(100% - 40px - 40px);
      .ant-tabs-tabpane-active {
        height: 100%;
      }
    }
  }
}
</style>
