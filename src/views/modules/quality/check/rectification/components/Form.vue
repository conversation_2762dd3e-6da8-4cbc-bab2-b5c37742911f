<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="整改单号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['code', validatorRules.code]"
                placeholder="请输入整改单号"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="施工工区" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['workArea', validatorRules.workArea]"
                placeholder="请输入施工工区"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="整改部位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-tree-select
                ref="treeSelect"
                placeholder="请选择父级节点"
                v-decorator="['rectificationPart']"
                dict="quality_catalogue_tree,name,id"
                pidField="pid"
                pidValue="0"
                hasChildField="has_child"
              >
              </j-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="整改期限" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择整改期限"
                v-decorator="['rectificationDeadline', validatorRules.rectificationDeadline]"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="整改内容" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <a-textarea
                autocomplete="off"
                v-decorator="['rectificationContent', validatorRules.rectificationContent]"
                placeholder="请输入整改内容"
                rows="4"
              ></a-textarea>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="整改建议" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <a-textarea
                autocomplete="off"
                v-decorator="['rectificationSuggestions']"
                rows="4"
                placeholder="请输入整改建议"
              ></a-textarea>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="影像资料" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload v-decorator="['mediaFiles', validatorRules.mediaFiles]" :trigger-change="true"></j-upload>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="整改措施" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <a-textarea
                v-decorator="['rectificationMeasure', validatorRules.rectificationMeasure]"
                rows="4"
                placeholder="请输入整改措施"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="整改情况" :labelCol="{ span: 3 }" :wrapperCol="{ span: 20 }">
              <a-textarea
                v-decorator="['rectificationSituation', validatorRules.rectificationSituation]"
                rows="4"
                placeholder="请输入整改情况"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="整改完成日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择整改完成日期"
                v-decorator="['rectificationCompleteDate', validatorRules.rectificationCompleteDate]"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="整改图片" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload v-decorator="['reportFiles', validatorRules.reportFiles]" :trigger-change="true"></j-upload>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="上报人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" disabled v-decorator="['reporter']" placeholder="请输入上报人"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="上报人电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" disabled v-decorator="['reporterPhone']" placeholder="请输入上报人电话"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="上报人部门" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" disabled v-decorator="['reporterDepart']" placeholder="请输入上报人部门"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="上报日期"  :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                disabled
                placeholder="请选择上报日期"
                v-decorator="['reportDate']"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="质量审核人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['reviewer', validatorRules.reviewer]"
                :trigger-change="true"
                dictCode="person_info,name,id"
                placeholder="请选择质量问题审核人"
              />
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import { getStore } from '@/utils/storage.js'
import moment from 'dayjs'

export default {
  name: 'QualityIssueRectificationsForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        code: {
          rules: [
            { required: true, message: '请输入整改单号!' },
            {
              validator: (rule, value, callback) =>
                validateDuplicateValue('quality_issue_rectifications', 'code', value, this.model.id, callback),
            },
          ],
        },
        workArea: {
          rules: [{ required: true, message: '请输入整改工区!' }],
        },
        rectificationPart: {
          rules: [{ required: true, message: '请输入整改部位!' }],
        },
        rectificationContent: {
          rules: [{ required: true, message: '请输入整改内容!' }],
        },
        mediaFiles: {
          rules: [{ required: true, message: '请输入影像资料!' }],
        },
        reviewer: {
          rules: [{ required: true, message: '请输入质量问题审核人!' }],
        },
        rectificationSituation: {
          rules: [{ required: true, message: '请输入整改情况!' }],
        },
        rectificationMeasure: {
          rules: [{ required: true, message: '请输入整改措施!' }],
        },
        rectificationDeadline: {
          rules: [{ required: true, message: '请输入整改期限!' }],
        },
        rectificationCompleteDate: {
          rules: [{ required: true, message: '请选择整改完成时间!' }],
        },
        reportFiles: {
          rules: [{ required: true, message: '请输入整改图片!' }],
        },
      },
      url: {
        add: '/quality/qualityIssueRectifications/add',
        edit: '/quality/qualityIssueRectifications/edit',
        queryById: '/quality/qualityIssueRectifications/queryById',
      },
    }
  },
  computed: {
    userInfo() {
      return getStore('pro__Login_UserinfoNew')
    },
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      if (this.model.id) {
        this.form.resetFields()
      } else {
        this.form.setFieldsValue({ reporter: this.userInfo.workUserName })
        this.form.setFieldsValue({ reporterPhone: this.userInfo.phone })
        this.form.setFieldsValue({ reporterDepart: this.userInfo.workDepartmentName })
        this.form.setFieldsValue({ reportDate: moment(String(new Date())).format('YYYY-MM-DD') })
      }
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'code',
            'workArea',
            'rectificationPart',
            'rectificationContent',
            'mediaFiles',
            'rectificationSuggestions',
            'reviewer',
            'rectificationSituation',
            'rectificationMeasure',
            'rectificationDeadline',
            'rectificationStatus',
            'reporter',
            'reporterName',
            'reporterPhone',
            'reporterDepart',
            'reportDate',
            'reportFiles',
            'rectificationCompleteDate'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok',res.result)
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'code',
          'workArea',
          'rectificationPart',
          'rectificationContent',
          'mediaFiles',
          'rectificationSuggestions',
          'reviewer',
          'rectificationSituation',
          'rectificationMeasure',
          'rectificationDeadline',
          'rectificationStatus',
          'reporter',
          'reporterName',
          'reporterPhone',
          'reporterDepart',
          'reportDate',
          'reportFiles',
          'rectificationCompleteDate'
        )
      )
    },
  },
}
</script>