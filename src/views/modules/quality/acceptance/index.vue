<template>
  <empty-page></empty-page>
</template>

<script>
import EmptyPage from '@/components/empty/EmptyPage'
export default {
  components: {
    EmptyPage
  },
  data() {
    return {};
  },
  watch: {
    '$route': {
      handler(val) {
        if (val.fullPath.indexOf('?isfresh=true') > -1) {
          // 设置为false
          this.$router.replace({ fullPath: val.fullPath.replace('?isfresh=true', '') })
          window.location.reload()
        }
      },
      immediate: true
    }
  },
  methods: {}
};
</script>

<style scoped>
</style>