<template>
  <a-modal
    :title="modalTitle"
    :visible="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirmLoading="confirmLoading"
    width="800px"
  >
    <a-form :form="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
      <a-row>
        <a-col :span="12">
          <a-form-item label="姓名">
            <a-input v-decorator="['name', validatorRules.name]" placeholder="请输入姓名" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="分包商">
            <a-input v-decorator="['subcontractor', validatorRules.subcontractor]" placeholder="请输入分包商" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="工种">
            <a-input v-decorator="['workType', validatorRules.workType]" placeholder="请输入工种" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="身份证号">
            <a-input v-decorator="['idCard', validatorRules.idCard]" placeholder="请输入身份证号" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="考试成绩">
            <a-input-number
              v-decorator="['score', validatorRules.score]"
              placeholder="请输入考试成绩"
              :min="0"
              :max="999.99"
              :precision="2"
              :step="0.01"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script>
export default {
  name: 'TrainRecordModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    record: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      confirmLoading: false,
      validatorRules: {
        name: {
          rules: [
            { required: true, message: '请输入姓名!' }
          ]
        },
        subcontractor: {
          rules: [
            { required: true, message: '请输入分包商!' }
          ]
        },
        workType: {
          rules: [
            { required: true, message: '请输入工种!' }
          ]
        },
        idCard: {
          rules: [
            { required: true, message: '请输入身份证号!' },
            {
              pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
              message: '请输入正确的身份证号!'
            }
          ]
        },
        score: {
          rules: [
            { required: true, message: '请输入考试成绩!' },
            { type: 'number', min: 0, max: 999.99, message: '成绩必须在0-999.99之间!' },
            {
              validator: (rule, value, callback) => {
                if (value !== undefined && value !== null) {
                  // 检查小数位数不超过2位
                  const decimalPart = value.toString().split('.')[1]
                  if (decimalPart && decimalPart.length > 2) {
                    callback('小数位数不能超过2位!')
                    return
                  }
                  // 检查总位数不超过5位（包括小数点前后）
                  const totalDigits = value.toString().replace('.', '').length
                  if (totalDigits > 5) {
                    callback('总位数不能超过5位!')
                    return
                  }
                }
                callback()
              }
            }
          ]
        }
      }
    }
  },
  computed: {
    modalTitle() {
      return this.isEdit ? '编辑培训记录' : '新增培训记录'
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          if (this.isEdit && this.record) {
            this.form.setFieldsValue(this.record)
          } else {
            this.form.resetFields()
          }
        })
      }
    }
  },
  methods: {
    handleOk() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.confirmLoading = true
          this.$emit('ok', values)
          this.confirmLoading = false
        }
      })
    },

    handleCancel() {
      this.form.resetFields()
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
/* 对话框样式 */
:deep(.ant-modal-body) {
  padding: 24px;
}

/* 表单项间距调整 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
}
</style>
