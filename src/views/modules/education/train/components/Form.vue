<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="8">
            <a-form-item label="培训主题" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['subject', validatorRules.subject]"
                       placeholder="请输入培训主题"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="培训方式" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['trainMethod', validatorRules.trainMethod]"
                                 :trigger-change="true" dictCode="train_method" placeholder="请选择培训方式" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="培训地点" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['location', validatorRules.location]"
                       placeholder="请输入培训地点"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date dateFormat="YYYY-MM-DD HH:mm:ss" :showTime="true" placeholder="请选择开始时间"
                      v-decorator="['startTime', validatorRules.startTime]" :trigger-change="true"
                      style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date dateFormat="YYYY-MM-DD HH:mm:ss" :showTime="true" placeholder="请选择结束时间"
                      v-decorator="['endTime', validatorRules.endTime]" :trigger-change="true" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="考试方式" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['examMethod', validatorRules.examMethod]"
                                 :trigger-change="true" dictCode="exam_method" placeholder="请选择考试方式" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="培训类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['trainType', validatorRules.trainType]"
                                 :trigger-change="true" dictCode="train_type" placeholder="请选择培训类型" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="选择试卷" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['paperId']" :trigger-change="true"
                                 dictCode="edu_exam_paper,name,id" placeholder="请选择选择试卷" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="关联资料" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['trainingMaterial']" :trigger-change="true"
                                 dictCode="edu_training_material,name,id" placeholder="请选择关联资料" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>

          <a-col :span="24">
            <a-form-item label="接受培训单位" :labelCol="labelCol24" :wrapperCol="wrapperCol24">
              <j-multi-select-tag type="list_multi" v-decorator="['unit', validatorRules.unit]" :trigger-change="true"
                                  dictCode="project_unit,name,id" placeholder="请选择接受培训单位" />
            </a-form-item>
          </a-col>
        </a-row>


        <a-row>
          <a-col :span="24">
            <a-form-item label="发送考试人员" :labelCol="labelCol24" :wrapperCol="wrapperCol24">
              <!--              <j-multi-select-tag type="list_multi" v-decorator="['personId']" :trigger-change="true"-->
              <!--                                  dictCode="person_info,name,id" placeholder="请选择发送考试人员" />-->
              <j-select-user-by-corp v-decorator="['personInfo', validatorRules.personInfo]" />

            </a-form-item>
          </a-col>
        </a-row>

        <!-- 培训附件 -->
        <a-row>
          <a-col :span="24">
            <a-form-item label="培训附件" :labelCol="labelCol24" :wrapperCol="wrapperCol24">
              <j-upload text="点击上传" :number="10" :fileType="'all'" v-model="model.file">
              </j-upload>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 培训简介独占一行 -->
        <a-row>
          <a-col :span="24">
            <a-form-item label="培训简介" :labelCol="labelCol24" :wrapperCol="wrapperCol24">
              <a-textarea autocomplete="off" v-decorator="['overview']" :rows="3"
                          placeholder="请输入培训简介"></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 培训记录标题和分隔线 -->
        <a-row>
          <a-col :span="24">
            <div class="record-title-section">
              <h3 class="record-title">培训记录</h3>
            </div>
          </a-col>
        </a-row>

        <!-- 培训记录表格独占一行 -->
        <a-row>
          <a-col :span="24">
            <div class="train-record-section">
              <div class="record-header">
                <a-button type="primary" @click="addTrainRecord">新增</a-button>
                <a-button @click="importRecords" style="margin-left: 8px;">导入</a-button>
                <a-button @click="downloadTemplate" style="margin-left: 8px;">下载模板</a-button>
              </div>
              <a-table
                :dataSource="trainRecords"
                :columns="recordColumns"
                :pagination="false"
                bordered
                size="small"
                :rowKey="(record, index) => index"
              >
                <template slot="action" slot-scope="text, record, index">
                  <a @click="editTrainRecord(record, index)" style="margin-right: 8px;">编辑</a>
                  <a-popconfirm title="确定删除吗?" @confirm="deleteTrainRecord(index)">
                    <a style="color: #ff4d4f;">删除</a>
                  </a-popconfirm>
                </template>
              </a-table>
            </div>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>

    <!-- 培训记录对话框 -->
    <TrainRecordModal
      :visible="recordModalVisible"
      :record="currentRecord"
      :isEdit="isEditMode"
      @ok="handleRecordOk"
      @cancel="handleRecordCancel"
    />
  </a-spin>

</template>

<script>

import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import TrainRecordModal from './TrainRecordModal.vue'
import JSelectUserByCorp from '@comp/jeecgbiz/JSelectUserByCorp.vue'

export default {
  name: 'EduTrainForm',
  components: {
    JSelectUserByCorp,
    TrainRecordModal
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      headers: ['姓名', '分包商', '工种', '身份证号', '考试成绩'],
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      // 12列布局
      labelCol12: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol12: {
        xs: { span: 24 },
        sm: { span: 20 }
      },
      // 24列布局
      labelCol24: {
        xs: { span: 24 },
        sm: { span: 2 }
      },
      wrapperCol24: {
        xs: { span: 24 },
        sm: { span: 22 }
      },
      confirmLoading: false,
      // 培训记录相关
      trainRecords: [],
      recordModalVisible: false,
      currentRecord: {},
      isEditMode: false,
      currentRecordIndex: -1,
      recordColumns: [
        {
          title: '序号',
          width: 60,
          align: 'center',
          customRender: (text, record, index) => index + 1
        },
        {
          title: '姓名',
          dataIndex: 'name',
          align: 'center'
        },
        {
          title: '分包商',
          dataIndex: 'subcontractor',
          align: 'center'
        },
        {
          title: '工种',
          dataIndex: 'workType',
          align: 'center'
        },
        {
          title: '身份证号',
          dataIndex: 'idCard',
          align: 'center'
        },
        {
          title: '考试成绩',
          dataIndex: 'score',
          align: 'center'
        },
        {
          title: '操作',
          width: 120,
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      validatorRules: {
        subject: {
          rules: [
            { required: true, message: '请输入培训主题!' }
          ]
        },
        unit: {
          rules: [
            { required: true, message: '请输入接受培训单位!' }
          ]
        },
        trainMethod: {
          rules: [
            { required: true, message: '请输入培训方式!' }
          ]
        },
        location: {
          rules: [
            { required: true, message: '请输入培训地点!' }
          ]
        },
        startTime: {
          rules: [
            { required: true, message: '请输入开始时间!' }
          ]
        },
        endTime: {
          rules: [
            { required: true, message: '请输入结束时间!' }
          ]
        },
        examMethod: {
          rules: [
            { required: true, message: '请输入考试方式!' }
          ]
        },
        trainType: {
          rules: [
            { required: true, message: '请输入培训类型!' }
          ]
        }
      },
      url: {
        add: '/education/eduTrain/add',
        edit: '/education/eduTrain/edit',
        queryById: '/education/eduTrain/queryById'
      }
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true

      // 处理培训记录数据
      if (record.details && Array.isArray(record.details)) {
        this.trainRecords = [...record.details]
      } else {
        this.trainRecords = []
      }


      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'subject', 'unit', 'trainMethod', 'location', 'startTime', 'endTime', 'examMethod', 'trainType', 'paperId', 'trainingMaterial', 'personInfo', 'file', 'overview'))
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          console.log('this.trainRecords', this.trainRecords)
          let formData = Object.assign(this.model, values, {
            details: this.trainRecords
          })
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }

      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'subject', 'unit', 'trainMethod', 'location', 'startTime', 'endTime', 'examMethod', 'trainType', 'paperId', 'trainingMaterial', 'personInfo', 'file', 'overview'))
    },

    // 培训记录管理方法
    addTrainRecord() {
      this.isEditMode = false
      this.currentRecordIndex = -1
      this.currentRecord = {}
      this.recordModalVisible = true
    },

    editTrainRecord(record, index) {
      this.isEditMode = true
      this.currentRecordIndex = index
      this.currentRecord = { ...record }
      this.recordModalVisible = true
    },

    deleteTrainRecord(index) {
      this.trainRecords.splice(index, 1)
      this.$message.success('删除成功')
    },

    handleRecordOk(values) {
      if (this.currentRecordIndex === -1) {
        // 新增
        this.trainRecords.push({
          ...values
        })
        this.$message.success('新增成功')
      } else {
        // 编辑
        this.trainRecords.splice(this.currentRecordIndex, 1, {
          ...this.trainRecords[this.currentRecordIndex],
          ...values
        })
        this.$message.success('编辑成功')
      }
      this.recordModalVisible = false
    },

    handleRecordCancel() {
      this.recordModalVisible = false
    },

    importRecords() {
      // 创建文件输入元素
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.xlsx,.xls'
      input.onchange = (event) => {
        const file = event.target.files[0]
        if (!file) return
        // 显示加载提示
        const loading = this.$message.loading('正在导入数据...', 0)
        // 导入XLSX库并处理文件
        import('xlsx').then(XLSX => {
          const reader = new FileReader()
          reader.onload = (e) => {
            try {
              // 读取Excel文件
              const data = new Uint8Array(e.target.result)
              const workbook = XLSX.read(data, { type: 'array' })

              // 获取第一个工作表
              const firstSheetName = workbook.SheetNames[0]
              const worksheet = workbook.Sheets[firstSheetName]

              // 将工作表转换为JSON数据
              const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

              if (jsonData.length < 2) {
                loading()
                this.$message.error('Excel文件中没有数据')
                return
              }

              // 获取表头和数据
              const headers = jsonData[0]
              const dataRows = jsonData.slice(1)

              // 验证表头格式
              const expectedHeaders = this.headers
              const isValidFormat = expectedHeaders.every((header, index) =>
                headers[index] === header
              )

              if (!isValidFormat) {
                loading()
                this.$message.error('Excel文件格式不正确，请使用标准模板')
                return
              }

              // 处理导入的数据
              this.processImportedData(dataRows, loading)

            } catch (error) {
              loading()
              console.error('导入失败:', error)
              this.$message.error('文件解析失败，请检查文件格式')
            }
          }
          reader.readAsArrayBuffer(file)
        }).catch(error => {
          loading()
          console.error('导入失败:', error)
          this.$message.error('导入功能加载失败，请重试')
        })
      }

      // 触发文件选择
      input.click()
    },

    processImportedData(dataRows, loading) {
      try {
        let successCount = 0
        let updateCount = 0
        let errorCount = 0
        const errors = []

        dataRows.forEach((row, index) => {
          // 跳过空行
          if (!row || row.every(cell => !cell && cell !== 0)) {
            return
          }

          const [name,subcontractor,workType,idCard,score] = row

          // 验证必填字段
          if (!name || !idCard) {
            errorCount++
            errors.push(`第${index + 2}行：姓名和身份证号不能为空`)
            return
          }

          // 验证身份证号格式
          if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(idCard)) {
            errorCount++
            errors.push(`第${index + 2}行：身份证号格式不正确`)
            return
          }

          // 创建记录对象
          const record = {
            name: String(name).trim(),
            score: score ? Number(score) : null,
            workType: workType ? String(workType).trim() : '',
            idCard: String(idCard).trim(),
            examScore: examScore ? Number(examScore) : null
          }

          // 检查是否已存在相同身份证号的记录
          const existingIndex = this.dataSource.findIndex(item => item.idCard === record.idCard)

          if (existingIndex !== -1) {
            // 覆盖现有记录
            this.dataSource.splice(existingIndex, 1, {
              ...this.dataSource[existingIndex],
              ...record,
              key: this.dataSource[existingIndex].key // 保持原有的key
            })
            updateCount++
          } else {
            // 添加新记录
            this.dataSource.push({
              ...record,
              key: Date.now() + Math.random() // 生成唯一key
            })
            successCount++
          }
        })

        loading()

        // 显示导入结果
        if (errorCount > 0) {
          this.$modal.error({
            title: '导入完成（部分失败）',
            content: `成功导入：${successCount}条，更新：${updateCount}条，失败：${errorCount}条\n\n错误详情：\n${errors.join('\n')}`,
            width: 600
          })
        } else {
          this.$message.success(`导入成功！新增：${successCount}条，更新：${updateCount}条`)
        }

        // 更新表格显示
        this.$forceUpdate()

      } catch (error) {
        loading()
        console.error('处理导入数据失败:', error)
        this.$message.error('数据处理失败，请重试')
      }
    },
    downloadTemplate() {
      // 导入XLSX库
      import('xlsx').then(XLSX => {
        // 定义Excel表头
        // 创建工作表数据，第一行为表头
        const wsData = [this.headers]
        // wsData.push([ '张三',"分包商",'电工','123456789012345678', '60'])
        const ws = XLSX.utils.aoa_to_sheet(wsData)
        // 设置列宽
        const colWidths = [
          { wch: 12 }, // 姓名
          { wch: 20 }, // 分包商
          { wch: 12 }, // 工种
          { wch: 20 }, // 身份证号
          { wch: 12 }  // 考试成绩
        ]
        ws['!cols'] = colWidths
        // 创建工作簿
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, '培训记录模板')
        // 生成文件名（包含当前日期）
        const now = new Date()
        const dateStr = now.getFullYear() +
          String(now.getMonth() + 1).padStart(2, '0') +
          String(now.getDate()).padStart(2, '0')
        const fileName = `培训记录导入模板_${dateStr}.xlsx`
        // 下载文件
        XLSX.writeFile(wb, fileName)
        this.$message.success('模板下载成功')
      }).catch(error => {
        console.error('下载模板失败:', error)
        this.$message.error('下载模板失败，请重试')
      })
    }
  }
}
</script>

<style scoped>
/* 培训记录标题区域 */
.record-title-section {
  margin-top: 24px;
  margin-bottom: 0;
}

.record-title {
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

/* 分隔线样式 */
:deep(.ant-divider) {
  margin: 12px 0 16px 0;
  border-color: #d9d9d9;
}

/* 培训记录表格区域 */
.train-record-section {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.record-header {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

/* 表格样式调整 */
:deep(.ant-table-thead > tr > th) {
  background: #f0f0f0;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px 16px;
}

/* 对话框样式 */
:deep(.ant-modal-body) {
  padding: 24px;
}

/* 表单项间距调整 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
}
</style>