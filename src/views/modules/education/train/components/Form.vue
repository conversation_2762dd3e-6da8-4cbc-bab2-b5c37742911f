<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="8">
            <a-form-item label="培训主题" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['subject', validatorRules.subject]" placeholder="请输入培训主题"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="接受培训单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-multi-select-tag type="list_multi" v-decorator="['unit', validatorRules.unit]" :trigger-change="true" dictCode="project_unit,name,id" placeholder="请选择接受培训单位" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="培训方式" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['trainMethod', validatorRules.trainMethod]" :trigger-change="true" dictCode="train_method" placeholder="请选择培训方式" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="培训地点" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['location', validatorRules.location]" placeholder="请输入培训地点"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date dateFormat="YYYY-MM-DD HH:mm:ss" :showTime="true" placeholder="请选择开始时间" v-decorator="['startTime', validatorRules.startTime]" :trigger-change="true" style="width: 100%"/>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date dateFormat="YYYY-MM-DD HH:mm:ss" :showTime="true" placeholder="请选择结束时间" v-decorator="['endTime', validatorRules.endTime]" :trigger-change="true" style="width: 100%"/>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="考试方式" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['examMethod', validatorRules.examMethod]" :trigger-change="true" dictCode="exam_method" placeholder="请选择考试方式" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="培训类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['trainType', validatorRules.trainType]" :trigger-change="true" dictCode="train_type" placeholder="请选择培训类型" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="选择试卷" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['paperId']" :trigger-change="true" dictCode="edu_exam_paper,name,id" placeholder="请选择选择试卷" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="关联资料" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['trainingMaterial']" :trigger-change="true" dictCode="edu_training_material,name,id" placeholder="请选择关联资料" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="发送考试人员" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-multi-select-tag type="list_multi" v-decorator="['personId']" :trigger-change="true" dictCode="person_info,name,id" placeholder="请选择发送考试人员" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 培训附件 -->
        <a-row>
          <a-col :span="24">
            <a-form-item label="培训附件" :labelCol="labelCol24" :wrapperCol="wrapperCol24">
              <j-upload text="点击上传" :number="10" :fileType="'all'" v-model="model.file">
              </j-upload>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 培训简介独占一行 -->
        <a-row>
          <a-col :span="24">
            <a-form-item label="培训简介" :labelCol="labelCol24" :wrapperCol="wrapperCol24">
              <a-textarea autocomplete="off" v-decorator="['overview']" :rows="3" placeholder="请输入培训简介"></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 培训记录标题和分隔线 -->
        <a-row>
          <a-col :span="24">
            <div class="record-title-section">
              <h3 class="record-title">培训记录</h3>
              <a-divider />
            </div>
          </a-col>
        </a-row>

        <!-- 培训记录表格独占一行 -->
        <a-row>
          <a-col :span="24">
            <div class="train-record-section">
              <div class="record-header">
                <a-button type="primary" icon="plus" @click="addTrainRecord">新增</a-button>
                <a-button icon="import" @click="importRecords" style="margin-left: 8px;">导入</a-button>
              </div>
              <a-table
                :dataSource="trainRecords"
                :columns="recordColumns"
                :pagination="false"
                bordered
                size="small"
                :rowKey="(record, index) => index"
              >
                <template slot="action" slot-scope="text, record, index">
                  <a @click="editTrainRecord(record, index)" style="margin-right: 8px;">编辑</a>
                  <a-popconfirm title="确定删除吗?" @confirm="deleteTrainRecord(index)">
                    <a style="color: #ff4d4f;">删除</a>
                  </a-popconfirm>
                </template>
              </a-table>
            </div>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>

    <!-- 培训记录对话框 -->
    <TrainRecordModal
      :visible="recordModalVisible"
      :record="currentRecord"
      :isEdit="isEditMode"
      @ok="handleRecordOk"
      @cancel="handleRecordCancel"
    />
  </a-spin>

</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'
  import TrainRecordModal from './TrainRecordModal.vue'

  export default {
    name: 'EduTrainForm',
    components: {
      TrainRecordModal
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        // 12列布局
        labelCol12: {
          xs: { span: 24 },
          sm: { span: 4 },
        },
        wrapperCol12: {
          xs: { span: 24 },
          sm: { span: 20 },
        },
        // 24列布局
        labelCol24: {
          xs: { span: 24 },
          sm: { span: 2 },
        },
        wrapperCol24: {
          xs: { span: 24 },
          sm: { span: 22 },
        },
        confirmLoading: false,
        // 培训记录相关
        trainRecords: [],
        recordModalVisible: false,
        currentRecord: {},
        isEditMode: false,
        currentRecordIndex: -1,
        recordColumns: [
          {
            title: '序号',
            width: 60,
            align: 'center',
            customRender: (text, record, index) => index + 1
          },
          {
            title: '姓名',
            dataIndex: 'name',
            align: 'center'
          },
          {
            title: '分包商',
            dataIndex: 'subcontractor',
            align: 'center'
          },
          {
            title: '工种',
            dataIndex: 'workType',
            align: 'center'
          },
          {
            title: '身份证号',
            dataIndex: 'idCard',
            align: 'center'
          },
          {
            title: '考试成绩',
            dataIndex: 'score',
            align: 'center'
          },
          {
            title: '操作',
            width: 120,
            align: 'center',
            scopedSlots: { customRender: 'action' }
          }
        ],
        validatorRules: {
          subject: {
            rules: [
              { required: true, message: '请输入培训主题!'},
            ]
          },
          unit: {
            rules: [
              { required: true, message: '请输入接受培训单位!'},
            ]
          },
          trainMethod: {
            rules: [
              { required: true, message: '请输入培训方式!'},
            ]
          },
          location: {
            rules: [
              { required: true, message: '请输入培训地点!'},
            ]
          },
          startTime: {
            rules: [
              { required: true, message: '请输入开始时间!'},
            ]
          },
          endTime: {
            rules: [
              { required: true, message: '请输入结束时间!'},
            ]
          },
          examMethod: {
            rules: [
              { required: true, message: '请输入考试方式!'},
            ]
          },
          trainType: {
            rules: [
              { required: true, message: '请输入培训类型!'},
            ]
          },
        },
        url: {
          add: "/education/eduTrain/add",
          edit: "/education/eduTrain/edit",
          queryById: "/education/eduTrain/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      }
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;

        // 处理培训记录数据
        if (record.children && Array.isArray(record.children)) {
          this.trainRecords = [...record.children];
        } else {
          this.trainRecords = [];
        }

        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'subject','unit','trainMethod','location','startTime','endTime','examMethod','trainType','paperId','trainingMaterial','personId','file','overview'))
        })
      },
      //渲染流程表单数据
      showFlowData(){
        if(this.formBpm === true){
          let params = {id:this.formData.dataId};
          getAction(this.url.queryById,params).then((res)=>{
            if(res.success){
              this.edit (res.result);
            }
          });
        }
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            let formData = Object.assign(this.model, values, {
              children: this.trainRecords
            });
            console.log("表单提交数据",formData)
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'subject','unit','trainMethod','location','startTime','endTime','examMethod','trainType','paperId','trainingMaterial','personId','file','overview'))
      },

      // 培训记录管理方法
      addTrainRecord() {
        this.isEditMode = false;
        this.currentRecordIndex = -1;
        this.currentRecord = {};
        this.recordModalVisible = true;
      },

      editTrainRecord(record, index) {
        this.isEditMode = true;
        this.currentRecordIndex = index;
        this.currentRecord = { ...record };
        this.recordModalVisible = true;
      },

      deleteTrainRecord(index) {
        this.trainRecords.splice(index, 1);
        this.$message.success('删除成功');
      },

      handleRecordOk(values) {
        if (this.currentRecordIndex === -1) {
          // 新增
          this.trainRecords.push({
            ...values
          });
          this.$message.success('新增成功');
        } else {
          // 编辑
          this.trainRecords.splice(this.currentRecordIndex, 1, {
            ...this.trainRecords[this.currentRecordIndex],
            ...values
          });
          this.$message.success('编辑成功');
        }
        this.recordModalVisible = false;
      },

      handleRecordCancel() {
        this.recordModalVisible = false;
      },

      importRecords() {
        this.$message.info('导入功能待实现');
      },
    }
  }
</script>

<style scoped>
/* 培训记录标题区域 */
.record-title-section {
  margin-top: 24px;
  margin-bottom: 0;
}

.record-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

/* 分隔线样式 */
:deep(.ant-divider) {
  margin: 12px 0 16px 0;
  border-color: #d9d9d9;
}

/* 培训记录表格区域 */
.train-record-section {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.record-header {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

/* 表格样式调整 */
:deep(.ant-table-thead > tr > th) {
  background: #f0f0f0;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px 16px;
}

/* 对话框样式 */
:deep(.ant-modal-body) {
  padding: 24px;
}

/* 表单项间距调整 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
}
</style>