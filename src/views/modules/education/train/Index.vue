<template>
  <div class="list-page edu-train-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
      <template slot="file-link" slot-scope="record">
        <a v-if="record.file" :href="record.file" target="_blank" download>
          <a-icon type="paperclip" />
          附件
        </a>
        <span v-else style="color: #ccc;">无附件</span>
      </template>
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { postAction } from '@/api/manage'

export default {
  name: 'EduTrainList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal
  },
  data() {
    return {
      rightTitle: '培训管理列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          'subject': null,
          'unit': null,
          'trainMethod': null,
          'startTime': null,
          'trainType': null
        },
        formItems: [
          { key: 'subject', label: '培训主题', type: 'text' },
          { key: 'unit', label: '接受培训单位', type: 'list', dictCode: 'project_unit,name,id' },
          { key: 'trainMethod', label: '培训方式', type: 'list', dictCode: 'train_method' },
          { key: 'startTime', label: '开始时间', type: 'datetime_range', keyParams: ['startTime', 'endTime'] },
          { key: 'trainType', label: '培训类型', type: 'list', dictCode: 'train_type' }
        ]
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            }
          },
          {
            title: '培训主题',
            align: 'center',
            dataIndex: 'subject'
          },
          {
            title: '培训类型',
            align: 'center',
            dataIndex: 'trainType_dictText'
          },
          {
            title: '接受培训单位',
            align: 'center',
            dataIndex: 'unit_dictText'
          },
          {
            title: '培训方式',
            align: 'center',
            dataIndex: 'trainMethod_dictText'
          },
          {
            title: '开始时间',
            align: 'center',
            dataIndex: 'startTime'
          },
          {
            title: '结束时间',
            align: 'center',
            dataIndex: 'endTime'
          },
          {
            title: '培训地点',
            align: 'center',
            dataIndex: 'location'
          },
          {
            title: '考试方式',
            align: 'center',
            dataIndex: 'examMethod_dictText'
          },

          {
            title: '培训附件',
            align: 'center',
            dataIndex: 'file',
            scopedSlots: { customRender: 'file-link' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons: [
          {
            text: '发送试卷',
            handler: this.handleSendPaper,
            disabled: (record) => {
              return record.examMethod_dictText !== '线上考试'
            }
          },
          {
            text: '查看',
            handler: this.handleDetail
          },
          {
            text: '编辑',
            handler: this.handleEdit
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete
          }
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd
          }
        ]
      },
      url: {
        list: '/education/eduTrain/list',
        delete: '/education/eduTrain/delete',
        deleteBatch: '/education/eduTrain/deleteBatch',
        exportXlsUrl: '/education/eduTrain/exportXls',
        importExcelUrl: 'education/eduTrain/importExcel',
        sendPaper: 'education/eduTrain/sendPaper'
      }
    }
  },
  created() {
  },
  computed: {},
  watch: {},
  methods: {
    handleSendPaper(record) {
      postAction(this.url.sendPaper, record).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.loadData()
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
    onTableChange(params) {
      this.loadData(params)
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>