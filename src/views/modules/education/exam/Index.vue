<template>
  <div class="list-page edu-train-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
      @close="searchQuery"
    >
      <template #exam_status="{ record }">
        <a-tag v-if="record.status === 'PENDING'" color="orange">未开始</a-tag>
        <a-tag v-else-if="record.status === 'INPROGRESS'" color="green">进行中</a-tag>
        <a-tag v-else-if="record.status === 'FINISHED'" color="gray">已结束</a-tag>
      </template>
    </table-layout>
    <!--    <modal ref="modalForm" @ok="modalFormOk"></modal>-->
    <QuestionDetailDialog
      :visible="showPaper"
      :showAnalysis="showAnalysis"
      :examId="examId"
      :question="paper"
      :editable="editable"
      @update:visible="closePaperDetail"
      @submit="searchQuery"
      ref="paperDetailRef" />

  </div>
</template>

<script>
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import QuestionDetailDialog from '@views/modules/education/examPaper/components/paperDetaiDialog.vue'
import { postAction } from '@api/manage'

export default {
  name: 'CustomTable',
  components: {
    QuestionDetailDialog
  },
  mixins: [JeecgTreeListMixin],
  data() {
    return {
      showAnalysis: false,
      examId: '',
      editable: false,
      showPaper: false,
      paper: {},
      rightTitle: '考试管理',
      isShow: false,
      viewId: '',
      examDialogVisible: false,
      examRecords: [],
      examResultDialogVisible: false,
      currentBusinessId: '',
      paperTitle: '',

      searchProps: {
        formModel: {
          'name': null,
          'startTimeRange': null,
          'status': null
        },
        formItems: [
          { key: 'name', label: '考试名称', type: 'text' },
          { key: 'startTimeRange', label: '开始时间', type: 'datetime_range', keyParams: ['startTime_', 'endTime_'] },
          { key: 'status', label: '考试状态', type: 'list', dictCode: 'exam_status' }
        ]
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            }
          },
          {
            title: '考试名称',
            align: 'left',
            // dataIndex: 'paperId_dictText';
            dataIndex: 'name'
          },
          {
            title: '考试时间',
            align: 'left',
            dataIndex: 'startTime',
            width: 360,
            customRender: (t, r, index) => {
              if (!r.startTime && !r.endTime) {
                return ''
              }
              if (!r.endTime) {
                return r.startTime + '~ ?'
              }
              return r.startTime + '~' + r.endTime
            }
          },
          {
            title: '考试时长(分)',
            align: 'center',
            width: 120,
            customRender: (t, r, index) => {
              return r.paper.duration
            }

          },
          {
            title: '考试总分',
            align: 'center',
            width: 120,
            customRender: (t, r, index) => {
              const score = r.paper.score
              if (score == null) {
                return ''
              }
              // 转换为数字并格式化
              const numScore = parseFloat(score)
              // 如果小数部分为0，显示整数；否则显示一位小数
              return numScore % 1 === 0 ? numScore.toString() : numScore.toFixed(1)
            }

          },
          {
            title: '及格分数',
            align: 'center',
            width: 120,
            customRender: (t, r, index) => {
              return r.paper.passScore
            }
          },
          {
            title: '考试成绩',
            align: 'center',
            width: 120,
            customRender: (t, r, index) => {
              const score = r.score
              if (score == null) {
                return ''
              }
              // 转换为数字并格式化
              const numScore = parseFloat(score)
              // 如果小数部分为0，显示整数；否则显示一位小数
              return numScore % 1 === 0 ? numScore.toString() : numScore.toFixed(1)
            }
          },
          {
            title: '考试状态',
            align: 'center',
            dataIndex: 'status_dictText',
            width: 120,
            scopedSlots: { customRender: 'exam_status' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
            disabled: (record) => {
              return record.status !== 'FINISHED'
            }
          },
          {
            text: '答题',
            handler: this.handAnswer,
            disabled: (record) => {
              return record.status === 'FINISHED'
            }
          }
        ],
        headerButtons: []
      },
      url: {
        list: '/education/eduExam/list',
        delete: '/education/eduExam/delete',
        deleteBatch: '/education/eduExam/deleteBatch',
        exportXlsUrl: '/education/eduExam/exportXls',
        importExcelUrl: 'education/eduExam/importExcel',
        startExam: 'education/eduExam/startExam',
        getExamDetail: 'education/eduExam/getExamDetail'
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.isShow = true
    })
  },
  beforeDestroy() {
  },
  methods: {
    handleDetail(record) {
      postAction(this.url.getExamDetail, record).then(res => {
        if (res.success) {
          this.editable = false
          this.showAnalysis = true
          let questionAnswer = {}
          res.result.details.forEach(detail => {
            questionAnswer[detail.questionId] = detail.answer
          })
          this.paper = res.result.paper
          if (this.paper && this.paper.paperRules) {
            this.paper.paperRules.forEach((paperRule) => {
              if (paperRule.details) {
                paperRule.details.forEach((detail) => { //将答案插入每一题中
                  detail.selectedAnswers = questionAnswer[detail.questionId]
                  if (detail.type === 'MULTIPLE') {
                    detail.selectedAnswers = detail.selectedAnswers ? detail.selectedAnswers.split('') : []
                  }
                })
              }
            })
          }
          this.showPaper = true
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handAnswer(record) {
      postAction(this.url.startExam, record).then(res => {
        if (res.success) {
          this.editable = true
          this.showAnalysis = false
          this.paper = record.paper
          this.examId = record.id
          this.showPaper = true
          this.loadData()
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    // 关闭试卷详情弹窗
    closePaperDetail() {
      this.showPaper = false
      this.editable = false
      this.paper = {}
    },
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
    onTableChange(params) {
      this.loadData(params)
    }
  }
}
</script>
<style scoped lang="scss">


</style>

