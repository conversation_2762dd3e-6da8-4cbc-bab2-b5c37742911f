<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="8">
            <a-form-item label="试卷名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['name', validatorRules.name]"
                       placeholder="请输入试卷名称"></a-input>
            </a-form-item>
          </a-col>
          <!--          <a-col :span="8">-->
          <!--            <a-form-item label="关联培训任务" :labelCol="labelCol" :wrapperCol="wrapperCol">-->
          <!--              <j-dict-select-tag type="list" v-decorator="['train']" :trigger-change="true" dictCode="edu_train,name,id"-->
          <!--                                 placeholder="请选择关联培训任务" />-->
          <!--            </a-form-item>-->
          <!--          </a-col>-->
          <a-col :span="8">
            <a-form-item label="考试时限" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number autocomplete="off" v-decorator="['duration', validatorRules.duration]"
                              placeholder="请输入考试时限" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="及格分数" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number autocomplete="off" v-decorator="['passScore', validatorRules.passScore]"
                              placeholder="请输入及格分数" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="题目标签" :labelCol="labelCol24" :wrapperCol="wrapperCol24">
              <j-multi-select-tag type="list_multi" v-decorator="['tags', validatorRules.tags]" :trigger-change="true"
                                  dictCode="question_tag" placeholder="请选择题目标签" />
            </a-form-item>
          </a-col>

        </a-row>
      </a-form>
    </j-form-container>

    <!-- 试卷内容标题和分隔线 -->
    <div class="paper-content-section">
      <div class="content-title-section">
        <h3 class="content-title">试卷内容（{{ totalScore }}分）</h3>
        <a-divider />
      </div>

      <!-- 试卷内容表格 -->
      <div class="content-table-section">
        <a-table
          :dataSource="rules"
          :columns="contentColumns"
          :pagination="false"
          bordered
          size="small"
          :rowKey="(record, index) => index"
        >
          <template slot="questionCount" slot-scope="text, record, index">
            <a-input-number
              v-model="record.count"
              :disabled="formDisabled"
              :min="0"
              :max="999"
              @change="calculateSubtotal(index)"
              style="width: 100%"
            />
          </template>
          <template slot="questionScore" slot-scope="text, record, index">
            <a-input-number
              :disabled="formDisabled"
              v-model="record.score"
              :min="0"
              :max="999"
              @change="calculateSubtotal(index)"
              style="width: 100%"
            />
          </template>
          <template slot="subtotal" slot-scope="text, record">
            <span>{{ record.subtotal || 0 }}</span>
          </template>
        </a-table>
      </div>

      <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center; margin-top: 20px;">
        <a-button @click="submitForm">提 交</a-button>
      </a-col>
    </div>
  </a-spin>
</template>

<script>

import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import { getDictItems } from '@/components/dict/JDictSelectUtil'

export default {
  name: 'EduExamPaperForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      labelCol24: {
        xs: { span: 24 },
        sm: { span: 2 }
      },
      wrapperCol24: {
        xs: { span: 24 },
        sm: { span: 21 }
      },

      confirmLoading: false,
      // 试卷内容数据
      rules: [], //试卷生成规则
      // 表格列配置
      contentColumns: [
        {
          title: '#',
          width: 60,
          align: 'center',
          customRender: (text, record, index) => index + 1
        },
        {
          title: '题目类型',
          dataIndex: 'questionTypeText',
          align: 'center',
          width: 120
        },
        {
          title: '题目数量',
          align: 'center',
          width: 150,
          scopedSlots: { customRender: 'questionCount' }
        },
        {
          title: '题目分值',
          align: 'center',
          width: 150,
          scopedSlots: { customRender: 'questionScore' }
        },
        {
          title: '小计',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'subtotal' }
        }
      ],
      validatorRules: {
        name: {
          rules: [
            { required: true, message: '请输入试卷名称!' },
            {
              validator: (rule, value, callback) =>
                validateDuplicateValue('edu_exam_paper', 'name', value, this.model.id, callback)
            }
          ]
        },
        startTime: {
          rules: [
            { required: true, message: '请输入开始时间!' }
          ]
        },
        endTime: {
          rules: [
            { required: true, message: '请输入截止时间!' }
          ]
        },
        duration: {
          rules: [
            { required: true, message: '请输入考试时限!' }
          ]
        },
        passScore: {
          rules: [
            { required: true, message: '请输入及格分数!' }
          ]
        }
      },
      url: {
        add: '/education/eduExamPaper/add',
        edit: '/education/eduExamPaper/edit',
        queryById: '/education/eduExamPaper/queryById'
      }
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
    // 计算总分
    totalScore() {
      return this.rules.reduce((total, item) => {
        return total + (item.subtotal || 0)
      }, 0)
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
    // 初始化试卷内容
    this.initPaperContent()
  },
  methods: {
    async initPaperContent() {
      try {
        // 从字典获取题目类型数据
        const dictItems = await getDictItems('question_type')
        console.log('dictItems', dictItems)
        if (dictItems && dictItems.length > 0) {
          // 根据字典数据初始化试卷内容
          this.rules = dictItems.map(item => ({
            type: item.value,           // 字典的value对应type
            questionTypeText: item.text || item.label, // 字典的text/label对应显示的类型
            count: 0,                   // 默认题目数量为0
            score: 0,                   // 默认题目分值为0
            subtotal: 0                 // 默认小计为0
          }))
        } else {
          // 如果字典没有数据，使用默认数据
          this.rules = [
            {
              type: 'single_choice',
              questionTypeText: '单选',
              count: 0,
              score: 0,
              subtotal: 0
            },
            {
              type: 'multiple_choice',
              questionTypeText: '多选',
              count: 0,
              score: 0,
              subtotal: 0
            },
            {
              type: 'judge',
              questionTypeText: '判断',
              count: 0,
              score: 0,
              subtotal: 0
            }
          ]
        }
      } catch (error) {
        console.error('初始化试卷内容失败:', error)
        // 出错时使用默认数据
        this.rules = [
          {
            type: 'single_choice',
            questionTypeText: '单选',
            count: 0,
            score: 0,
            subtotal: 0
          },
          {
            type: 'multiple_choice',
            questionTypeText: '多选',
            count: 0,
            score: 0,
            subtotal: 0
          },
          {
            type: 'judge',
            questionTypeText: '判断',
            count: 0,
            score: 0,
            subtotal: 0
          }
        ]
      }
    },
    add() {
      this.edit({})
    },
    edit(record) {
      record = JSON.parse(JSON.stringify(record))
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      // 处理试卷内容数据
      if (record.rules) {
        record.rules = JSON.parse(record.rules)//rules 是 json文本
        console.log("record.rules",record.rules)
      }
      this.$nextTick(() => {
          if (record.rules && Array.isArray(record.rules)) {
            this.rules = [...record.rules]
          } else {
            this.initPaperContent()
          }
          this.form.setFieldsValue(pick(this.model, 'name', 'startTime', 'endTime', 'train', 'tags', 'duration', 'passScore'))
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values, {
            score: this.totalScore,
            paperRules : this.rules
          })
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }

      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'name', 'startTime', 'endTime', 'train', 'tags', 'duration', 'passScore'))
    },

    // 计算小计
    calculateSubtotal(index) {
      const item = this.rules[index]
      const count = item.count || 0
      const score = item.score || 0
      item.subtotal = count * score

      // 触发响应式更新
      this.$set(this.rules, index, { ...item })
    }
  }

  // 初始化试卷内容

}
</script>

<style scoped>
/* 试卷内容区域 */
.paper-content-section {
  margin-top: 24px;
  padding: 0 24px;
}

/* 试卷内容标题区域 */
.content-title-section {
  margin-bottom: 0;
}

.content-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

/* 分隔线样式 */
:deep(.ant-divider) {
  margin: 12px 0 16px 0;
  border-color: #d9d9d9;
}

/* 试卷内容表格区域 */
.content-table-section {
  background: #ffffff;
}

:deep(.ant-table-column-title) {
  color: white;
}

:deep(.ant-table-thead) {
  background-color: rgb(6, 111, 236);
}

/* 表格样式调整 */
:deep(.ant-table-thead > tr > th) {
  background: #f0f0f0;
  font-weight: 600;
  text-align: center;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px 16px;
  text-align: center;
}

/* 输入框样式 */
:deep(.ant-input-number) {
  width: 100%;
}

/* 小计列样式 */
:deep(.ant-table-tbody tr td:last-child) {
  font-weight: 600;
  color: #1890ff;
}


</style>