<template>
  <a-modal
    :visible="visibleInner"
    title="试题详情"
    :width="'100%'"
    :style="{ top: '0px' }"
    :bodyStyle="{ height: '100vh', padding: '24px 32px 10px 32px', background: '#f8fafd' }"
    @cancel="handleClose"
    :footer="null"
    class="question-detail-dialog"
  >
    <div v-if="!question || !question.paperRules || !question.paperRules.length" class="empty-state">
      <div class="empty-text">暂无数据</div>
    </div>
    <div v-else class="exam-dialog-layout">
      <!-- 左侧题型分块 -->
      <div class="exam-sidebar">
        <div v-for="(paperRule, gIdx) in question.paperRules" :key="paperRule.type" class="sidebar-section">
          <div class="section-title">
            {{ paperRule.questionTypeText }}（共{{ paperRule.details.length }}题，合计{{
              paperRule.details.length * (paperRule.score || 0)
            }}分）
          </div>
          <div class="question-grid">
            <div
              v-for="(q, qIdx) in paperRule.details"
              :key="q.id || qIdx"
              class="question-item"
              @click="scrollToQuestion(gIdx, qIdx)"
            >
              {{ getQuestionIndex(gIdx, qIdx) }}
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧内容 -->
      <div class="exam-content">
        <div class="exam-header">
          <div class="exam-title">{{ question.name }}</div>
        </div>
        <div v-for="(paperRule, gIdx) in question.paperRules" :key="paperRule.id">
          <div class="type-title-main">
            {{ getChineseNumber(gIdx + 1) }}、
            {{ paperRule.questionTypeText }} （共{{
              paperRule.details.length
            }}小题，每小题{{ paperRule.score || '-' }}分，共{{
              paperRule.details.length * (paperRule.score || 0)
            }}分）
          </div>

          <div
            v-for="(detail, qIdx) in paperRule.details"
            :key="detail.id || qIdx"
            class="question-block"
            :ref="'question' + getQuestionIndex(gIdx, qIdx)"
          >
            <!-- 题目头部 -->
            <div class="question-header">
              <div class="question-index">第{{ getQuestionIndex(gIdx, qIdx) }}题</div>
              <div class="question-actions">
                <!-- 这里可插入收藏、错题、解析等按钮slot -->
                <slot name="actions" :question="detail"></slot>
              </div>
            </div>
            <!-- 题干 -->
            <div class="question-content">{{ detail.content }}</div>
            <!-- 选项 -->
            <div v-if="getQuestionOptions(detail).length" class="options-list">
              <!-- 单选题 -->
              <a-radio-group v-if="isSingleChoice(paperRule.type)" v-model="detail.selectedAnswers">
                <a-radio
                  v-for="(option, optIndex) in getQuestionOptions(detail)"
                  :key="optIndex"
                  :value="getOptionLabel(optIndex)"
                  class="option-radio"
                  :disabled="!editable"
                >
                  <span class="option-label">{{ getOptionLabel(optIndex) }}.</span>
                  <span class="option-content">{{ option }}</span>
                </a-radio>
              </a-radio-group>
              <!-- 多选题 -->
              <a-checkbox-group v-else-if="isMultiChoice(paperRule.type)" v-model="detail.selectedAnswers">
                <a-checkbox
                  v-for="(option, optIndex) in getQuestionOptions(detail)"
                  :key="optIndex"
                  :value="getOptionLabel(optIndex)"
                  class="option-checkbox"
                  :disabled="!editable"
                >
                  <span class="option-label">{{ getOptionLabel(optIndex) }}.</span>
                  <span class="option-content">{{ option }}</span>
                </a-checkbox>
              </a-checkbox-group>
              <!-- 判断题 -->
              <a-radio-group v-else-if="paperRule.type === 'TRUEFALSE'" v-model="detail.selectedAnswers">
                <a-radio
                  v-for="(option, optIndex) in getQuestionOptions(detail)"
                  :key="optIndex"
                  :value="getOptionLabel(optIndex)"
                  class="option-radio"
                  :disabled="!editable"
                >
                  <span class="option-label">{{ getOptionLabel(optIndex) }}.</span>
                  <span class="option-content">{{ option }}</span>
                </a-radio>
              </a-radio-group>
            </div>
            <!--             答案和解析-->
            <div class="answer-analysis-block" v-if="showAnalysis">
              <div class="answer-row">
                <span class="answer-label">正确答案：</span>
                <span class="answer-value">{{ detail.answer || '-' }}</span>
              </div>
              <div class="analysis-row">
                <span class="analysis-label">解析：</span>
                <span class="analysis-content">{{ detail.explanation || '无' }}</span>
              </div>
            </div>
          </div>
        </div>
        <div v-if="editable" class="submit-button-container">
          <a-button type="primary" size="large" @click="handleSubmit">交卷</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script>
// import { getDictLabel, getDictData } from "@/utils/dict";
import { postAction } from '@api/manage'

export default {
  name: 'QuestionDetailDialog',
  props: {
    visible: { type: Boolean, default: false },
    question: { type: Object, default: () => ({}) },
    showAnalysis: { type: Boolean, default: false },
    editable: { type: Boolean, default: false },
    examId: { type: String, default: '' }
  },
  data() {
    return {
      visibleInner: false,
      count: 0
    }
  },
  watch: {
    async visible(val) {
      this.visibleInner = val
    },
    visibleInner(val) {
      if (!val) this.$emit('update:visible', false)
    },
    question: {
      immediate: true,
      handler(val) {
        if (val && val.paperRules) {
          val.paperRules.forEach((paperRule) => {
            if (paperRule.details) {
              paperRule.details.forEach((detail) => {
              })
            }
          })
        }
      }
    }
  },
  async mounted() {
    // await getDictData("QUESTION_TYPE");
  },
  methods: {
    handleSubmit() {
      // 搜集所有题目的答案
      const answers = []
      let unansweredQuestions = []
      let totalQuestions = 0
      // 遍历所有题型
      this.question.paperRules.forEach((paperRule, gIdx) => {
        // 遍历每个题型下的题目
        paperRule.details.forEach((detail, qIdx) => {
          totalQuestions++
          const questionIndex = this.getQuestionIndex(gIdx, qIdx)
          // 根据题型获取答案
          let userAnswer = null
          let isAnswered = false
          if (this.isSingleChoice(paperRule.type) || paperRule.type === 'TRUEFALSE') {
            // 单选题和判断题
            userAnswer = detail.selectedAnswers ? detail.selectedAnswers.trim() : ''
            isAnswered = userAnswer && userAnswer.trim() !== ''
          } else if (this.isMultiChoice(paperRule.type)) {
            // 多选题 - 将数组转换为连续字母字符串 如: ["A", "C"] -> "AC"
            const selectedArray = detail.selectedAnswers
            if (selectedArray && Array.isArray(selectedArray) && selectedArray.length > 0) {
              userAnswer = selectedArray.sort().join('') // 排序后连接，如 "AC"
              isAnswered = true
            } else {
              userAnswer = ''
              isAnswered = false
            }
          }
          // 记录答案
          answers.push({
            paperDetailId: detail.id,
            questionId: detail.questionId,
            answer: userAnswer,
            type: paperRule.type
          })

          // 记录未答题
          if (!isAnswered) {
            unansweredQuestions.push({
              questionIndex: questionIndex,
              type: paperRule.questionTypeText,
              questionContent: detail.content
            })
          }
        })
      })

      // 检查是否有未答题
      if (unansweredQuestions.length > 0) {
        const unansweredList = unansweredQuestions.map(q =>
          `第${q.questionIndex}题 (${q.type})`
        ).join('、')

        this.$confirm({
          title: '提交确认',
          content: `您还有 ${unansweredQuestions.length} 道题未作答,确定要提交吗？`,
          okText: '确定提交',
          cancelText: '继续答题',
          onOk: () => {
            this.submitAnswers(answers)
          },
          onCancel: () => {
            // 滚动到第一个未答题
            if (unansweredQuestions.length > 0) {
              this.scrollToQuestionByIndex(unansweredQuestions[0].questionIndex)
            }
          }
        })
      } else {
        // 所有题目都已作答，直接提交
        this.$confirm({
          title: '提交确认',
          content: `您已完成全部 ${totalQuestions} 道题，确定要提交试卷吗？`,
          okText: '确定提交',
          cancelText: '再检查一下',
          onOk: () => {
            this.submitAnswers(answers)
          }
        })
      }
    },

    // 提交答案到后端
    submitAnswers(answers) {
      console.log('提交的答案数据：', answers)

      // 将答案数据更新到 question.details 中
      const questionCopy = JSON.parse(JSON.stringify(this.question))
      delete questionCopy.paperRules
      questionCopy.details = answers
      const toSubmitData = {
        'id': this.examId,
        'personId': 1,
        'paperId': questionCopy.id,
        'paperScore': questionCopy.score,
        'details': answers
      }
      // 调用后端API提交答案
      postAction('/education/eduExam/submit', toSubmitData).then(res => {
        if (res.success) {
          this.$message.success(res.message || '试卷提交成功！')
          this.handleClose()
          // 发出提交事件，通知父组件刷新
          this.$emit('submit')
        } else {
          this.$message.warning(res.message || '提交失败，请重试')
        }
      }).catch(error => {
        console.error('提交试卷失败:', error)
        this.$message.error('网络错误，提交失败，请重试')
      })
    },

    // 根据题目序号滚动到指定题目
    scrollToQuestionByIndex(questionIndex) {
      this.$nextTick(() => {
        const ref = this.$refs['question' + questionIndex]
        if (ref && ref[0]) {
          ref[0].scrollIntoView({ behavior: 'smooth', block: 'start' })
        }
      })
    },
    getDictLabel(typeCode, value) {
      // return getDictLabel(typeCode, value);
    },
    handleClose() {
      this.visibleInner = false
      this.$emit('close')
    },
    // 将数字转换为中文序号
    getChineseNumber(num) {
      const chineseNumbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
      if (num <= 0) return ''
      if (num <= 10) return chineseNumbers[num - 1]
      if (num > 10 && num < 20) return '十' + (num > 11 ? chineseNumbers[num - 11] : '')
      if (num === 20) return '二十'
      return '' // 大于20的暂不处理
    },
    getTypeText(type) {
      if (!type) return ''
      // 根据枚举值判断题型
      switch (type) {
        case 'SINGLE':
          return '单选题'
        case 'MULTIPLE':
          return '多选题'
        case 'TRUEFALSE':
          return '判断题'
        default:
          return ''
      }
    },
    isTrueFalse(type) {
      return type === 'TRUEFALSE' // 判断题
    },
    isSingleChoice(type) {
      return type === 'SINGLE' // 单选题
    },
    isMultiChoice(type) {
      return type === 'MULTIPLE' // 多选题
    },
    formatDate(ts) {
      if (!ts) return ''
      const date = new Date(ts)
      return date.toLocaleString()
    },
    getQuestionIndex(gIdx, qIdx) {
      // 题号全局递增
      let idx = 1
      for (let i = 0; i < gIdx; i++) {
        idx += this.question.paperRules[i].details.length
      }
      return idx + qIdx
    },
    scrollToQuestion(gIdx, qIdx) {
      // 滚动到指定题目
      this.$nextTick(() => {
        const ref = this.$refs['question' + this.getQuestionIndex(gIdx, qIdx)]
        if (ref && ref[0]) {
          ref[0].scrollIntoView({ behavior: 'smooth', block: 'start' })
        }
      })
    },
    // 解析题目选项
    getQuestionOptions(question) {
      if (!question || !question.options) {
        return []
      }

      try {
        // 如果 options 是字符串，尝试解析为 JSON
        if (typeof question.options === 'string') {
          return JSON.parse(question.options)
        }
        // 如果已经是数组，直接返回
        if (Array.isArray(question.options)) {
          return question.options
        }
        return []
      } catch (error) {
        console.error('解析题目选项失败:', error, question.options)
        return []
      }
    },
    // 获取选项标签 (A, B, C, D)
    getOptionLabel(index) {
      return String.fromCharCode(65 + index) // A=65, B=66, C=67, D=68
    }
  }
}
</script>

<style scoped>
/* ant-design modal全屏样式 */
.question-detail-dialog :deep(.ant-modal) {
  max-width: 100%;
  top: 0;
  padding-bottom: 0;
  margin: 0;
}

.question-detail-dialog :deep(.ant-modal-content) {
  height: 100vh;
  border-radius: 0;
}

:deep(.ant-modal-body) {
  height: calc(100% - 55px) !important;
  overflow: hidden;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

.empty-text {
  font-size: 16px;
  color: #999;
}

.exam-dialog-layout {
  display: flex;
  height: 90vh;
}

.exam-sidebar {
  width: 220px;
  padding: 20px;
  border-right: 1px solid #e4e7ed;
  overflow-y: auto;
}

.paper-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 18px;
  text-align: center;
}

.exam-time {
  font-size: 14px;
  color: #888;
  text-align: center;
  margin-bottom: 18px;
}

.type-block {
  padding: 10px 0;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 8px;
  text-align: center;
  background: #fff;
  border: 1px solid #e4e7ed;
  transition: background 0.2s;
}

.type-block:hover {
  background: #3e6cf6;
  color: #fff;
  border-color: #3e6cf6;
}

.type-title {
  font-weight: bold;
}

.type-count {
  font-size: 13px;
  color: #888;
}

.exam-content {
  flex: 1;
  padding: 0 40px;
  overflow-y: auto;
}

.exam-header {
  text-align: center;
  margin-bottom: 24px;
}

.exam-title {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 6px;
}

.question-block {
  margin-bottom: 32px;
  border-bottom: 1px solid #eee;
  padding-bottom: 18px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  background-color: #066fec;
  padding: 6px 8px;
  border-radius: 4px;
}

.question-index {
  font-weight: bold;
  font-size: 16px;
  color: #fff0f6;
}

.question-content {
  font-size: 16px;
  color: #222;
  margin-bottom: 12px;
  line-height: 1.7;
  padding: 0 6px;
}

.options-list {
  padding: 0 12px;
}

.option-radio,
.option-checkbox {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.option-label {
  font-weight: bold;
  margin-right: 8px;
}

.option-content {
  flex: 1;
}

.answer-analysis-block {
  background: #f8fafd;
  border-radius: 4px;
  padding: 12px 16px;
  margin-top: 10px;
}

.answer-row,
.analysis-row {
  margin-bottom: 6px;
  font-size: 15px;
}

.answer-label,
.analysis-label {
  color: #666;
  font-weight: bold;
  margin-right: 6px;
}

.answer-value {
  color: #409eff;
  font-weight: bold;
}

.analysis-content {
  color: #333;
}

.type-title-main {
  font-size: 16px;
  font-weight: bold;
  margin: 18px 0 10px 0;
}

.sidebar-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.question-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.question-item {
  width: 28px;
  height: 28px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 13px;
  color: #606266;
  cursor: pointer;
  transition: all 0.2s;
}

.question-item:hover {
  border-color: #409eff;
  color: #409eff;
}

.question-item.is-correct {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
}

.question-item.empty {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #909399;
}

:deep(.ant-checkbox-wrapper + .ant-checkbox-wrapper),
:deep(.ant-radio-wrapper + .ant-radio-wrapper) {
  margin-left: 0 !important;
}

/* 交卷按钮居中容器 */
.submit-button-container {
  text-align: center;
  margin-top: 30px;
  margin-bottom: 20px;
  padding: 20px 0;
}
</style>
