<template>
  <div class="list-page edu-exam-paper-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
    <QuestionDetailDialog
      :visible="showPaper"
      :showAnalysis="true"
      :question="paper"
      @update:visible="closePaperDetail"
      @submit="handlePaperSubmit"
      ref="paperDetailRef" />
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import QuestionDetailDialog from './components/paperDetaiDialog.vue'

export default {
  name: 'EduExamPaperList',
  mixins: [JeecgTreeListMixin],
  components: {
    Mo<PERSON>, QuestionDetailDialog
  },
  data() {
    return {
      editable: false,
      paper: {},
      showPaper: false,
      rightTitle: '试卷管理列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          'name': null
          // 'startTime': null
        },
        formItems: [
          { key: 'name', 'label': '试卷名称', 'type': 'text' }
          // { key: 'startTime', label: '开始时间', type: 'date' }
        ]
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            }
          },
          {
            title: '试卷名称',
            align: 'left',
            dataIndex: 'name',
            width: 200
          },
          {
            title: '关联培训任务',
            align: 'left',
            dataIndex: 'trainNames',
            width: 200
          },
          {
            title: '题目标签',
            align: 'left',
            dataIndex: 'tags_dictText'
          },
          {
            title: '考试时限(分钟)',
            align: 'center',
            dataIndex: 'duration',
            width: 120
          },
          {
            title: '及格分数',
            align: 'center',
            dataIndex: 'passScore',
            width: 120
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 200,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons: [
          {
            text: '预览试卷',
            handler: this.handleShowPaperDetail
          },
          {
            text: '查看',
            handler: this.handleDetail
          },
          {
            text: '编辑',
            handler: this.handleEdit
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete
          }
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd
          }
        ]
      },

      url: {
        list: '/education/eduExamPaper/list',
        delete: '/education/eduExamPaper/delete',
        deleteBatch: '/education/eduExamPaper/deleteBatch',
        exportXlsUrl: '/education/eduExamPaper/exportXls',
        importExcelUrl: 'education/eduExamPaper/importExcel'
      }
    }
  },
  created() {
  },
  computed: {},
  watch: {},
  methods: {
    handleShowPaperDetail(record) {
      this.showPaper = true
      this.paper = record
    },
    // // 重写混入中的 handleDetail 方法，避免打开错误的弹窗
    // handleDetail(record) {
    //   this.handleShowPaperDetail(record)
    // },
    closePaperDetail() {
      this.showPaper = false
    },
    // 处理试卷提交事件
    handlePaperSubmit() {
      // 关闭弹窗
      this.closePaperDetail()
      // 刷新列表数据
      this.loadData()
      // 可以添加其他需要的逻辑，如跳转到成绩页面等
    },
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
    onTableChange(params) {
      this.loadData(params)
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>