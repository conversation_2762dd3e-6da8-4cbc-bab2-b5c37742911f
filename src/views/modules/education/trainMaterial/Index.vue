<template>
  <div class="list-page training-material-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal.vue'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'

export default {
  name: 'TrainingMaterialList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal
  },
  data() {
    return {
      rightTitle: '培训资料列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          'name': null,
          'type': null,
        },
        formItems: [
          { key: 'name', label: '资料名称', type:'text', },
          { key: 'type', label: '类型字典', type:'list', dictCode:'training_material_type', },
        ]
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            }
          },
        {
            title:'创建人',
            align:"center",
            dataIndex: 'username'
        },
        {
            title:'资料名称',
            align:"center",
            dataIndex: 'name'
        },
        {
            title:'类型',
            align:"center",
            dataIndex: 'type_dictText'
        },
        // {
        //     title:'封面图片',
        //     align:"center",
        //     dataIndex: 'cover'
        // },
        // {
        //     title:'资料附件',
        //     align:"center",
        //     dataIndex: 'file'
        // },
        {
            title:'上传单位',
            align:"center",
            dataIndex: 'unit_dictText'
        },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons: [
            {
                text: '查看',
                handler: this.handleDetail,
            },
            {
                text: '编辑',
                handler: this.handleEdit,
            },
            {
                text: '删除',
                type: 'danger',
                handler: this.handleDelete,
            },
        ],
        headerButtons: [
            {
                text: '新增',
                icon: 'plus-circle',
                handler: this.handleAdd,
            },
        ]
      },

      url: {
        list: "/education/trainingMaterial/list",
        delete: "/education/trainingMaterial/delete",
        deleteBatch: "/education/trainingMaterial/deleteBatch",
        exportXlsUrl: "/education/trainingMaterial/exportXls",
        importExcelUrl: "education/trainingMaterial/importExcel",
      }
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
    onTableChange(params) {
      this.loadData(params)
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>