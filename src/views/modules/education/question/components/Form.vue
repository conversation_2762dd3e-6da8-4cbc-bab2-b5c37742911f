<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <!-- 第一行：出题人、题目类型、答案 -->
        <a-row>
          <a-col :span="8">
            <a-form-item label="出题人" :labelCol="labelCol8" :wrapperCol="wrapperCol8">
              <j-dict-select-tag type="list" v-decorator="['author', validatorRules.author]" :trigger-change="true"
                                 dictCode="person_info,name,id" placeholder="请选择出题人" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="题目类型" :labelCol="labelCol8" :wrapperCol="wrapperCol8">
              <j-dict-select-tag type="list" v-decorator="['type', validatorRules.type]" :trigger-change="true"
                                 dictCode="question_type" placeholder="请选择题目类型" @change="onQuestionTypeChange" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="答案" :labelCol="labelCol8" :wrapperCol="wrapperCol8">
              <a-input autocomplete="off" v-decorator="['answer', validatorRules.answer]"
                       placeholder="请输入答案"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 第二行：题目标签、文件上传 -->
        <a-row>
          <a-col :span="12">
            <a-form-item label="题目标签" :labelCol="labelCol12" :wrapperCol="wrapperCol12">
              <JMultiSelectTag v-decorator="['tags', validatorRules.tags]" :trigger-change="true"
                               dictCode="question_tag" placeholder="请选择题目标签" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="文件上传" :labelCol="labelCol12" :wrapperCol="wrapperCol12">
              <j-upload text="添加附件" :number="10" :fileType="'all'" v-model="form.file" size="'mine'">
              </j-upload>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="题目内容" :labelCol="labelCol24" :wrapperCol="wrapperCol24">
              <a-input autocomplete="off" v-decorator="['content', validatorRules.content]"
                       placeholder="请输入题目内容"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 题目选项 -->
        <a-row>
          <a-col :span="24">
            <a-form-item label="题目选项" :labelCol="labelCol24" :wrapperCol="wrapperCol24">
              <div v-for="(option, index) in questionOptions" :key="index" class="option-item">
                <div class="option-content">
                  <span class="option-label">{{ String.fromCharCode(65 + index) }}</span>
                  <a-input
                    v-model="option.content"
                    placeholder="请输入选项内容"
                    class="option-input"
                    :class="{ 'option-error': option.error }"
                    :disabled="isJudgeQuestion"
                    @input="clearOptionError(option)"
                  />
                  <a-button
                    type="link"
                    @click="removeOption(index)"
                    class="remove-option-btn"
                    v-if="questionOptions.length > 1 && !isJudgeQuestion"
                  >
                    删除
                  </a-button>
                </div>
                <div v-if="option.error" class="option-error-message">
                  {{ option.errorMessage }}
                </div>
              </div>
              <a-button
                type="link"
                @click="addOption"
                class="add-option-btn"
                v-if="!isJudgeQuestion"
              >
                新增选项
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="答案详解" :labelCol="labelCol24" :wrapperCol="wrapperCol24">
              <a-textarea :rows="3" autocomplete="off" v-decorator="['explanation', validatorRules.explanation]"
                          placeholder="请输入答案详解"></a-textarea>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" class="submit-button-container">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import JMultiSelectTag from '@/components/dict/JMultiSelectTag'

export default {
  name: 'EduQuestionForm',
  components: {
    JMultiSelectTag
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      // 原来的配置保留为默认值
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      // 为8列宽的表单项添加配置
      labelCol8: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol8: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      // 为12列宽的表单项添加配置
      labelCol12: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol12: {
        xs: { span: 24 },
        sm: { span: 20 }
      },
      // 为24列宽的表单项添加配置
      labelCol24: {
        xs: { span: 22 },
        sm: { span: 2 } // 这个值需要调整以匹配8列宽的标签对齐
      },
      wrapperCol24: {
        xs: { span: 22 },
        sm: { span: 22 }
      },
      confirmLoading: false,
      // 当前选择的题目类型（响应式数据）
      currentQuestionType: null,
      // 题目选项数据
      questionOptions: [
        { content: '', error: false, errorMessage: '' },
        { content: '', error: false, errorMessage: '' },
        { content: '', error: false, errorMessage: '' },
        { content: '', error: false, errorMessage: '' }
      ],
      validatorRules: {
        author: {
          rules: [
            { required: true, message: '请输入出题人!' }
          ]
        },
        type: {
          rules: [
            { required: true, message: '请输入题目类型!' }
          ]
        },
        answer: {
          rules: [
            { required: true, message: '请输入答案!' }
          ]
        },
        tags: {
          rules: [
            { required: true, message: '请输入题目标签!' }
          ]
        },
        content: {
          rules: [
            { required: true, message: '请输入题目内容!' }
          ]
        },

        explanation: {
          rules: [
            { required: true, message: '请输入答案详解!' }
          ]
        }
      },
      url: {
        add: '/education/eduQuestion/add',
        edit: '/education/eduQuestion/edit',
        queryById: '/education/eduQuestion/queryById'
      }
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
    // 判断是否为判断题
    isJudgeQuestion() {
      console.log('计算 isJudgeQuestion, currentQuestionType:', this.currentQuestionType)
      return this.currentQuestionType === 'TRUEFALSE' // 判断题的枚举值为 "TRUEFALSE"
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true

      // 处理选项数据
      if (record.options && typeof record.options === 'string') {
        try {
          const options = JSON.parse(record.options)
          this.questionOptions = options.map(opt => ({ content: opt, error: false, errorMessage: '' }))
        } catch (e) {
          // 如果解析失败，保持默认的4个空选项
          this.questionOptions = [
            { content: '', error: false, errorMessage: '' },
            { content: '', error: false, errorMessage: '' },
            { content: '', error: false, errorMessage: '' },
            { content: '', error: false, errorMessage: '' }
          ]
        }
      } else if (Array.isArray(record.options)) {
        this.questionOptions = record.options.map(opt => ({ content: opt, error: false, errorMessage: '' }))
      }

      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'author', 'type', 'answer', 'tags', 'content', 'explanation'))
        // 根据题目类型设置选项
        this.onQuestionTypeChange(this.model.type)
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this

      // 判断题不需要校验选项内容（已固定）
      if (!this.isJudgeQuestion) {
        // 先校验选项内容
        const hasEmptyOption = this.questionOptions.some(option => !option.content.trim())

        if (hasEmptyOption) {
          // 标记空选项为错误状态
          this.questionOptions.forEach(option => {
            if (!option.content.trim()) {
              this.$set(option, 'error', true)
              this.$set(option, 'errorMessage', '请填写选项内容')
            } else {
              this.$set(option, 'error', false)
              this.$set(option, 'errorMessage', '')
            }
          })
          this.$message.error('请填写完所有选项内容后再提交')
          return
        }
      }

      // 清除所有选项错误状态
      this.questionOptions.forEach(option => {
        this.$set(option, 'error', false)
        this.$set(option, 'errorMessage', '')
      })

      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          // 处理选项数据（不过滤空选项，因为已经校验过了）
          const optionsData = this.questionOptions.map(opt => opt.content)
          let formData = Object.assign(this.model, values, { options: JSON.stringify(optionsData) })
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }

      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'author', 'type', 'answer', 'tags', 'content', 'options', 'explanation'))
    },
    // 添加选项
    addOption() {
      // 判断题不允许添加选项
      console.log('addOption', this.isJudgeQuestion)
      if (this.isJudgeQuestion) {
        this.$message.warning('判断题选项已固定，无法添加新选项')
        return
      }

      // 校验现有选项是否都已填写
      const hasEmptyOption = this.questionOptions.some(option => !option.content.trim())

      if (hasEmptyOption) {
        // 标记空选项为错误状态
        this.questionOptions.forEach(option => {
          if (!option.content.trim()) {
            this.$set(option, 'error', true)
            this.$set(option, 'errorMessage', '请先填写此选项内容')
          } else {
            this.$set(option, 'error', false)
            this.$set(option, 'errorMessage', '')
          }
        })
        this.$message.warning('请先填写完所有选项内容再新增')
        return
      }

      // 清除所有错误状态
      this.questionOptions.forEach(option => {
        this.$set(option, 'error', false)
        this.$set(option, 'errorMessage', '')
      })

      // 添加新选项
      this.questionOptions.push({ content: '', error: false, errorMessage: '' })
    },
    // 删除选项
    removeOption(index) {
      // 判断题不允许删除选项
      if (this.isJudgeQuestion) {
        this.$message.warning('判断题选项已固定，无法删除选项')
        return
      }

      if (this.questionOptions.length > 1) {
        this.questionOptions.splice(index, 1)
      }
    },
    // 处理文件上传
    handleUpload() {
      // 这里可以添加文件上传逻辑
      this.$message.info('文件上传功能待实现')
    },
    // 清除选项错误状态
    clearOptionError(option) {
      if (option.content.trim()) {
        this.$set(option, 'error', false)
        this.$set(option, 'errorMessage', '')
      }
    },

    // 题目类型变化处理
    onQuestionTypeChange(value) {
      console.log('题目类型变化:', value)
      // 更新响应式数据
      this.currentQuestionType = value

      if (value === 'TRUEFALSE') {
        // 判断题：固定两个选项
        this.questionOptions = [
          { content: '正确', error: false, errorMessage: '' },
          { content: '错误', error: false, errorMessage: '' }
        ]
      } else {
        // 其他题型：恢复默认的4个空选项
        if (this.questionOptions.length === 2 &&
          this.questionOptions[0].content === '正确' &&
          this.questionOptions[1].content === '错误') {
          this.questionOptions = [
            { content: '', error: false, errorMessage: '' },
            { content: '', error: false, errorMessage: '' },
            { content: '', error: false, errorMessage: '' },
            { content: '', error: false, errorMessage: '' }
          ]
        }
      }
    }
  }
}
</script>

<style scoped>
/* 选项容器 */
.option-item {
  margin-bottom: 8px;
}

/* 选项内容行 */
.option-content {
  display: flex;
  align-items: center;
}

/* 选项标签 (A, B, C, D) */
.option-label {
  margin-right: 8px;
  font-weight: bold;
}

/* 选项输入框 */
.option-input {
  flex: 1;
  margin-right: 8px;
}

/* 删除选项按钮 */
.remove-option-btn {
  color: #1890ff;
}

/* 新增选项按钮 */
.add-option-btn {
  color: #1890ff;
}

/* 选项错误状态 */
.option-error {
  border-color: #f5222d !important;
  box-shadow: 0 0 0 2px rgba(245, 34, 45, 0.2) !important;
}

.option-error:focus {
  border-color: #f5222d !important;
  box-shadow: 0 0 0 2px rgba(245, 34, 45, 0.2) !important;
}

/* 选项错误信息 */
.option-error-message {
  color: #f5222d;
  font-size: 12px;
  margin-left: 24px;
  margin-top: 4px;
}

/* 提交按钮容器 */
.submit-button-container {
  text-align: center;
}
</style>