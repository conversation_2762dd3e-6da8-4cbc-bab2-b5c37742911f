<template>
  <div class="list-page edu-question-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'

export default {
  name: 'EduQuestionList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal
  },
  data() {
    return {
      rightTitle: '题库列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          'type': null,
          'tags': null,
          'content': null
        },
        formItems: [
          { key: 'type', label: '题目类型', type: 'list', dictCode: 'question_type' },
          { key: 'tags', label: '题目标签', type: 'list', dictCode: 'question_tag' },
          { key: 'content', label: '题目内容', type: 'text' }
        ]
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
          isorter: {
            column: 'createTime',
            order: 'desc'
          }

        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            }
          },
          {
            title: '题目内容',
            dataIndex: 'content',
            align: 'left'
          },
          {
            title: '题目类型',
            align: 'center',
            width: 100,
            dataIndex: 'type_dictText'
          },
          {
            title: '题目标签',
            align: 'center',
            dataIndex: 'tags_dictText'
          },
          {
            title: '答题人数',
            align: 'center',
            dataIndex: 'answer_count',
            width: 100
          },
          {
            title: '答错人数',
            align: 'center',
            dataIndex: 'error_count',
            width: 100
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail
          },
          {
            text: '编辑',
            handler: this.handleEdit
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete
          }
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd
          }
        ]
      },

      url: {
        list: '/education/eduQuestion/list',
        delete: '/education/eduQuestion/delete',
        deleteBatch: '/education/eduQuestion/deleteBatch',
        exportXlsUrl: '/education/eduQuestion/exportXls',
        importExcelUrl: 'education/eduQuestion/importExcel'
      }
    }
  },
  created() {
  },
  computed: {},
  watch: {},
  methods: {
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
    // 重写 onTableChange 方法，修复 sorter 为 undefined 的问题
    onTableChange(tableChangeParams) {
      let { pagination, filters, sorter } = tableChangeParams
      sorter = sorter || {}
      filters = filters || {}
      this.handleTableChange(pagination, filters, sorter)
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>