<template>
  <div class="list-page contract-info-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { httpAction, getAction } from '@/api/manage'

export default {
  name: 'ContractInfoList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {
      rightTitle: '合同列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          code: null,
          name: null,
        },
        formItems: [
          { key: 'code', label: '合同编号', type: 'text' },
          { key: 'name', label: '合同名称', type: 'text' },
        ],
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '合同编号',
            align: 'center',
            dataIndex: 'code',
          },
          {
            title: '合同名称',
            align: 'center',
            dataIndex: 'name',
          },
          {
            title: '合同乙方',
            align: 'center',
            dataIndex: 'partyB',
          },
          {
            title: '合同总价(元)',
            align: 'center',
            dataIndex: 'totalMoney',
          },
          {
            title: '签订日期',
            align: 'center',
            dataIndex: 'signDate',
            customRender: function (t, r, index) {
              return !t ? '' : t.length > 10 ? t.substr(0, 10) : t
            },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],
      },

      url: {
        list: '/settlement/contractInfo/list',
        query: '/settlement/contractInfo/queryById',
        delete: '/settlement/contractInfo/delete',
        deleteBatch: '/settlement/contractInfo/deleteBatch',
        exportXlsUrl: '/settlement/contractInfo/exportXls',
        importExcelUrl: 'settlement/contractInfo/importExcel',
      },
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    handleEdit(record) {
      getAction(this.url.query, { id: record.id }).then((res) => {
        if (res.success) {
          record = res.result
          this.$refs.modalForm.edit(record)
          this.$refs.modalForm.disableSubmit = false
          this.$refs.modalForm.title = '编辑'
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handleDetail(record) {
      getAction(this.url.query, { id: record.id }).then((res) => {
        if (res.success) {
          record = res.result
          this.$refs.modalForm.edit(record)
          this.$refs.modalForm.disableSubmit = true
          this.$refs.modalForm.title = '详情'
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
