<template>
  <div>
    <a-spin :spinning="confirmLoading">
      <j-form-container :disabled="formDisabled">
        <a-form :form="form" slot="detail">
          <a-row>
            <a-col :span="12">
              <a-form-item label="合同编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  autocomplete="off"
                  v-decorator="['code', validatorRules.code]"
                  placeholder="请输入合同编号"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="合同名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  autocomplete="off"
                  v-decorator="['name', validatorRules.name]"
                  placeholder="请输入合同名称"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="合同甲方" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  autocomplete="off"
                  v-decorator="['partyA', validatorRules.partyA]"
                  placeholder="请输入合同甲方"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="合同乙方" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  autocomplete="off"
                  v-decorator="['partyB', validatorRules.partyB]"
                  placeholder="请输入合同乙方"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12" style="display: none">
              <a-form-item label="合同监理方" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  autocomplete="off"
                  v-decorator="['contractSupervisor']"
                  placeholder="请输入合同监理方"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="合同总价(元)" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input-number
                  autocomplete="off"
                  v-decorator="['totalMoney', validatorRules.totalMoney]"
                  placeholder="请输入合同总价"
                  style="width: 100%"
                  disabled
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="签订日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <j-date
                  placeholder="请选择签订日期到天"
                  v-decorator="['signDate']"
                  :trigger-change="true"
                  :show-time="false"
                  date-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="合同附件" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <j-upload v-decorator="['file', validatorRules.file]" name="file" :disabled="formDisabled" />
              </a-form-item>
            </a-col>
            <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
              <a-button @click="submitForm">提 交</a-button>
            </a-col>
          </a-row>
        </a-form>
      </j-form-container>
      <a-row>
        <a-col :span="24">
          <a-form-item
            :wrapperCol="{
              xs: { span: 24 },
              sm: { span: 24 },
            }"
          >
            <table-layout class="my-table" :table-props="tableProps" @init-params="initParams">
              <template #header="{ record }">
                <a-button type="primary" @click="download" icon="download">模板下载</a-button>
                <a-button type="primary" :disabled="formDisabled" @click="triggerFileInput" icon="upload"
                  >导入清单
                </a-button>
                <a-button type="primary" :disabled="formDisabled" @click="addRow" icon="plus-circle">新增</a-button>
              </template>
              <template #downloadButton="{ record }"></template>
              <template #addButton="{ record }"></template>
              <template #code="{ record }">
                <a-input
                  v-model="record.code"
                  autocomplete="false"
                  placeholder="请输入"
                  :disabled="formDisabled"
                ></a-input>
              </template>
              <template #name="{ record }">
                <a-input
                  v-model="record.name"
                  autocomplete="false"
                  placeholder="请输入"
                  :disabled="formDisabled"
                ></a-input>
              </template>
              <template #unit="{ record }">
                <!--                <a-input v-if='!record.addFlag'-->
                <!--                         v-model='record.unit'-->
                <!--                         autocomplete='false'-->
                <!--                         placeholder='请输入'-->
                <!--                         :disabled='formDisabled'-->
                <!--                ></a-input>-->
                <a-select v-model="record.unit" :disabled="formDisabled" autocomplete="false" :allowClear="true" style="width: 100%">
                  <a-select-option v-for="(item, id) in unitList" :key="item.title" :value="item.title"
                    >{{ item.title }}
                  </a-select-option>
                </a-select>
              </template>
              <template #amount="{ record }">
                <a-input-number
                  v-model="record.amount"
                  autocomplete="false"
                  placeholder="请输入"
                  :disabled="formDisabled"
                  @change="amountChange(record)"
                ></a-input-number>
              </template>
              <template #unitMoney="{ record }">
                <a-input-number
                  v-decorator="[
                    'partyB',
                    {
                      rules: [{ pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '请输入正确的数值' }],
                    },
                  ]"
                  v-model="record.unitMoney"
                  autocomplete="false"
                  placeholder="请输入"
                  :disabled="formDisabled"
                  @change="unitMoneyChange(record)"
                ></a-input-number>
              </template>
              <template #totalMoney="{ record }">
                <a-input v-model="record.totalMoney" autocomplete="false" placeholder="请输入" disabled></a-input>
              </template>
              <template #download>
                <a-button @click="download">下载</a-button>
              </template>
            </table-layout>
          </a-form-item>
        </a-col>
      </a-row>
    </a-spin>
    <input type="file" ref="fileInput" style="display: none" @change="handleFileUpload" />
  </div>
</template>

<script>
import { httpAction, getAction, downloadFile, deleteAction } from '@/api/manage'
import pick from 'lodash.pick'
import { validateDuplicateValue } from '@/utils/util'
import TableLayout from '@/components/tableLayout/TableLayout.vue'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { pushIfNotExist, randomNumber, randomUUID } from '@/utils/util'

import * as XLSX from 'xlsx'
import Vue from 'vue' //此处用 impor XLSX from 'xlsx' 会报错!!!
export default {
  name: 'ContractInfoForm',
  mixins: [JeecgTreeListMixin],
  components: { TableLayout },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      unitList: [],
      isorter: {
        column: 'createTime',
        order: 'desc',
      },
      disableMixinCreated: true,
      tableProps: {
        rowKey: 'index',
        dataSource: [],
        ipagination: false,
        columns: [
          {
            title: '序号',
            align: 'center',
            dataIndex: 'index',
            customRender: (text, record, index) => {
              return `${index + 1}`
            },
            width: 60,
          },
          {
            title: '编号',
            dataIndex: 'code',
            align: 'center',
            width: 80,
            slot: 'customTitle',
            scopedSlots: { customRender: 'code' },
            disabled: this.isDisable,
          },
          {
            title: '名称',
            dataIndex: 'name',
            align: 'center',
            scopedSlots: { customRender: 'name' },
            disabled: this.isDisable,
          },
          {
            title: '单位',
            dataIndex: 'unit',
            align: 'center',
            width: 100,
            scopedSlots: { customRender: 'unit' },
            disabled: this.isDisable,
          },

          {
            title: '数量',
            dataIndex: 'amount',
            align: 'center',
            width: 100,
            scopedSlots: { customRender: 'amount' },
            disabled: this.isDisable,
          },

          {
            title: '单价(元)',
            dataIndex: 'unitMoney',
            align: 'center',
            width: 130,
            scopedSlots: { customRender: 'unitMoney' },
            disabled: this.isDisable,
          },

          {
            title: '合价(元)',
            dataIndex: 'totalMoney',
            width: 130,
            align: 'center',
            scopedSlots: { customRender: 'totalMoney' },
            disabled: this.isDisable,
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: '90px',
            scopedSlots: { customRender: 'action' },
            disabled: this.isDisable,
          },
        ],
        actionButtons: [
          {
            text: '删除',
            type: 'danger',
            handler: this.removeRow,
            disabled: this.isDisable,
          },
        ],
        // headerButtons: [
        //   {
        //     text: '模板下载',
        //     icon: 'download',
        //     handler: this.download,
        //     scopedSlots: { customRender: 'downloadButton' },
        //   },
        //   {
        //     text: '导入清单',
        //     icon: 'upload',
        //     handler: this.triggerFileInput,
        //     disabled: this.isDisable,
        //     scopedSlots: { customRender: 'uploadButton' },
        //   },
        //   {
        //     text: '新增',
        //     icon: 'plus-circle',
        //     handler: this.addRow,
        //     disabled: this.isDisable(),
        //     scopedSlots: { customRender: 'addButton' },
        //   },
        // ],
      },

      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        unitMoney: {
          rules: [{ pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '请输入正确的数值' }],
        },
        code: {
          rules: [{ required: true, message: '请输入合同编码!' }],
        },
        name: {
          rules: [{ required: true, message: '请输入合同名称!' }],
        },
        partyA: {
          rules: [{ required: true, message: '请输入合同甲方!' }],
        },
        partyB: {
          rules: [{ required: true, message: '请输入合同乙方!' }],
        },
        totalMoney: {
          rules: [
            { required: true, message: '请输入合同总价!' },
            //加入校验规则,必须是合法数字
            { pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '请输入正确的数值' },
          ],
        },
        file: {
          rules: [{ required: true, message: '请输入合同附件!' }],
        },
      },
      url: {
        add: '/settlement/contractInfo/addOrEdit',
        edit: '/settlement/contractInfo/addOrEdit',
        queryById: '/settlement/contractInfo/queryById',
        delete: '/settlement/contractInfoDetail/delete',
        download: '/static/template/合同清单导入模板.xlsx',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },

    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
    this.loadUnitList()
  },

  methods: {
    loadUnitList() {
      getAction('sys/dict/getDictItems/air_china_unit', {}).then((res) => {
        if (res.success) {
          this.unitList = res.result
        }
      })
    },
    calcTotal() {
      //统计末端节点的金额
      let total = 0
      for (let index = 0; index < this.tableProps.dataSource.length; index++) {
        const item1 = this.tableProps.dataSource[index]
        if (!item1.code) {
          total += item1.totalMoney
          continue
        }
        let ok = true //检查item1 是否是某个节点的父级
        for (let index1 = 0; index1 < this.tableProps.dataSource.length; index1++) {
          const item2 = this.tableProps.dataSource[index1]
          if (!item2.code) continue
          if (item2.code.toString().indexOf(item1.code.toString() + '.') === 0) {
            ok = false
            break
          }
        }
        if (ok) {
          //ok 表示item1是叶子结点
          total += item1.totalMoney
        }
      }
      if (isNaN(total)) {
        this.$message.warn('合同总价内容必须是数字且不能为空')
      }
      total = total.toFixed(2)
      this.form.setFieldsValue({ totalMoney: total })
    },

    unitMoneyChange(record) {
      record.totalMoney = Number(((record.amount * record.unitMoney * 100) / 100).toFixed(2))
      this.calcTotal()
    },
    amountChange(record) {
      record.totalMoney = Number(((record.amount * record.unitMoney * 100) / 100).toFixed(2))
      this.calcTotal()
    },
    isDisable() {
      return this.formDisabled
    },
    download() {
      let fileName = '合同清单导入模板.xlsx'
      var link = document.createElement('a')
      link.href = this.url.download
      link.download = fileName
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    triggerFileInput() {
      this.$nextTick(() => {
        this.$refs.fileInput.click()
      })
      console.log('triggerFileInput')
    },
    handleFileUpload(event) {
      console.log('开始读取文件')
      const files = event.target.files
      console.log(files.length)
      if (files.length === 0) return
      const file = files[0]
      const reader = new FileReader()
      const fieldMap = {
        code: '编号',
        name: '名称',
        unit: '单位',
        amount: '数量',
        unitMoney: '单价(元)',
        totalMoney: '合价(元)',
      }

      reader.onload = (e) => {
        let that = this
        const data = new Uint8Array(e.target.result)
        console.log(XLSX)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const sheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(sheet) //加上{header:1}会把第一行作为表头
        let newArr = []
        jsonData &&
          jsonData.length &&
          jsonData.forEach((item) => {
            newArr.push({
              code: item[fieldMap.code],
              name: item[fieldMap.name],
              unit: item[fieldMap.unit],
              amount: item[fieldMap.amount],
              unitMoney: item[fieldMap.unitMoney],
              totalMoney: item[fieldMap.totalMoney],
              index: randomUUID(),
            })
          })
        debugger
        let dataToAdd = []

        for (let index = 0; index < newArr.length; index++) {
          const element = newArr[index]
          let targetIndex = this.tableProps.dataSource.findIndex((i) => i.code === element.code)
          if (targetIndex > -1) {
            // 更新
            Object.assign(this.tableProps.dataSource[targetIndex], element)
          } else {
            // 增加
            dataToAdd.push(element)
          }
        }
        if (dataToAdd.length > 0) {
          that.tableProps.dataSource = that.tableProps.dataSource.concat(dataToAdd)
        }
        this.calcTotal()
        this.$refs.fileInput.value = '' // 清空input方便下次继续上传
      }
      reader.readAsArrayBuffer(file)
    },
    removeRow(record) {
      console.log('removeRow', record)
      if (record.id) {
        this.$confirm({
          title: '彻底删除',
          content: (
            <div>
              <p>您确定要彻底删除这一项吗？</p>
              <p style="color:red;">注意：彻底删除后将无法恢复，请谨慎操作！</p>
            </div>
          ),
          centered: false,
          onOk: () => {
            var that = this
            deleteAction(that.url.delete, { id: record.id }).then((res) => {
              if (res.success) {
                this.$message.success('操作成功')
                const index = that.tableProps.dataSource.findIndex((item) => item.id === record.id)
                if (index > -1) {
                  that.tableProps.dataSource.splice(index, 1)
                  this.calcTotal()
                }
              } else {
                that.$message.warning(res.message)
              }
            })
          },
        })
      } else {
        const index = this.tableProps.dataSource.findIndex((item) => item.index === record.index)
        console.log('被移除index', index)
        if (index > -1) {
          this.tableProps.dataSource.splice(index, 1)
          this.calcTotal()
        }
      }
    },

    addRow() {
      let count = 0
      count = this.tableProps.dataSource.length
      this.tableProps.dataSource.push({
        index: randomUUID(),
        id: null,
        code: null,
        name: null,
        unit: null,
        amount: null,
        unitMoney: null,
        totalMoney: null,
        addFlag: true,
      })
      this.$nextTick(() => {
        const tableContainer = document.querySelector('.my-table')
        if (tableContainer) {
          let table = tableContainer.getElementsByClassName('ant-table-body')
          let length = table.length
          console.log('表格搜索结果', table)
          if (length > 0) {
            table[length - 1].scrollTop = table[length - 1].scrollHeight
          }
        }
      })
    },
    initParams(mergeParams) {
      console.log('initParams-mergeParams', mergeParams)
      this.queryParam = mergeParams.params
    },

    add() {
      this.tableProps.dataSource = []
      this.edit({})
    },
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      //这里如果有children就直接赋值，没有就清空
      this.$nextTick(() => {
        if (record.children) {
          this.tableProps.dataSource = record.children
        } else {
          this.tableProps.dataSource = []
        }
        this.form.setFieldsValue(
          pick(this.model, 'code', 'name', 'partyA', 'partyB', 'contractSupervisor', 'totalMoney', 'signDate', 'file')
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },

    validateTable() {
      let nullCodeIndex = this.tableProps.dataSource.findIndex((item) => {
        return item.code == null || item.code === '' || item.code === undefined
      })
      console.log('nullCodeIndex', nullCodeIndex)
      if (nullCodeIndex >= 0) {
        this.$message.error('第' + (nullCodeIndex + 1) + '行合同编号为空')
        this.confirmLoading = false
        return false
      }
      let duplicateIndex = this.tableProps.dataSource.findIndex((item1, index1) => {
        return (
          this.tableProps.dataSource.findIndex((item2, index2) => {
            return index1 !== index2 && item1.code === item2.code
          }) !== -1
        )
      })
      if (duplicateIndex !== -1) {
        this.$message.error('第' + (duplicateIndex + 1) + '行合同编号有重复')
        this.confirmLoading = false
        return false
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'post'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          console.log('this.tableProps.dataSource', this.tableProps.dataSource)

          formData.children = this.tableProps.dataSource

          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
                this.tableProps.dataSource = []
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(row, 'code', 'name', 'partyA', 'partyB', 'contractSupervisor', 'totalMoney', 'signDate', 'file')
      )
    },
  },
}
</script>
<style lang="less" scoped>
.my-table {
  height: 400px;
}
</style>
