<template>
  <div class='list-page'>
    <table-layout
      ref='tableLayout'
      :rightTitle='rightTitle'
      :search-props='searchProps'
      :table-props='tableProps'
      :rowKey='record => record.id'
      @init-params='initParams'
      @search-submit='searchQuery'
      @search-reset='searchReset'
      @table-expand='onTableExpand'
    >
      <!--      @table-change='onTableChange'-->
<!--      <template #table>-->
<!--        <a-table class='custom-table'-->
<!--                 :rowKey='record => record.id'-->
<!--                 :columns='tableProps.columns'-->
<!--                 :data-source='tableProps.dataSource'-->
<!--                 :row-selection='tableProps.rowSelection'-->
<!--                 :expanded-row-keys.sync='tableProps.expandedRowKeys'-->

<!--        />-->
<!--      </template>-->


      <template #code>
        <a-input placeholder='请输入清单编号'
                 v-model='code'></a-input>
      </template>
      <template #name='{ record }'>
        <a-select v-model='contractId' ref='select' @change='changeName' :allowClear='true'>
          <a-select-option v-for='item in options' :key='item.id' :value='item.id'>{{ item.name }} {{ item.code }}
          </a-select-option>
        </a-select>
      </template>
    </table-layout>
  </div>
</template>

<script>
import TableLayout from '@/components/tableLayout/TableLayout.vue'
// import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { getAction } from '@/api/manage'

export default {
  name: 'SettlementLedgerList',
  components: {
    TableLayout
  },
  // mixins: [JeecgTreeListMixin],
  data() {
    return {
      code: null,
      contractId: null,
      options: [],
      disableMixinCreated: false, // 初始化是否执行混入请求
      rightTitle: '结算台账',
      searchProps: {
        formModel: {
          code: '',
          contractId: ''
        },
        formItems: [
          {
            key: 'contractId',
            label: '合同名称',
            type: 'select',
            options: this.options,
            slot: 'name'
          },
          { key: 'code', label: '清单编号', slot: 'code' }

        ]
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: false,
        columns: [
          {
            title: '合同编号',
            align: 'left',
            dataIndex: 'code'
          },
          {
            title: '合同名称',
            align: 'center',
            dataIndex: 'name'
          },
          {
            title: '合同金额(元)',
            align: 'center',
            dataIndex: 'totalMoney'
          },
          {
            title: '应结算金额(元)',
            align: 'center',
            dataIndex: 'settledMoney'
          },
          {
            title: '发票金额(元)',
            align: 'center',
            dataIndex: 'invoiceMoney'
          },
          {
            title: '支付金额(元)',
            align: 'center',
            dataIndex: 'payMoney'
          },
          {
            title: '支付比例(%)',
            align: 'center',
            dataIndex: '',
            customRender: (text, record, index) => {
              let value = ((record.payMoney / record.totalMoney) * 100).toFixed(2)
              if (isNaN(value)) {
                return '-'
              } else if (value > 100) {
                return <span style='color:red'>{value}</span>
              } else {
                return value
              }
            }
          }
        ],
        actionButtons: [],
        expandedRowKeys: [] // 用于控制哪些行展开
      },

      url: {
        optionList: '/settlement/settlementLedger/getSettledContractList',
        list: '/settlement/settlementLedger/rootList',
        childList: '/settlement/settlementLedger/childList',
        getChildListBatch: '/settlement/settlementLedger/getChildListBatch',
        contractQuery: '/settlement/settlementLedger/queryContract'
      }
    }
  },
  created() {
    this.loadSearchOptions()
    this.searchQuery()
  },
  methods: {
    getNodeIdsFromTree(tree, ids) {
      tree.forEach(item => {
        if (item.children != null) {
          ids.push(item.id)
          this.getNodeIdsFromTree(item.children, ids)
        }
      })
    },
    searchQuery() {
      let params = {}
      if (this.contractId) {
        params.contractId = this.contractId
      }
      if (this.code) {
        params.code = this.code
      }
      getAction(this.url.list, params).then((res) => {
        this.loadDataAfter(res)
      })
    },


    changeName(contractId) {
      console.log('changeName', contractId)
      this.searchProps.formModel.contractId = contractId
    },
    loadSearchOptions() {
      getAction(this.url.optionList, { pid: '0' }).then((res) => {
        if (res.success) {
          this.options = res.result
          console.log('options', this.options)
        }
      })
    },
//=================================================================
    loadDataAfter(res) {
      let nodeIds = []
      console.log('loadDataAfter', res)
      if (Number(res.result.total) > 0) {
        this.tableProps.dataSource = res.result.records
        if (this.contractId || this.code) {
          this.getNodeIdsFromTree(res.result.records, nodeIds)
          // this.tableProps.expandedRowKeys = nodeIds
          this.$nextTick(() => {
            this.$set(this.tableProps, 'expandedRowKeys', nodeIds)
            console.log('expand', nodeIds, this.tableProps.expandedRowKeys)
          })
        }
      } else {
        this.tableProps.dataSource = []
        this.tableProps.defaultExpandAllRows = false
      }
    },
    // 处理序号
    onTableExpandAfter(dataList) {
      this.calcPath(dataList, '')
    },

    getDataByResult(result) {
      if (result) {
        return result.map((item) => {
          // 判断是否标记了带有子节点
          if (item[this.hasChildrenField] == '1') {
            let loadChild = { id: item.id + '_loadChild', name: 'loading...', isLoading: true }
            item.children = [loadChild]
          }
          return item
        })
      }
    },
    loadDataByExpandedRowsAfter(dataList) {
      console.log('loadDataByExpandedRowsAfter1', dataList)
      this.calcPath(dataList, '')
      console.log('loadDataByExpandedRowsAfter2', dataList)
    },
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
    onTableExpand(expanded, record) {

    },
    searchReset() {
      this.code = null
      this.contractId = null
      this.searchProps.formModel.code = null
      this.searchProps.formModel.contractId = null
      this.queryParam = {}
      this.$set(this.tableProps, 'expandedRowKeys', [])
      // this.loadData(1)
      this.searchQuery()

    },

    // loadDataBefore(params) {
    //   // this.tableProps.expandedRowKeys = []
    //   console.log('tableProps.expandedRowKeys', this.tableProps.expandedRowKeys)
    //   // this.tableProps.dataSource = []
    //   console.log('loadDataBefore', params)
    //   return true
    // }
    loadDataByExpandedRows(dataList) {
      console.log('loadDataByExpandedRows===============')
      if (this.tableProps.expandedRowKeys && this.tableProps.expandedRowKeys.length > 0) {
        return getAction(this.url.getChildListBatch, {
          parentIds: this.tableProps.expandedRowKeys.join(','),
          code: this.searchProps.formModel.code
        }).then(res => {
          if (res.success && res.result.records.length > 0) {
            // 已展开的数据批量子节点
            let records = res.result.records
            const listMap = new Map()
            for (let item of records) {
              let pid = item[this.pidField]
              if (this.tableProps.expandedRowKeys.join(',').includes(pid)) {
                let mapList = listMap.get(pid)
                if (mapList == null) {
                  mapList = []
                }
                mapList.push(item)
                listMap.set(pid, mapList)
              }
            }
            let childrenMap = listMap
            let fn = (list) => {
              if (list) {
                list.forEach(data => {
                  if (this.tableProps.expandedRowKeys.includes(data.id)) {
                    data.children = this.getDataByResult(childrenMap.get(data.id))
                    fn(data.children)
                  }
                })
              }
            }
            fn(dataList)
            // 根据已展开的行查询数据之后
            if (this.loadDataByExpandedRowsAfter && (typeof this.loadDataByExpandedRowsAfter === 'function')) {
              this.loadDataByExpandedRowsAfter(dataList)
            }
          }
        })
      } else {
        return Promise.resolve()
      }
    }
  }
}
</script>


<style lang='less' scoped>
.custom-table {
  // background: #ccc;
  overflow: auto;
  height: 100%;
  // padding: 12px 0;
  display: flex;
  flex-direction: column;

  .table-header {
    flex-shrink: 0;
    margin-bottom: 12px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    > * {
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .table-body {
    flex-grow: 1;
    display: flex;

    .actions {
      .default {
        color: #066fec;
      }

      .warning {
        color: #ff7d00;
      }

      .danger {
        color: #ff4d4f;
      }
    }

    // ::v-deep .ant-table td {
    //   border-right: 1px solid #ece8e8; /* 设置竖向边框颜色 */
    // }
    // ::v-deep .ant-table td:last-child {
    //   border-right: none; /* 最后一列不需要竖向右边框 */
    // }

    // ::v-deep .ant-table tbody > tr > td {
    //   border-bottom: none; /* 去除横向底部边框 */
    // }
    // ::v-deep .ant-table-thead > tr > th {
    //   border-bottom: none; /* 去除表头横向底部边框 */
    // }

    ::v-deep .ant-spin-nested-loading {
      flex-grow: 1;
    }

    ::v-deep .ant-spin-container {
      height: 100%;
      display: flex;
      flex-direction: column;

      .ant-table {
        flex-grow: 1;
      }

      .ant-pagination {
        display: flex;
        justify-content: flex-end;
      }
    }

    ::v-deep .ant-table-header {
      width: calc(100% + 9px);
    }

    ::v-deep .ant-table-thead {
      tr > th {
        background: #066fec !important;
        color: #ffffff;
      }
    }

    // ::v-deep .ant-table-pagination {
    //   margin-bottom: 0;
    // }
  }
}
</style>
