<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <!-- 申报信息 -->
      <a-form :form="form" slot="detail">
        <a-row>
          <div class="right-header mb-20">
            <img class="icon" src="@/assets/custom/title-icon.svg" />
            <span class="title">申报信息</span>
          </div>
          <a-col :span="8">
            <a-form-item label="结算合同" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select
                v-if="model.id == null"
                placeholder="请选择"
                :allowClear="true"
                style="width: 100%"
                v-decorator="['contractId', validatorRules.contractId]"
                @change="handleChangeContract"
              >
                <a-select-option v-for="(item, id) in contractList" :key="id" :value="item.id"
                  >{{ item.name + ' ' + item.code }}
                </a-select-option>
              </a-select>

              <a-input
                v-if="!!model.id"
                disabled
                autocomplete="off"
                v-decorator="['contractName', validatorRules.contractName]"
                placeholder="请输入合同名称"
              ></a-input>
              <a-input hidden disabled autocomplete="off" v-decorator="['contractMoney']"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="结算名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['settleName', validatorRules.settleName]"
                placeholder="请输入结算名称"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="应结算金额(元)" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                autocomplete="off"
                v-decorator="['totalMoney']"
                placeholder="请输入应结算金额"
                style="width: 100%"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col v-if="false && showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>

        <div class="table-layout-custom">
          <table-layout
            style="height: 470px"
            ref="tableLayout"
            :tree-props="treeProps"
            :search-props="searchProps"
            :table-props="tableProps"
            @tree-init="initTreeData"
            @init-params="initParams"
            @tree-select="onTreeSelect"
            @search-submit="onSearchSubmit"
            @table-change="onTableChange"
            @table-expand="onTableExpand"
            @search-reset="onSearchReset"
          >
            <template #currentSettlement="{ record }">
              <a-input-number
                :disabled="!tableProps.rowSelection.selectedRowKeys.includes(record.id) || formDisabled"
                v-model="record.currentSettlement"
                autocomplete="off"
                @change="handCurrentSettlementChange"
                :placeholder="tableProps.rowSelection.selectedRowKeys.includes(record.id) ? '请输入' : ''"
              ></a-input-number>
            </template>
          </table-layout>
        </div>
        <a-divider class="divider" />
        <!-- 发票信息 -->
        <a-row>
          <div class="right-header mb-20">
            <img class="icon" src="@/assets/custom/title-icon.svg" />
            <span class="title">发票信息</span>
          </div>
          <a-col :span="8">
            <a-form-item label="发票金额(元)" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                autocomplete="off"
                v-decorator="['invoiceMoney']"
                placeholder="请输入发票金额"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-divider class="divider" />
        <!-- 支付信息 -->
        <a-row>
          <div class="right-header mb-20">
            <img class="icon" src="@/assets/custom/title-icon.svg" />
            <span class="title">支付信息</span>
          </div>
          <a-col :span="8">
            <a-form-item label="支付金额(元)" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number
                autocomplete="off"
                v-decorator="['payMoney']"
                placeholder="请输入支付金额"
                style="width: 100%"
                @change="handlePayMoneyChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="支付比例(%)" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                placeholder="请输入结算占比"
                :trigger-change="true"
                style="width: 100%"
                disabled
                v-decorator="['percent', {}]"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="备注"
              :labelCol="{
                xs: { span: 24 },
                sm: { span: 3 },
              }"
              :wrapperCol="{
                xs: { span: 24 },
                sm: { span: 21 },
              }"
            >
              <a-textarea
                rows="4"
                autocomplete="off"
                v-decorator="['remark', validatorRules.remark]"
                placeholder="请输入"
              ></a-textarea>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="附件"
              :labelCol="{
                xs: { span: 24 },
                sm: { span: 3 },
              }"
              :wrapperCol="{
                xs: { span: 24 },
                sm: { span: 21 },
              }"
            >
              <j-upload v-decorator="['file', validatorRules.file]" name="file" :disabled="formDisabled" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import TableLayout from '@/components/tableLayout/TableLayout.vue'
import { pick, cloneDeep } from 'lodash'

export default {
  name: 'SettlementForm',
  mixins: [JeecgTreeListMixin],
  components: { TableLayout },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      dataCache: {}, //用来缓存用户的结算数据 key 是 contractInfoDetailId value 是结算数据
      batch: '',
      disableMixinCreated: true,
      contractId: '',
      treeProps: {
        selectedKeys: [],
        expandedKeys: [],
        defaultExpandedKeys: [],
        treeData: [],
        treeField: 'code',
        isSelectParentNodes: true, // 父节点不可以被选中
        defaultSelectedKeys: [], // 存储选中的节点
        replaceFields: {
          key: 'code',
          title: 'name',
        },
      },
      tableProps: {
        rowSelection: {
          selectedRowKeys: [], // 用于控制哪些行被勾选
          onChange: this.onSelectChange, // 这个处理函数应该在 methods 中定义
          getCheckboxProps: (record) => ({
            props: {
              disabled: !!record.hasChild || this.formDisabled, 
              name: record.name,
            },
            // name属性是你数据中的特定字段，你可以根据实际情况替换
          }),
        },
        checkable: true,
        loading: false,
        dataSource: [],
        ipagination: false,
        columns: [
          {
            title: '编号',
            dataIndex: 'code',
            align: 'left',
            width: 100,
          },
          {
            title: '名称',
            dataIndex: 'name',
            align: 'center',
          },
          {
            title: '合同价额(元)',
            dataIndex: 'totalMoney',
            align: 'center',
            width: 100,
          },
          {
            title: '已结算金额(元)',
            dataIndex: 'settledMoney',
            align: 'center',
            width: 150,
          },
          {
            title: '本次结算金额(元)',
            dataIndex: '',
            scopedSlots: { customRender: 'currentSettlement' },
            align: 'center',
            width: 150,
          },
        ],
        expandedRowKeys: ['1'], // 用于控制哪些行展开
      },

      searchProps: {
        formModel: {
          name: '',
        },
        formItems: [{ key: 'name', label: '名称' }],
        labelCol: { style: { width: '45px' } },
      },

      contractList: [],
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        contractName: {
          rules: [{ required: true, message: '请输入合同名称!' }],
        },
        contractId: {
          rules: [{ required: true, message: '请选择合同' }],
        },
        contractCode: {
          rules: [{ required: true, message: '请输入合同编号!' }],
        },
        settleName: {
          rules: [{ required: true, message: '请输入结算名称!' }],
        },
        file: {
          rules: [{}],
        },
        content: {
          rules: [{ max: 200, message: '备注不能超过200个字' }],
        },
      },
      url: {
        contractlist: '/settlement/contractInfo/list',
        list: '/settlement/settlement/queryContractInfoSettlement',
        add: '/settlement/settlement/add',
        edit: '/settlement/settlement/edit',
        queryById: '/settlement/settlement/queryById',
        treeData: '/settlement/settlement/getTreeData',
        listEdit: '/settlement/settlement/queryContractInfoSettlement',
        batchInfo: '/settlement/settlement/getBatchSettlementInfo',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        return this.formData.disabled !== false
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    handCurrentSettlementChange() {
      this.updateCache() //先更新表单,同步当前页面选中的内容
      console.log('this.dataCache', this.dataCache)
      let totalMoney = 0
      Object.keys(this.dataCache).forEach((key) => {
        console.log(this.dataCache[key])
        totalMoney += this.dataCache[key].currentSettlement
      })
      this.form.setFieldsValue({
        totalMoney: totalMoney,
      })
      this.handlePayMoneyChange()
    },
    handlePayMoneyChange() {
      this.$nextTick(() => {
        let payMoney = this.form.getFieldValue('payMoney') ? this.form.getFieldValue('payMoney') : 0
        let contractMoney = this.form.getFieldValue('contractMoney')
        console.log(contractMoney)
        if (contractMoney) {
          this.form.setFieldsValue({
            percent: ((payMoney / contractMoney) * 100).toFixed(2),
          })
        }
      })
    },
    getNowString() {
      let date = new Date()
      let year = date.getFullYear()
      let month = ('0' + (date.getMonth() + 1)).slice(-2)
      let day = ('0' + date.getDate()).slice(-2)
      let hour = ('0' + date.getHours()).slice(-2)
      let minute = ('0' + date.getMinutes()).slice(-2)
      let second = ('0' + date.getSeconds()).slice(-2)
      return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second
    },
    /**
     * 更新用户勾选项的缓存
     */
    updateCache() {
      for (let index = 0; index < this.tableProps.dataSource.length; index++) {
        let element = this.tableProps.dataSource[index]
        if (this.tableProps.rowSelection.selectedRowKeys.includes(element.id)) {
          if (!element.currentSettlement) {
            element.currentSettlement = 0.0
          }
          element.contractId = this.model.contractId //总合同的id
          this.dataCache[element.id] = {
            contractId: this.model.contractId,
            contractInfoDetailId: element.id,
            code: element.code,
            currentSettlement: element.currentSettlement,
            id: element.settlementId,
            batch: this.model.batch,
          }
        } else {
          delete this.dataCache[element.id]
        }
      }
    },
    onSearchReset(params) {
      this.updateCache()
      console.log('onSearchReset', params, this.tableProps.rowSelection.selectedRowKeys)
      this.searchProps.formModel.name = null
      this.queryParam.code = this.treeProps.selectedKeys[0]
      this.searchQuery()
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.tableProps.rowSelection.selectedRowKeys = selectedRowKeys
      console.log('onSelectedRowKeysChange', selectedRowKeys) //[1,2,3,4,5]
      this.tableProps.dataSource.forEach((element) => {
        let elementIndex = selectedRowKeys.findIndex((item) => element.id === item)
        let needChecked = elementIndex >= 0
        if (element.hasChild === 1) {
          if (needChecked) {
            selectedRowKeys.splice(elementIndex)
            this.$message.warning('请勿填报有子项的记录')
            element.checked = false //如果有子节点，不允许勾选
          }
        } else {
          element.checked = needChecked
        }
      })
      this.tableProps.rowSelection.selectedRowKeys = selectedRowKeys
      console.log('onSelectChange===111111111', selectedRowKeys, selectedRows)
      // 可能还需要其他处理逻辑...
      this.handCurrentSettlementChange()
    },
    loadDataAfter(res) {
      console.log('loadDataAfter', this.tableProps.rowSelection.selectedRowKeys)
      this.tableProps.dataSource = res.result.records || res.result || []
      Object.keys(this.dataCache).forEach((key) => {
        let item = this.tableProps.dataSource.find((item) => item.id === key)
        if (item) {
          item.currentSettlement = this.dataCache[key].currentSettlement
        }
      })
      this.handCurrentSettlementChange()
    },
    initTreeData() {},
    onSelectedRowKeysChange(keys) {},
    handleChangeContract(contractId) {
      if (!this.model.batch) {
        this.dataCache = []
        this.tableProps.rowSelection.selectedRowKeys = [] //切换合同时清除已经选中的
      } //编辑模式
      console.log('handleChangeContract', contractId)
      if (contractId) {
        this.model.contractId = contractId
        console.log('this.model.contractId', this.model.contractId)
        //找出对应的填报记录 读取已结算金额
        this.queryParam.contractId = contractId
        let contract = this.contractList.find((element) => {
          return element.id === contractId
        })
        console.log('contract', contract)
        if (contract) {
          //找到指定的合同id之后
          this.$nextTick(() => {
            this.form.setFieldsValue({ percent: 0, totalMoney: 0, contractMoney: contract.totalMoney })
          })
          //如果包含batch信息这里加载到当前合同 在这个batch的所有提交记录 同步到 this.dataCache中
          this.getTreeData(contractId)
        }
      }
    },

    getTreeData(contractId, expandedKeys, selectedKeys) {
      getAction(this.url.treeData, { contractId: contractId }).then((res) => {
        if (res.success) {
          this.$nextTick(() => {
            this.treeProps.treeData.length = 0
            res.result.forEach((element) => {
              this.treeProps.treeData.push(element)
            })
            if (res.result.length > 0) {
              //如果设置了 展开项和 选中项,则自动展开和选中,并触发一次搜索
              if (expandedKeys && selectedKeys && expandedKeys.length > 0 && selectedKeys.length > 0) {
                expandedKeys && (this.treeProps.expandedKeys = expandedKeys)
                selectedKeys && (this.treeProps.selectedKeys = selectedKeys)
                console.log('设置展开选中', this.treeProps.expandedKeys, this.treeProps.selectedKeys)
                this.queryParam.code = selectedKeys[0]
                this.queryParam.batch = this.model.batch
                this.loadData(1)
              } else {
                if (!this.model.batch) {
                  //新增的情况, 此处暂定不自动展开
                  // const { expandedKeys, selectedKeys } = this.getExpandKeysAndSelectedKeys(res.result)
                  // this.treeProps.expandedKeys = expandedKeys
                  // this.treeProps.selectedKeys = selectedKeys
                  // console.log('设置展开选中', this.treeProps.expandedKeys, this.treeProps.selectedKeys)
                  // this.queryParam.code = selectedKeys[0]
                  // this.queryParam.batch = this.model.batch
                  // this.loadData(1)
                }
              }
            }
            console.log('treeData', this.treeProps.treeData)
          })
        }
      })
    },
    getExpandKeysAndSelectedKeys(obj) {
      //如果第一项有子项,展开第一项,选中它的第一个子项
      //如果第一项没有子项,则选中第一项, 不用展开
      let expandedKey = ''
      let selectedKey = ''
      if (obj.length === 0) {
        return {
          expandedKeys: [],
          selectedKeys: [],
        }
      }
      if (obj[0].children.length > 0) {
        expandedKey = obj[0].code
        selectedKey = obj[0].children[0].code
        return {
          expandedKeys: [expandedKey],
          selectedKeys: [selectedKey],
        }
      } else {
        //没有子项  选中第一个不用展开
        selectedKey = obj[0].code
        return {
          expandedKeys: [],
          selectedKeys: [selectedKey],
        }
      }
    },

    loadBatch(contractId, batch) {
      getAction(this.url.batchInfo, { contractId: contractId, batch: batch }).then((res) => {
        if (res.success) {
          this.dataCache = {}
          this.tableProps.rowSelection.selectedRowKeys = []
          res.result.forEach((detail) => {
            this.dataCache[detail.id] = {
              contractId: detail.contractId,
              contractInfoDetailId: detail.id,
              code: detail.code,
              currentSettlement: detail.currentSettlement,
              id: detail.settlementId,
            }
            this.tableProps.rowSelection.selectedRowKeys.push(detail.id)
          })
          console.log(
            'loadBath',
            this.dataCache,
            this.tableProps.rowSelection.selectedRowKeys,
            this.tableProps.dataSource
          )
        }
      })
    },
    loadContractList(contractId = null) {
      getAction(this.url.contractlist).then((res) => {
        if (res.success) {
          this.contractList = res.result.records
          console.log('contractList', this.contractList)
          this.contractList.forEach((element) => {
            element.info = element.name + '-' + element.code
          })
          if (contractId) {
            this.handleChangeContract(contractId)
          }
        }
      })
    },

    onTreeSelect(selectedKeys, e) {
      console.error('handleTreeSelect', selectedKeys, e)
      this.treeProps.selectedKeys = selectedKeys.selectedKeys
      console.error('handleTreeSelect1', this.tableProps.rowSelection.selectedRowKeys)
      this.queryParam.batch = this.model.batch
      this.updateCache()
      this.loadData(1)
      // 树形结构选择响应事件
    },
    onSearchSubmit(params) {
      this.updateCache()
      console.log('test-onSearchSubmit', params)
      this.queryParam.batch = this.model.batch
      this.queryParam.code = this.treeProps.selectedKeys[0]
      this.loadData(1)
    },
    onTableChange(params) {
      console.error('test-onTableChange', params)
      this.onSearchSubmit(params)
    },
    initParams(mergeParams) {
      console.error('mergeParams', mergeParams)
      this.queryParam = mergeParams.formModel
      this.queryParam.code = mergeParams.params.code
      this.queryParam.contractId = this.model.contractId
      this.queryParam.batch = this.model.batch
    },
    add() {
      this.edit({})
    },
    edit(recordData) {
      this.dataCache = [] //每次打开表单必须清空缓存
      let record = cloneDeep(recordData) //先克隆对象
      this.model = Object.assign({}, record)
      console.log('record', record)
      //下面处理tableLayout的树和表格
      this.$set(this.searchProps.formModel, 'name', '')
      this.tableProps.dataSource.length = 0
      this.tableProps.rowSelection.selectedRowKeys = []
      this.treeProps.selectedKeys = []
      this.treeProps.expandedKeys = []
      this.treeProps.treeData = [
        {
          code: undefined,
          name: '请选择一条合同',
          children: [],
        },
      ]
      this.form.resetFields()
      console.log('record.contractId', record.contractId)
      if (!record.contractId) {
        //新增
        this.loadContractList()
      } else {
        let expandedKey = '',
          selectedKey = ''
        let currentCode = record.code
        if (currentCode.indexOf('.') > 0) {
          let codeArr = currentCode.split('.')
          expandedKey = codeArr[0]
          selectedKey = codeArr[0] + '.' + codeArr[1]
        } else {
          selectedKey = currentCode
        }
        //编辑
        this.$nextTick(() => {
          this.form.setFieldsValue({
            contractName: record.contractInfo.name,
          })
        })
        this.queryParam.contractId = record.contractId
        this.loadBatch(record.contractId, record.batch)
        this.loadContractList(record.contractId)
        console.log('需要展开和选中的code,expandedKey,selectedKey', expandedKey, selectedKey)
        this.getTreeData(record.contractId, [expandedKey], [selectedKey])
      }
      //增加模式的 适用空的树
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(
            this.model,
            'contractName',
            'contractCode',
            'settleName',
            'currentSettlement',
            'settledMoney',
            'totalMoney',
            'settleDate',
            'file',
            'remark',
            'payMoney',
            'invoiceMoney'
          )
        )
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          let formData = Object.assign(this.model, values)
          if (formData.payMoney !== formData.totalMoney && !formData.remark) {
            that.$message.warning('支付金额和应结算金额不一致，请填写备注说明差别原因')
            return
          }
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit + '?contractId=' + this.model.contractId + '&batch=' + this.model.batch
            method = 'put'
          }

          this.updateCache() //先更新表单,同步当前页面选中的内容,然后整体更新 结算名,结算时间
          console.log('表单提交数据', formData)
          let data = []
          let dateStr = this.getNowString()
          console.log('this.dataCache', this.dataCache)
          Object.keys(this.dataCache).forEach((key) => {
            this.dataCache[key].settleDate = dateStr //统一更新结算时间
            this.dataCache[key].settleName = formData.settleName //统一更新结算名
            this.dataCache[key].file = formData.file
            this.dataCache[key].invoiceMoney = formData.invoiceMoney
            this.dataCache[key].remark = formData.remark
            this.dataCache[key].payMoney = formData.payMoney
            data.push(this.dataCache[key])
          })
          console.log('data', data)
          // data[0].invoiceMoney = formData.invoiceMoney;
          // data[0].remark = formData.remark;
          // data[0].payMoney = formData.payMoney;
          httpAction(httpurl, data, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(
          row,
          'contractName',
          'contractCode',
          'settleName',
          'currentSettlement',
          'settledMoney',
          'totalMoney',
          'settleDate',
          'file',
          'remark',
          'payMoney',
          'invoiceMoney'
        )
      )
    },
  },
}
</script>
<style lang="less" scoped>
.line {
  font-size: 17px;
  margin-bottom: 15px;
  margin-top: 15px;
  border-bottom: 1px solid #bebebe;
  width: 100%;
}
.right-header {
  display: flex;
  align-items: center;
  width: 100%;
  .icon {
    width: 14px;
    height: 14px;
    margin-right: 5px;
  }
  .title {
    font-size: 16px;
    color: #1D2129;
    line-height: 28px;
    font-weight: 550;
    flex: auto;
  }
}
.table-layout-custom {
  background-color: #ececec;
  padding: 10px;
}
.divider {
  // margin: 12px 0;
  margin: 15px 0;
}
</style>
