<template>
  <div class='list-page settlement-list'>
    <table-layout
      :rightTitle='rightTitle'
      :search-props='searchProps'
      :table-props='tableProps'
      @init-params='initParams'
      @search-submit='searchQuery'
      @table-change='onTableChange'
    >
    </table-layout>
    <modal ref='modalForm' @ok='modalFormOk'></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { deleteAction } from '@/api/manage'

export default {
  name: 'SettlementList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal
  },
  data() {
    return {

      rightTitle: '结算列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          contractName: null,
          settleName: null,
          settleDate: null
        },
        formItems: [
          { key: 'contractName', label: '合同名称', type: 'list', dictCode: 'contract_info,name,name' },
          { key: 'settleName', label: '结算名称', type: 'text' },
          {
            key: 'settleDate',
            label: '结算日期',
            type: 'datetime_range',
            keyParams: ['startTime', 'endTime'],
            format: 'YYYY-MM-DD',
            showTime: false

            // colConfig: { sm: 24, md: 12, lg: 8, xl: 12 },
            // labelCol: {
            //   xs: { span: 24 },
            //   sm: { span: 4 },
            // },
            // wrapperCol: {
            //   xs: { span: 24 },
            //   sm: { span: 20 },
            // },
          }
        ]
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            }
          },
          {
            title: '合同名称',
            align: 'center',
            dataIndex: '',
            customRender: (text, record, index) => {
              return record.contractInfo.name
            }
          },
          {
            title: '合同金额(元)',
            align: 'center',
            dataIndex: '',
            customRender: (text, record, index) => {
              return record.contractInfo.totalMoney
            }
          },
          {
            title: '结算名称',
            align: 'center',
            dataIndex: 'settleName'
          },
          {
            title: '应结算金额(元)',
            align: 'center',
            dataIndex: 'batchTotal'
          },
          {
            title: '发票金额(元)',
            align: 'center',
            dataIndex: 'invoiceMoney'
          },
          {
            title: '支付金额(元)',
            align: 'center',
            dataIndex: 'payMoney'
          },
          {
            title: '' +
              '支付比例(%)',
            align: 'center',
            dataIndex: '',
            customRender: (text, record, index) => {
              if (!record.contractInfo || record.payMoney == null) {
                return '-'
              }
              let percent = record.payMoney / record.contractInfo.totalMoney
              console.log('percent', percent)
              return (percent * 100).toFixed(2)
            }
          },
          {
            title: '结算日期',
            align: 'center',
            dataIndex: 'settleDate',
            customRender: (text) => {
              return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail
          },
          {
            text: '编辑',
            handler: this.handleEdit
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete
          }
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd
          }
        ]
      },

      url: {
        list: '/settlement/settlement/list',
        delete: '/settlement/settlement/delete',
        deleteBatch: '/settlement/settlement/deleteBatch',
        exportXlsUrl: '/settlement/settlement/exportXls',
        importExcelUrl: 'settlement/settlement/importExcel'
      }
    }
  },
  created() {
  },
  computed: {},
  watch: {},
  methods: {
    handleDelete(record) {
      let that = this
      console.log('handleDelete', record)
      let batch = record.batch
      this.$confirm({
        title: '提醒',
        content: '确认要删除吗?',
        okText: '确认',
        cancelText: '取消',
        onOk() {
          deleteAction(that.url.delete, { batch: record.batch }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        },
        onCancel() {
        }
      })
    },

    initParams(mergeParams) {
      this.queryParam ={...mergeParams.formModel}
      console.log('initParams', this.queryParam)
      delete this.queryParam.settleDate

    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
