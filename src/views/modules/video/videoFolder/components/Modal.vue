<template>
  <j-modal
    :title='title'
    :width='width'
    :visible='visible'
    :confirmLoading='confirmLoading'
    :maskClosable='false'
    :destroyOnClose='true'
    switchFullscreen
    @ok='handleOk'
    @cancel='handleCancel'
    cancelText='关闭'>
    <a-spin :spinning='confirmLoading'>
      <j-form-container :disabled='disableSubmit'>

        <a-form :form='form' slot='detail'>
          <a-col :span='24' v-if='!!model.id'>
            <a-form-item label='当前目录名' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input disabled v-bind:value='currentName'>
                autocomplete='off'>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24' v-if='!!model.id'>
            <a-form-item label='新目录名' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['newName', validatorRules.newName]"
                       autocomplete='off'></a-input>
            </a-form-item>
          </a-col>

          <a-col :span='24' v-if='!model.id'>
            <a-form-item label='上级目录' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input disabled v-bind:value='parentName' placeholder='请输入目录名称'></a-input>
            </a-form-item>
          </a-col>
          <a-col :span='24' v-if='!model.id'>
            <a-form-item label='目录名称' :labelCol='labelCol' :wrapperCol='wrapperCol'>
              <a-input v-decorator="['name', validatorRules.name]" placeholder='请输入目录名称'></a-input>
            </a-form-item>
          </a-col>


        </a-form>
      </j-form-container>
    </a-spin>
  </j-modal>
</template>

<script>

import { getAction, httpAction } from '@/api/manage'
import { pick, cloneDeep } from 'lodash'
import { validateDuplicateValue } from '@/utils/util'
import * as api from '@api/api'

export default {
  name: 'VideoFolderModal',
  components: {},
  data() {
    return {
      pid: '',
      parentName: '',
      currentName: '',
      form: this.$form.createForm(this),
      title: '操作',
      width: 800,
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 19 }
      },

      confirmLoading: false,
      validatorRules: {
        name: {
          rules: [
            { required: true, message: '请输入目录名称!' }
          ]
        },
        newName: {
          rules: [
            { required: true, message: '请输入新目录名!' },
            {
              validator: (rule, value, callback) => {
                this.validateSameLayerDuplicateValue(this.model.id, value, callback)
              }
            }
          ]
        }
      },
      url: {
        add: '/video/videoFolder/add',
        edit: '/video/videoFolder/edit',
        checkDuplicateSameLayer: '/video/videoFolder/checkDuplicateSameLayer'
      },
      expandedRowKeys: [],
      pidField: '',
      disableSubmit: false // 是否禁止编辑
    }
  },
  created() {
  },
  methods: {
    validateSameLayerDuplicateValue(id, name, callback) {
      if (id) {
        if (!name) {
          return callback()
        }
        let params = { id, name }
        getAction(this.url.checkDuplicateSameLayer, params).then(res => {
          res['success'] ? callback() : callback(res['message'])
        }).catch(err => {
          callback(err.message || err)
        })
      } else {
        callback(new Error('请提供正确的Id'))
      }
    },
    add(obj) {
      this.edit(obj)
    },


    edit(oldRecord) {
      let record = cloneDeep(oldRecord)
      this.parentName = record.parentName
      this.currentName = record.name
      this.pid = record.pid
      this.form.resetFields()
      this.model = Object.assign({}, record)
      console.log('edit', this.model)
      this.visible = true
      this.$nextTick(() => {
        if (!this.model.id) {
          this.form.setFieldsValue('parentName', record.parentName)
        }
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let old_pid = this.model[this.pidField]
          let formData = Object.assign(this.model, values)
          let new_pid = this.model[this.pidField]
          if (this.model.id && this.model.id === new_pid) {
            that.$message.warning('父级节点不能选择自己')
            that.confirmLoading = false
            return
          }
          console.log('表单提交数据', formData)
          if (!!this.model.id) {
            formData['id'] = this.model.id
            formData['name'] = formData.newName
            delete formData.newName
          }
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              this.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
            that.close()
          })
        }

      })
    },
    handleCancel() {
      this.close()
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'name'))
    },
    submitSuccess(formData, flag) {
      if (!formData.id) {
        let treeData = this.$refs.treeSelect.getCurrTreeData()
        this.expandedRowKeys = []
        this.getExpandKeysByPid(formData[this.pidField], treeData, treeData)
        this.$emit('ok', formData, this.expandedRowKeys.reverse())
      } else {
        this.$emit('ok', formData, flag)
      }
    },
    getExpandKeysByPid(pid, arr, all) {
      if (pid && arr && arr.length > 0) {
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].key == pid) {
            this.expandedRowKeys.push(arr[i].key)
            this.getExpandKeysByPid(arr[i]['parentId'], all, all)
          } else {
            this.getExpandKeysByPid(pid, arr[i].children, all)
          }
        }
      }
    }


  }
}
</script>