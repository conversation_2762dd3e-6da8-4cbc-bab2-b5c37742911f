<template>
  <div class='list-page video-folder-list'>
    <table-layout
      ref='tableLayout'
      :rightTitle='rightTitle'
      :table-props='tableProps'
      @search-submit='searchQuery'
      @table-change='onTableChange'
      @table-expand='onTableExpand'


      @init-params='initParams'
    >

      <template #action='{record}'>
        <a @click='handleEdit(record)'>编辑</a>
        <a-divider type='vertical' />
        <a :disabled='!record.upEnable' @click='handleMoveUp(record.id)'>上移</a>
        <a-divider type='vertical' />
        <a :disabled='!record.downEnable' @click='handleMoveDown(record.id)'>下移</a>
        <a-divider type='vertical' />
        <a @click='handleAddSub(record)'>新增下级</a>
        <a-divider type='vertical' />
        <a :disabled='record.pid==="0"' @click='handleAddBrother(record)'>新增同级</a>
        <a-divider type='vertical' />
        <!--        <a-popconfirm title='确定删除吗?' @confirm='() => handleDelete(record.id)' placement='topLeft'>-->
        <a type='danger' :disabled='record.pid==="0"' :class="record.pid === '0' ? 'grayText' : 'redText'"
           @click='handleDelete(record.id)'>删除</a>
        <!--        </a-popconfirm>-->
      </template>

    </table-layout>
    <modal ref='modalForm' @ok='modalFormOk'></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'

export default {
  name: 'VideoFolderList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal
  },
  data() {
    return {
      isorter: {
        column: 'sortNo',
        order: 'asc'
      },
      rightTitle: '影像目录列表',
      // 搜索组件的props
      // searchProps: {
      //   formModel: {},
      //   formItems: []
      // },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: false,
        columns: [
          {
            title: '序号',
            dataIndex: 'path',
            key: 'rowIndex',
            width: 300,
            align: 'left'
          },
          {
            title: '目录名称',
            align: 'center',
            dataIndex: 'name'

          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 350,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons1: [
          {
            text: '查看',
            handler: this.handleDetail
          },
          {
            text: '编辑',
            handler: this.handleEdit
          },

          {
            text: '上移',
            handler: this.handleMoveUp,
            disabled: (record) => {
              return !record.upEnable
            }
          },
          {
            text: '下移',
            handler: this.handleMoveDown,
            disabled: (record) => {
              return !record.downEnable
            }
          },
          {
            text: '新增下级',
            handler: this.handleAddSub
          },
          {
            text: '新增同级',
            handler: this.handleAddBrother
          },
          {
            text: '删除',
            handler: this.handleDelete,
            type: 'danger'
          }
        ],
        expandedRowKeys: [] // 用于控制哪些行展开
      },
      url: {
        list: '/video/videoFolder/rootList',
        childList: '/video/videoFolder/childList',
        getChildListBatch: '/video/videoFolder/getChildListBatch',
        delete: '/video/videoFolder/delete',
        deleteBatch: '/video/videoFolder/deleteBatch',
        exportXlsUrl: '/video/videoFolder/exportXls',
        importExcelUrl: 'video/videoFolder/importExcel',

        moveUp: 'video/videoFolder/moveUp',
        moveDown: 'video/videoFolder/moveDown'


      }
    }
  },
  created() {
  },
  computed: {},
  watch: {},
  methods: {
    expandAll() {
      const keys = []
      const collectKeys = (data) => {
        data.forEach((item) => {
          keys.push(item.id)
          if (item.children && item.children.length) {
            collectKeys(item.children)
          }
        })
      }
      collectKeys(this.tableData)
      this.expandedKeys = keys
    },

    loadDataAfter(res) {
      let that = this
      if (Number(res.result.total) > 0) {
        that.tableProps.dataSource = that.getDataByResult(res.result.records)
        that.calcPath(that.tableProps.dataSource)
        let rootId = that.tableProps.dataSource[0].id
        if (!that.tableProps.expandedRowKeys.includes(rootId)) {
          that.tableProps.expandedRowKeys.push(rootId)
        }
        return that.loadDataByExpandedRows(that.tableProps.dataSource)
      } else {
        that.tableProps.dataSource = []
      }
    },
    // 处理序号
    onTableExpandAfter(dataList) {
      this.calcPath(dataList, '')
    },

    getDataByResult(result) {
      if (result) {
        return result.map((item) => {
          // 判断是否标记了带有子节点
          if (item[this.hasChildrenField] === '1') {
            let loadChild = { id: item.id + '_loadChild', name: 'loading...', isLoading: true }
            item.children = [loadChild]
          }
          return item
        })
      }
    },
    loadDataByExpandedRowsAfter(dataList) {
      console.log('loadDataByExpandedRowsAfter1', dataList)
      this.calcPath(dataList, '')
      console.log('loadDataByExpandedRowsAfter2', dataList)
    },
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
    handleEdit(record) {
      console.log('编辑', record)
      this.$refs.modalForm.title = '编辑'
      this.$refs.modalForm.disableSubmit = false
      this.$refs.modalForm.edit(record)
    },
    handleAdd() {
      let target = this.findNodeById(this.tableProps.dataSource, '0')
      let args = {
        pid: target.id,
        parentName: target.name
      }
      this.$refs.modalForm.add(args)
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.disableSubmit = false
    },
    handleAddSub(record) {
      let target = this.findNodeById(this.tableProps.dataSource, record.id)
      let args = {
        pid: target.id,
        parentName: target.name
      }
      this.$refs.modalForm.add(args)
      this.$refs.modalForm.title = '新增下级'
      this.$refs.modalForm.disableSubmit = false
    },
    handleAddBrother(record) {
      console.log(this.tableProps.expandedRowKeys)
      console.log('handleAddBrother', record)
      let target = this.findNodeById(this.tableProps.dataSource, record.pid)
      console.log('handleAddBrother', target)
      let args = {
        pid: target.id,
        parentName: target.name
      }
      this.$refs.modalForm.add(args)
      this.$refs.modalForm.title = '新增同级'
      this.$refs.modalForm.disableSubmit = false
    },
    findNodeById(data, targetId) {
      for (let j = 0; j < data.length; j++) {
        let item = data[j]
        if (item.id === targetId) {
          return item
        }
        if (item.children) {
          let obj = this.findNodeById(item.children, targetId)
          if (obj) {
            return obj
          }
        }
      }
      return null  // 如果遍历了所有元素也没有找到，返回 null
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';

.redText {
  color: red;
}

.grayText {
  color: rgba(0, 0, 0, 0.25);
}
</style>