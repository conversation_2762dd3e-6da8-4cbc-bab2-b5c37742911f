<template>
  <div class="list-page video-info-list">
    <table-layout
      :rightTitle="rightTitle"
      @init-params="initParams"
      @table-change="onTableChange"
      :tree-props="treeProps"
      :table-props="tableProps"
      @tree-select="onTreeSelected"
    >
      <template #table>
        <div class="table-header">
          <a-checkbox
            :disabled="currentCode === 'construction' || currentCode === 'dolphin'"
            type="primary"
            icon="select"
            :indeterminate="indeterminate"
            :checked="checkAll"
            class="table-header-item"
            @click="handleSelectAll"
            >全选
          </a-checkbox>
          <a-dropdown v-show="selectedItems.length > 0" style="margin-right: 8px">
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="handleDeleteBatch"><a-icon type="delete" />删除</a-menu-item>
              <a-menu-item key="2" @click="batchDownload"><a-icon type="download" />下载</a-menu-item>
            </a-menu>
            <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
          </a-dropdown>
          <!-- <a-button :disabled='(currentCode==="construction" || currentCode==="dolphin")'
                    type='danger' icon='delete' @click='handleDeleteBatch'
                    class='table-header-item'>删除
          </a-button> -->
          <a-button
            :disabled="currentCode === 'construction' || currentCode === 'dolphin'"
            type="primary"
            icon="plus"
            @click="handleAdd"
            class="table-header-item"
            >上传
          </a-button>
        </div>

        <a-row :gutter="[16, 10]" class="media-row">
          <a-col :span="6" v-for="(media, index) in mediaList" :key="media.id">
            <Media
              :fileType="media.fileType"
              :file="media.file"
              :checked="media.checked"
              :createTime="media.createTime"
              :name="media.name"
              @click-media="
                (file, fileType) => {
                  handlePreview(file, fileType, index)
                }
              "
              @change="handleChecked(media.id, $event)"
            >
              <template #checkbox>
                <a-checkbox
                  :disabled="currentCode === 'construction' || currentCode === 'dolphin'"
                  v-model="media.checked"
                  class="media-checkbox"
                  @change="onCheckChanged"
                ></a-checkbox>
              </template>
            </Media>
          </a-col>
        </a-row>
        <template>
          <div class="pagination">
            <a-pagination
              v-model="pageNo"
              :page-size-options="pageSizeOptions"
              :total="total"
              show-size-changer
              show-quick-jumper
              :default-current="pageNo"
              :page-size="pageSize"
              :show-total="(total) => `共 ${total} 个`"
              @showSizeChange="onShowSizeChange"
              @change="onPageChange"
            >
              <template slot="buildOptionText" slot-scope="props">
                <span v-if="props.value !== '50'">{{ props.value }}条/页</span>
                <span v-if="props.value === '50'">全部</span>
              </template>
            </a-pagination>
          </div>
        </template>

        <!--        <div class='pagination'>-->
        <!--          <a-pagination show-quick-jumper :default-current='pageNo' :total='total'-->
        <!--                        :show-total='total => `共 ${total} 个`'-->
        <!--                        :page-size-options='pageSizeOptions'-->
        <!--                        @showSizeChange='onShowSizeChange'-->
        <!--                        @change='onPageChange'>-->
        <!--            <template slot='buildOptionText' slot-scope='props'>-->
        <!--              <span v-if="props.value !== '50'">{{ props.value }}条/页</span>-->
        <!--              <span v-if="props.value === '50'">全部</span>-->
        <!--            </template>-->
        <!--          </a-pagination>-->
        <!--          >-->
        <!--        </div>-->
      </template>
    </table-layout>
    <!-- <a-modal :visible="preview.visable" :footer="null" width="800px" @cancel="handleOk(preview)">
      <div class="modal-content">
        <img alt="example" class="responsive-image" :src="preview.previewImageUrl" />
      </div>
    </a-modal> -->
    <a-modal :visible="preview.visable" :destroyOnClose="true" width="60%" @cancel="handleCancel" :footer="false">
      <div class="w-100pre h-500 customModal"></div>
      <template slot="title">
        <div class="flex align-center">
          <div class="line1 mr-8"></div>
          <div class="home" style="color: #444; font-weight: bolder">预览</div>
        </div>
      </template>
    </a-modal>

    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { deleteAction, getAction, getFileAccessHttpUrl } from '@api/manage'
import Media from './components/Media.vue'

export default {
  name: 'VideoInfoList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
    Media,
  },
  data() {
    return {
      checkAll: false,
      indeterminate: false,
      preview: {
        visable: false,
        previewImageUrl: '',
        fileType: 0, //0图片,1视频
      },
      pageSizeOptions: ['8', '12', '16'],
      total: 0,
      pageSize: 8,
      pageNo: 1,
      totalPage: 10,
      mediaList: [],
      selectedItems: [],
      disableMixinCreated: true,
      currentCode: null,
      treeProps: {
        isSelectParentNodes: false, // 父节点不可以被选中
        treeData: [],
        treeField: 'code',
        replaceFields: {
          key: 'id',
          title: 'name',
        },
      },
      tableProps: {
        dataSource: [],
      },
      rightTitle: '影像列表',
      isorter: {
        column: 'createTime',
        order: 'desc',
      },
      url: {
        tree: '/video/videoInfo/getTreeData',
        list: '/video/videoInfo/list',
        delete: '/video/videoInfo/delete',
        deleteBatch: '/video/videoInfo/deleteBatch',
        exportXlsUrl: '/video/videoInfo/exportXls',
        importExcelUrl: 'video/videoInfo/importExcel',
      },
      headerButtons: [
        {
          text: '模板下载',
          icon: 'download',
          handler: this.download,
          scopedSlots: { customRender: 'downloadButton' },
        },
      ],
    }
  },
  created() {
    this.loadTreeData()
  },
  computed: {},
  watch: {},
  methods: {
    setDuration() {
      this.duration = this.$refs.video.duration
    },
    updateProgress() {
      this.currentTime = this.$refs.video.currentTime
    },
    seekVideo() {
      this.$refs.video.currentTime = this.currentTime
    },
    togglePlay() {
      if (this.$refs.video.paused) {
        this.$refs.video.play()
      } else {
        this.$refs.video.pause()
      }
    },
    videoEnded() {
      this.isPlaying = false
      this.currentTime = 0
    },
    updatePlayState() {
      this.isPlaying = !this.$refs.video.paused
    },
    handlePreview(file, fileType, index) {
      this.preview.visable = true
      this.$nextTick(() => {
        let files = this.mediaList.filter((x) => x.fileType == 0)
        let urls = files.map((x) => x.file)
        this.$ImgPreview({
          multiple: true,
          nowImgIndex: index,
          imgList: urls,
          targetElement: '.customModal', //这个是需要插入的元素的类名
        })
        this.$previewRefresh()
      })
      // this.preview.previewImageUrl = getFileAccessHttpUrl(file)
    },

    handleCancel() {
      this.preview.visable = false
      this.$closeImagePreview({ way: 'closeBtn' })
    },

    handleAdd() {
      this.$refs.modalForm.edit({ folderId: this.currentCode })
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.disableSubmit = false
    },
    loadTreeData() {
      getAction(this.url.tree, {}).then((res) => {
        if (res.success) {
          this.treeProps.treeData = res.result
          this.currentCode = 'construction'
          this.handleSearch()
        } else {
          this.$message.warn(res.message)
        }
      })
    },
    initParams(mergeParams) {
      console.log('this.queryParam', mergeParams)
      this.queryParam = mergeParams.formModel
      this.queryParam.code = mergeParams.params.code
      this.currentCode = this.queryParam.code
    },
    handleChecked(id, event) {
      let media = this.mediaList.find((m) => m.id === id)
      if (media) {
        media.checked = event.target.checked
      }
      this.changeChecked()
    },
    changeChecked() {
      this.selectedItems = this.mediaList.filter((media) => media.checked)
    },
    handleDeleteBatch() {
      //先筛选出mediaList中被选中的项
      let checkedMediaList = this.mediaList.filter((media) => media.checked)
      if (checkedMediaList.length === 0) {
        this.$message.warn('请先选择要删除的项')
        return
      }
      let ids = checkedMediaList.map((m) => m.id).join(',')
      this.$confirm({
        title: '确认删除所选中数据?',
        onOk: () => {
          deleteAction(this.url.deleteBatch, { ids: ids }).then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.handleSearch()
            } else {
              this.$message.warn(res.message)
            }
          })
        },
      })
    },
    batchDownload() {
      let checkedMediaList = this.mediaList.filter((media) => media.checked)
      if (checkedMediaList.length === 0) {
        this.$message.warn('请先选择要下载的项')
        return
      }
      checkedMediaList.forEach((element) => {
        this.downloadResource(element.file, element.name)
      })
    },
    forceDownload(blob, filename) {
      const a = document.createElement('a')
      a.download = filename
      a.href = window.URL.createObjectURL(blob)
      document.body.appendChild(a)
      a.click()
      a.remove()
      window.URL.revokeObjectURL(a.href)
    },
    downloadResource(url, filename) {
      fetch(url)
        .then((response) => response.blob())
        .then((blob) => this.forceDownload(blob, filename))
        .catch((e) => console.error('出现错误!', e))
    },
    getSearchParams() {
      let params = {
        folderId: this.currentCode,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
      }
      params = Object.assign(params, this.isorter)
      return params
    },
    getFileName(path) {
      if (path.lastIndexOf('\\') >= 0) {
        let reg = new RegExp('\\\\', 'g')
        path = path.replace(reg, '/')
      }
      return path.substring(path.lastIndexOf('/') + 1)
    },

    handleSearch() {
      let url = this.url.list
      let params = this.getSearchParams()
      getAction(url, params).then((res) => {
        if (res.success) {
          let records = res.result.records
          this.total = res.result.total
          this.totalPage = res.result.pages
          records.forEach((record) => {
            record.name = this.getFileName(record.file)
            record.file = getFileAccessHttpUrl(record.file)
            record.checked = false
          })
          console.log('searchResult', records)
          this.mediaList = records
          console.log('searchResult', this.mediaList)
        } else {
          this.$message.warn(res.message)
        }
      })
    },
    onTreeSelected(params) {
      console.log('onTreeSelected', params)
      this.checkAll = false
      this.indeterminate = false
      this.pageNo = 1
      this.selectedItems = []
      this.handleSearch()
    },
    onPageChange(pageNo) {
      console.log('onPageChange', pageNo)
      this.handleSearch()
    },
    handleSelectAll(e) {
      console.log('handleSelectAll', e.target.checked)
      this.mediaList.forEach((media) => {
        media.checked = !e.target.checked
      })
      this.indeterminate = false
      this.checkAll = !e.target.checked
      this.changeChecked()
    },

    onShowSizeChange(current, pageSize) {
      this.pageSize = pageSize
      console.log('onShowSizeChange', current, pageSize, this.pageSize)
      this.handleSearch()
    },
    modalFormOk() {
      console.log('modalFormOk,表单提交完毕')
      this.handleSearch()
    },
    onCheckChanged() {
      //统计this.mediaList中checked为true的数量
      let checkedCount = 0
      this.mediaList.forEach((media) => {
        if (media.checked) {
          checkedCount++
        }
      })
      if (checkedCount <= 0) {
        this.checkAll = false
        this.indeterminate = false
      } else {
        this.indeterminate = checkedCount !== this.mediaList.length
      }
      this.changeChecked()
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';

.pagination {
  float: right;
  text-align: right; /* 将分页器对齐到右边 */

  margin-top: 22px;
  margin-bottom: 10px;
}

.table-header {
  flex-shrink: 0;
  margin-bottom: 12px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.table-header-item {
  margin-right: 12px;
}

.media-row {
  height: calc(100% - 155px);
  overflow: auto;
  padding-bottom: 5px;
}

.video {
}

.modal-content {
  width: 100%;
  max-height: 480px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.responsive-image {
  max-width: 100%;
  max-height: 480px;
  width: auto;
  height: auto;
}
::v-deep .ant-modal-close-x {
  width: 36px;
  height: 36px;
  line-height: 36px;
}
.line1 {
  width: 3px;
  height: 10px;
  background: #3254ff;
}
</style>
