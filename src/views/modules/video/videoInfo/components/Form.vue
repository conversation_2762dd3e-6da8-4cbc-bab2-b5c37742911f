<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-item
              label="影像资料"
              :labelCol="{
                xs: { span: 24 },
                sm: { span: 4 },
              }"
              :wrapperCol="{
                xs: { span: 24 },
                sm: { span: 20 },
              }"
            >

              <j-upload v-decorator="['file',validatorRules.file]" :isMultiple="true" :limit="10"></j-upload>
            </a-form-item>
            <!--            <div class='upload-content'>-->
            <!--              <div class='content-item'>-->
            <!--                <div class='title'>-->
            <!--                  <img src='@/assets/custom/image-icon.svg' class='image' />-->
            <!--                  <span class='text'>图片资料：</span>-->
            <!--                </div>-->
            <!--                <j-image-upload text='上传' v-model='fileList1' :isMultiple='true' limit='10'></j-image-upload>-->
            <!--              </div>-->

            <!--              <div class='content-item'>-->
            <!--                <div class='title'>-->
            <!--                  <img src='@/assets/custom/video-icon.svg' class='image' />-->
            <!--                  <span class='text'>视频资料：</span>-->
            <!--                </div>-->
            <!--                <j-video-upload text='上传' v-model='fileList2' :isMultiple='true' limit='1'></j-video-upload>-->
            <!--              </div>-->
            <!--            </div>-->
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'

import { pick, cloneDeep } from 'lodash'

import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'VideoInfoForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {},
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      folderId: '',
      videoExtensions: [
        '.mp4',
        '.avi',
        '.mov',
        '.mkv',
        '.wmv',
        '.flv',
        '.mpeg',
        '.mpg',
        '.m4v',
        '.3gp',
        '.webm',
        '.vob',
        '.ogv',
      ],
      imageExtensions: [
        '.jpg',
        '.jpeg',
        '.png',
        '.gif',
        '.bmp',
        '.tiff',
        '.tif',
        '.svg',
        '.webp',
        '.ico',
        '.heic',
        '.heif',
        '.raw',
        '.cr2',
        '.nef',
        '.orf',
        '.sr2',
      ],
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
        file: {
          rules: [{ required: true, message: '请上传视频或者图片!' }],
        },
      },
      url: {
        add: '/video/videoInfo/add',
        edit: '/video/videoInfo/edit',
        queryById: '/video/videoInfo/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    add(record) {
      this.edit(record)
    },
    edit(oldRecord) {
      let record = cloneDeep(oldRecord)
      this.folderId = oldRecord.folderId
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(pick(this.model, 'file'))
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    getExtension(filename) {
      var index = filename.toLowerCase().lastIndexOf('.')
      return index < 0 ? '' : filename.substr(index)
    },

    parseFiles(file) {
      //file 是json文本
      var obj = JSON.parse(file)
      var arr = []
      obj.forEach((item) => {
        arr.push(item.url)
      })
      let videos = []
      let images = []
      arr.forEach((item) => {
        let ext = this.getExtension(item)
        let index = this.videoExtensions.findIndex((item) => item === ext)
        if (index > -1) {
          videos.push(item)
        }
        index = this.imageExtensions.findIndex((item) => item === ext)
        if (index > -1) {
          images.push(item)
        }
      })
      let uploadData = []
      videos.forEach((item) => {
        uploadData.push({ file: item, folderId: this.folderId, fileType: 1 })
      })
      images.forEach((item) => {
        uploadData.push({ file: item, folderId: this.folderId, type: 0 })
      })
      return uploadData
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          // if (!this.model.id) {
          //   httpurl += this.url.add
          //   method = 'post'
          // } else {
          //   httpurl += this.url.edit
          //   method = 'put'
          // }
          console.log('表单提交数据', values)
          // {file: '[{"uid":"vc-upload-1723101466476-5","url":"http://…920x1080.jpg","size":994641,"type":"image/jpeg"}]', __ob__: Observer}
          var text = JSON.parse(values.file)
          httpurl = this.url.add
          method = 'post'
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          let uploadData = this.parseFiles(formData.file)
          // let uploadData = { files: formData.file, folderId: this.folderId }
          console.log('表单提交数据', uploadData)
          httpAction(httpurl, uploadData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'file'))
    },
  },
}
</script>
