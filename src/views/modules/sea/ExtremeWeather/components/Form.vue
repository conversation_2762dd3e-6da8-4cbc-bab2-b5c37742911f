<template>
  <a-spin :spinning="confirmLoading">
    <!-- <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="24">
            <span>{{form.warningLevel}}</span>
          </a-col>
          <a-col :span="24">
            <a-form-item label="发布时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date placeholder="请选择发布时间" v-decorator="['releaseDate']" :trigger-change="true" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="内容" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['content']" placeholder="请输入内容"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="发布来源" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['releaseSource']" placeholder="请输入发布来源"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container> -->
    <div class="text-left bolder mb-10 font-20 ">
      <img  class="icon" style="margin-right: 7px;" src="@/assets/custom/title-icon.svg"/>{{ model.title }}
      <a-tag color="#F53F3F" v-if="model.warningLevel == '红色'"> 红色预警 </a-tag>
      <a-tag color="#E7BD29" v-if="model.warningLevel == '黄色'"> 黄色预警 </a-tag>
      <a-tag color="#FF7D00" v-if="model.warningLevel == '橙色'"> 橙色预警 </a-tag>
      <a-tag color="#3254FF" v-if="model.warningLevel == '蓝色'"> 蓝色预警 </a-tag>
      <a-tag color="#E5E6EB" v-if="model.warningLevel == '白色'"> 白色预警 </a-tag>
    </div>
    <div class="text-left mb-10 font-14"><img  class="icon" style="margin-right: 7px;" src="@/assets/icon06251502.png"/>发布时间：<b>{{ model.releaseDate }}</b></div>
    <div class="text-left mb-20 font-14"><img  class="icon" style="margin-right: 7px;" src="@/assets/icon06251501.png"/>发布单位：<b>{{ model.releaseSource }}</b></div>
    <div class="text-left font-16" style="height:187px; width:100%;background-color: #efefef;padding:16px;">
      <b>{{ model.content }}</b>
      <!-- {{ model.content }} -->
      <!-- <a-textarea
          autocomplete='false'
          v-model = model.content
          :auto-size='{ minRows: 8, maxRows:10 }'
          style="background-color: #efefef;"
        ></a-textarea> -->
    </div>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'ExtremeWeatherForm',
    components: {
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/sea/extremeWeather/add",
          edit: "/sea/extremeWeather/edit",
          queryById: "/sea/extremeWeather/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      }
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'alertStatus','title','warningLevel','type','releaseDate','content','releaseSource'))
        })
      },
      //渲染流程表单数据
      showFlowData(){
        if(this.formBpm === true){
          let params = {id:this.formData.dataId};
          getAction(this.url.queryById,params).then((res)=>{
            if(res.success){
              this.edit (res.result);
            }
          });
        }
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            let formData = Object.assign(this.model, values);
            console.log("表单提交数据",formData)
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'alertStatus','title','warningLevel','type','releaseDate','content','releaseSource'))
      },
    }
  }
</script>