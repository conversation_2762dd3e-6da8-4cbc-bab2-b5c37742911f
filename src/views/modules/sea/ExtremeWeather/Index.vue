<template>
  <div class="list-page extreme-weather-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchSubmit"
      @table-change="searchSubmit"
    >
    <template #level="{ record}">
      <a-badge :count='getTitle(record)' :number-style='{ backgroundColor: getColor(record) }' />
      <!-- <a-tag color="#F53F3F" v-if="record.warningLevel == '红色'"> 红色预警 </a-tag>
      <a-tag color="#E7BD29" v-if="record.warningLevel == '黄色'"> 黄色预警 </a-tag>
      <a-tag color="#FF7D00" v-if="record.warningLevel == '橙色'"> 橙色预警 </a-tag>
      <a-tag color="#3254FF" v-if="record.warningLevel == '蓝色'"> 蓝色预警 </a-tag>
      <a-tag color="#E5E6EB" v-if="record.warningLevel == '白色'"> 白色预警 </a-tag> -->
    </template>
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'

export default {
  name: 'ExtremeWeatherList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {
      /* 排序参数 */
      isorter: {
        column: '',
        order: '',
      },
      rightTitle: '极端天气列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          releaseDate: null,
        },
        formItems: [{ key: 'releaseDate', label: '发布时间', type: 'date', format: 'YYYY-MM-DD' }],
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          // {
          //   title: '预警状态',
          //   align: 'center',
          //   dataIndex: 'alertStatus',
            
          // },
          {
            title: '发布时间',
            align: 'center',
            dataIndex: 'releaseDate',
          },
          {
            title: '预警概要',
            align: 'center',
            dataIndex: 'title',
          },
          {
            title: '预警类型',
            align: 'center',
            dataIndex: 'type',
          },
          {
            title: '预警级别',
            align: 'center',
            dataIndex: 'warningLevel',
            scopedSlots: { customRender: 'level' },
          },
          // {
          //   title: '内容',
          //   align: 'center',
          //   dataIndex: 'content',
          // },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            // fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          // {
          //     text: '编辑',
          //     handler: this.handleEdit,
          // },
          // {
          //     text: '删除',
          //     type: 'danger',
          //     handler: this.handleDelete,
          // },
        ],
        // headerButtons: [
        //     {
        //         text: '新增',
        //         icon: 'plus-circle',
        //         handler: this.handleAdd,
        //     },
        // ]
      },

      url: {
        list: '/sea/extremeWeather/list',
        delete: '/sea/extremeWeather/delete',
        deleteBatch: '/sea/extremeWeather/deleteBatch',
        exportXlsUrl: '/sea/extremeWeather/exportXls',
        importExcelUrl: 'sea/extremeWeather/importExcel',
      },
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    searchSubmit(params) {
      const formModel = { ...params.formModel }
      // delete formModel.datetimerange
      this.loadData(formModel)
    },
    getColor(record) {
      let warningLevel = record.warningLevel
      if (warningLevel == '红色') {
        return '#F53F3F'
      }else if(warningLevel == '黄色'){
        return '#E7BD29'
      }else if(warningLevel == '橙色'){
        return '#FF7D00'
      }else if(warningLevel == '蓝色'){
        return '#3254FF'
      }else if(warningLevel == '白色'){
        return '#E5E6EB'
      }else{
        return ''
      }
    },
    getTitle(record) {
      let warningLevel = record.warningLevel
      if (warningLevel == '红色') {
        return '红色预警'
      }else if(warningLevel == '黄色'){
        return '黄色预警'
      }else if(warningLevel == '橙色'){
        return '橙色预警'
      }else if(warningLevel == '蓝色'){
        return '蓝色预警'
      }else if(warningLevel == '白色'){
        return '白色预警'
      }else{
        return ''
      }

    },
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>