# Online 图表配置文档

## 一、配置单个数据源的图表

- 配置地址: `/online/graphreport`

### 具体步骤

1. 在页面中点击 `新增` 按钮
2. 在打开的弹窗中输入你的图表信息。其中，必填项有：
    - 图表名称
    - 编码（编码是唯一的）
    - X轴字段（数据源中被当做 X 轴的字段）
    - Y轴字段（数据源中被当做 Y 轴的字段）
    - 查询SQL/数据JSON
3. 其中有几个动态的内容区域，分别是：
    - 当 `数据类型` 字段选为 `JSON` 后，`查询SQL` 字段会被替换成 `数据JSON` 字段，该字段会验证你的JSON字符串格式是否正确，反之则不变
4. 配置列表字段
    - 列表字段前两项是配置 数据表格 的 `列（columns）`
        - `字段名` 是必填的，对应 `column.dataIndex`
        - `字段文本` 是对字段名的描述，对应 `column.title`，不填则不显示
    - `是否显示` 默认勾选，如果去掉勾选则不显示此列
    - `计算总计` 默认不勾选，如果勾选上则会对当前列所有的数据进行求和，如果存在非数字的内容，则拒绝计算并提示"包含非数字内容"
    - `是否查询` 默认不勾选，如果勾选上则会在图表最上方显示一个表单，用于筛选表格的数据
    - `字段类型` 默认为空，可选择查询条件表单的类型，可选值有：数值类型、日期类型、字符类型、长整型
    - `查询模式` 默认为空，可选择查询条件的筛选方式，如果选择了范围查询，则会显示两个表单，一个是开始值，一个是结束值，共同完成筛选
5. 点击右下角的`确定`按钮完成添加操作

### 使用方法

- 在 `操作` 列中，选中 `更多`，点击 `功能测试` 可以查看你配置的效果。
- 效果会根据不同的 `展示模板` 显示不同的布局

### 配置示例：JSON数据格式

假如我有一段JSON，我要将它配置成和下图一样图表，那么需要怎么做呢？

![img](http://bbs.jeecg.com/data/attachment/forum/201904/24/153759lsqquv6uioutwopt.png)

#### 第一步：准备好你需要的JSON

```json
[
    {"day": "星期一", "step": 1234, "assess": "良"},
    {"day": "星期二", "step": 1884, "assess": "优"},
    {"day": "星期三", "step": 1671, "assess": "良+"},
    {"day": "星期四", "step": 2197, "assess": "优+"},
    {"day": "星期五", "step": 1342, "assess": "中"},
    {"day": "星期六", "step": 545, "assess": "差"},
    {"day": "星期日", "step": 244, "assess": "极差"}
]
```

#### 第二步：填写JSON

点击“新增”按钮，填写一些基本信息，然后将 `数据类型` 改为 `JSON`，然后将JSON填入`数据JSON`字段中，如下图

![img](http://bbs.jeecg.com/data/attachment/forum/201904/24/152831h4di2s2zhzsiwr57.png)

#### 第三步：配置数据字段

数据字段即`X轴字段`、`Y轴字段`和`Y轴文字`。 
`X轴字段` 顾名思义，就是需要在X轴显示的字段，根据上图示例图表中我们可以发现，X轴方向显示的是星期一到星期日，而在准备的json中，`day`字段是存储星期信息的，所以我们要将 `X轴字段`处填写成`day`。
`Y轴字段` 也是如此，即对应需要在Y轴上显示的字段，这里我们填写上 `step`
`Y轴文字` 是对Y轴数据的一个解释。这里我们填上`步数`，那么就会在鼠标悬浮在图表上时直观的显示出来，如下图所示。

![img](http://bbs.jeecg.com/data/attachment/forum/201904/24/154819za9k1uz737c7nak7.png)

### 第四步：配置数据表格的列

在`列表字段`下面的表格中配置，配置示例如下

![img](http://bbs.jeecg.com/data/attachment/forum/201904/24/161336nwh2we0y5rrhw2z6.png)

这里的配置是配置数据表格的列信息，只有配置上去的字段才会被显示出来。
数据表格可以计算列的总数，当`计算总计`被勾选上之后，会在数据表格最下面显示一行“总计”，当所有的`计算总计`都没被勾选的话，那么就不会显示这一行，如果要计算总计的列中某一行包含非数字的值，那么将会计算失败，并显示错误信息（包含非数字内容）

### 第五步：提交并测试功能

点击右下角的`确定`按钮并成功保存之后，我们可以在新增加的数据行右侧点击`更多 --> 功能测试`

![img](http://bbs.jeecg.com/data/attachment/forum/201904/24/160905jbsqsgnlqo9oh624.png)

最终显示效果如下：

![img](http://bbs.jeecg.com/data/attachment/forum/201904/24/163104u1cuur0icf0700cm.png)

我们发现只有一个柱状图，而刚刚配置的数据表格并没有显示出来，这是因为`图表类型`只配置了一个柱状图。我们回到列表页面，点击编辑按钮，在`图表类型`处勾选`数据列表`，如下图所示

![img](http://bbs.jeecg.com/data/attachment/forum/201904/24/162913n1h1bth7o8co1tdo.png)

点击`确定`保存，再点击功能测试，最终显示效果如下：

![img](http://bbs.jeecg.com/data/attachment/forum/201904/24/162915izno5za14a7p5cyq.png)

至此，配置JSON数据格式的图表就已经完成了

### 配置SQL数据格式

配置SQL数据格式的图表与JSON的步骤类似，只是需要将`数据类型` 改为 `SQL`即可，在`查询SQL`处填写上你的SQL语句，填写好`X轴字段`、`Y轴字段`和`Y轴文字`点击`确定`保存即可

## 二、配置多数据源的图表

多数据源图表可以将你配置过的`单数据源图表`整合到一个页面中，并且可以进行分组、排序

- 配置地址: `/online/graphreport/templet`（暂定）

### 具体步骤

2. 点击页面中的 `新增` 按钮
3. 在打开的弹窗中输入你的图表信息。其中，必填项有：
    - 报表名称
    - 报表编码（编码是唯一的）
    - 报表风格（Tab风格、单排布局、双排布局、组合布局）
4. 图表配置
    - 图表 （必填项，选择的是你配置过的`单数据源图表`）
    - 图表类型（如果选择`不配置`，那么则应用选择的`单数据源图表`中配置的`图表类型`，如果配置了则优先显示此处配置的图表类型）
    - 组合编码（必填项，只能为数字，数字越小越往前排）
5. 点击右下角的`确定`按钮完成添加操作

### 注意事项

1. 如果`报表风格`配置成了`组合布局`，那么就会将配置的图表显示在一张图表内，并且`图表类型`只能配置成`柱状图`或`曲线图`，即使配置成了其他的类型，实际运行中也一样不会生效

### 使用方法

- 在 `操作` 列中，选中 `更多`，点击 `功能测试` 可以查看你配置的效果。
- 效果会根据不同的 `报表风格`和`组合展示风格` 显示不同的布局

### 配置示例

#### 第一步：配置图表名称、编码、风格、多图表组合等

示例配置图如下

![img](http://bbs.jeecg.com/data/attachment/forum/201904/24/164212p0zdvmdq71ev17op.png)

#### 第二步：查看效果

在新增加的数据行右侧点击`更多 --> 功能测试`

![img](http://bbs.jeecg.com/data/attachment/forum/201904/24/160905jbsqsgnlqo9oh624.png)

最终显示效果如下：

![img](http://bbs.jeecg.com/data/attachment/forum/201904/24/170233fmgumdu0gu01hgoz.png)

![img](http://bbs.jeecg.com/data/attachment/forum/201904/24/170234ijwxdh2wwhx6h6dw.png)

![img](http://bbs.jeecg.com/data/attachment/forum/201904/24/170237hyem041em70m6qua.png)

![img](http://bbs.jeecg.com/data/attachment/forum/201904/24/170238xi5eza8z5h59ds5n.png)