<template>
  <div class="list-page video-info-list">
    <table-layout
      :rightTitle="rightTitle"
      @init-params="initParams"
      @table-change="onTableChange"
      :tree-props="treeProps"
      :table-props="tableProps"
      @tree-select="onTreeSelected"
      :searchProps="searchProps"
    >
      <template #leftHeader>
        <a-tabs v-model="activeKey" @change="handleTabChange">
          <a-tab-pane :tab="item.label" v-for="item in IdentifyType" :key="item.value"> </a-tab-pane>
        </a-tabs>
      </template>
      <template v-if="mediaList.length > 0" #table>
        <a-row :gutter="[16, 10]" class="media-row">
          <a-col :span="6" v-for="(media, index) in mediaList" :key="media.id">
            <div class="picture-item">
              <div class="imgBg w-100pre h-260">
                <img
                  v-if="media.picture"
                  class="img_block w-100pre h-100pre rounded-8"
                  :src="media.picture"
                  @click="handlePreview(media, index)"
                />
              </div>
              <div class="pl-10 pt-5">
                <span class="text-0096">{{ media.pictureName }}</span>
                <span>{{ media.captureTime }}</span>
              </div>
            </div>
          </a-col>
        </a-row>
        <template>
          <div class="pagination">
            <a-pagination
              v-model="pageNo"
              :page-size-options="pageSizeOptions"
              :total="total"
              show-size-changer
              show-quick-jumper
              :default-current="pageNo"
              :page-size="pageSize"
              :show-total="(total) => `共 ${total} 个`"
              @showSizeChange="onShowSizeChange"
              @change="onPageChange"
            >
              <template slot="buildOptionText" slot-scope="props">
                <span v-if="props.value !== '50'">{{ props.value }}条/页</span>
                <span v-if="props.value === '50'">全部</span>
              </template>
            </a-pagination>
          </div>
        </template>
      </template>
      <template v-else #table>
        <a-empty />
      </template>
    </table-layout>
  </div>
</template>

<script>
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { getAction } from '@api/manage'
import { getDictItems } from '@/components/dict/JDictSelectUtil'
export default {
  name: 'AICaptrueList',
  mixins: [JeecgTreeListMixin],
  components: {},
  data() {
    return {
      checkAll: false,
      indeterminate: false,
      preview: {
        visable: false,
        previewImageUrl: '',
        fileType: 0, //0图片,1视频
      },
      pageSizeOptions: ['8', '12', '16'],
      total: 0,
      pageSize: 8,
      pageNo: 1,
      totalPage: 10,
      mediaList: [],
      selectedItems: [],
      disableMixinCreated: true,
      currentCode: null,
      treeProps: {
        treeField: 'deviceId',
        isSelectFirstChild: false,
        isSelectParentNodes: false,
        replaceFields: { title: 'name', key: 'id' },
        treeData: [],
      },
      // 表头
      searchProps: {
        formModel: {
          identifyType: '1',
        },
      },
      tableProps: {
        dataSource: [],
      },
      rightTitle: '抓拍记录',
      loading: true,
      url: {
        list: '/monitor/AICapture/list',
      },
      IdentifyType: [],
      activeKey: '1',
    }
  },
  created() {
    this.treeLoadData()
  },
  computed: {},
  watch: {},
  mounted() {
    // 获取预警类型 字典
    getDictItems('IdentifyType').then((res) => {
      console.log('IdentifyType', res)
      this.IdentifyType = res
    })
  },
  methods: {
    // tabs 切换事件
    handleTabChange() {
      // 让分页重置
      this.pageSiz = 8
      this.pageNo = 1
      this.handleSearch()
    },
    handlePreview(media, index) {
      console.log('预览第几张', index, this.mediaList, media)
      let urlData = this.mediaList.map((x) => x.picture)
      this.$hevueImgPreview({
        multiple: true,
        nowImgIndex: index,
        imgList: urlData,
      })
    },

    handleCancel() {
      this.preview.visable = false
      this.$closeImagePreview({ way: 'closeBtn' })
    },

    treeLoadData() {
      this.loading = true
      this.loadTree()
    },
    loadTree() {
      console.log('loadTreeaaaa!!!')
      getAction('/base/baseArea/list').then((response) => {
        if (response.success) {
          const arealist = response.result.records
          getAction('/base/points/list').then((resp) => {
            if (resp.success) {
              const records = resp.result
              // 处理数据 处理成树形结构
              let tree = []
              arealist.forEach((item) => {
                if (records[item.id]) {
                  item.children = records[item.id].filter((x) => x.monitorType == 'AIRecognition')
                }
              })
              let root = [
                {
                  name: '额敏风电场',
                  id: '0',
                  children: arealist,
                },
              ]
              this.treeProps.treeData = root
              this.handleSearch()
            } else {
              this.$message.warning(resp.message)
            }
          })
        } else {
          this.$message.warning(response.message)
        }
      })
    },
    initParams(mergeParams) {
      console.log('mergeParams', mergeParams)
      console.log('this.queryParam', this.queryParam)
      this.queryParam = mergeParams.formModel
      this.queryParam.code = mergeParams.params.code
      console.log('initParams', this.queryParam)
      this.currentCode = this.queryParam.code
    },
    getSearchParams() {
      // 获取当前选中的树
      console.log('获取当前选中的树', this.treeProps.selectedKeys)
      let params = {
        deviceId: this.currentCode,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        identifyType: this.activeKey,
      }
      params = Object.assign(params, this.isorter)
      return params
    },

    handleSearch() {
      let url = this.url.list
      // 获取参数,
      let params = this.getSearchParams()
      getAction(url, params).then((res) => {
        if (res.success) {
          let records = res.result.records
          this.total = res.result.total
          this.totalPage = res.result.pages
          this.mediaList = records
        } else {
          this.$message.warn(res.message)
        }
      })
    },
    onTreeSelected(params) {
      console.log('onTreeSelected', params)
      this.currentCode = params.selectedKeys[0]
      params.params.identifyType = this.activeKey
      this.pageNo = 1
      this.handleSearch()
    },
    onPageChange(pageNo) {
      console.log('onPageChange', pageNo)
      this.handleSearch()
    },
    onShowSizeChange(current, pageSize) {
      this.pageSize = pageSize
      console.log('onShowSizeChange', current, pageSize, this.pageSize)
      this.handleSearch()
    },
  },
}
</script>
<style lang="scss" scoped>
.media-row {
  height: calc(100% - 93px);
  overflow: auto;
}
.picture-item {
  padding: 10px;
  background-color: #f2f2f2;
  span {
    display: block;
  }
}
.pagination {
  float: right;
  text-align: right; /* 将分页器对齐到右边 */
  margin-top: 22px;
  margin-bottom: 10px;
}
.imgBg {
  background-image: url('../../../assets/svg/no_pictures.svg');
  background-size: 50%;
  background-position: center;
  background-repeat: no-repeat;
  box-shadow: 0px 10px 16px 0px rgba(0, 0, 0, 0.3); /* 下边的阴影 */
  border-radius: 8px;
  transition: transform 0.3s ease;
}
.imgBg:hover {
  transform: translateY(-10px);
}
</style>
