<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :maskClosable="false"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <DataForm ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></DataForm>
  </j-modal>
</template>

<script>

  import DataForm from './Form.vue'
  export default {
    name: 'DataShipInfoForm',
    components: {
      DataForm
    },
    // props:{
    //   title:{
    //     type: String,
    //     required: true
    //   }
    //
    // },
    data () {
      return {
        dataShip:{},
        title:'',
        width:896,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add (extraData) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add(extraData);
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      view (record) {
        this.disableSubmit = true
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>