<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="船舶类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag v-decorator="['shipType']" placeholder="请输入船舶类型" :disabled='true' dictCode="ship_typ_code" ></j-dict-select-tag>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="船舶名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['shipName',validatorRules.shipName]" placeholder="请输入船舶名称" 
              autocomplete="off"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="MMSI" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['mmsi',validatorRules.mmsi]" placeholder="请输入MMSI" autocomplete="off"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="船东" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['shipOwner', validatorRules.shipOwner]" placeholder="请输入船东"  autocomplete="off"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="船东联系人	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['shipLink']" placeholder="请输入船东联系人	" autocomplete="off" ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系人电话	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['linkPhone',validatorRules.linkPhone]" placeholder="请输入联系人电话" autocomplete="off" ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="目前状态	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['shipState',validatorRules.shipState]" :trigger-change="true" dictCode="ship_states" placeholder="请选择目前状态	" />
            </a-form-item>
          </a-col>
          <a-divider dashed />


          <a-col :span="12" >
            <a-form-item label="长(m)" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['shipLong']" placeholder="请输入长(m)" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" >
            <a-form-item label="宽(m)	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['shipWide']" placeholder="请输入宽(m)	" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="型深(m)	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['typeDepth']" placeholder="请输入型深(m)	" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="船籍港" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['homePort']" placeholder="请输入船籍港" autocomplete="off" ></a-input>
            </a-form-item>
          </a-col>
          <a-divider dashed />

          <a-col :span="12" v-if='showShip4'>
            <a-form-item label="是否无动力船" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="radio" v-decorator="['unpoweredShip']" :trigger-change="true" dictCode="unpowered_type" placeholder="请选择是否无动力船" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='showShip4'>
            <a-form-item label="抗浪涌情况(m)" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['resistSurgeSituation']" placeholder="请输入抗浪涌情况(m)" autocomplete="off" ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='showShip4'>
            <a-form-item label="抗风等级	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['resistWindLevel']" placeholder="请输入抗风等级	" style="width: 100%"  autocomplete="off"/>
            </a-form-item>
          </a-col>

          <a-col :span="12" v-if='showShip2'>
            <a-form-item label="定位方式	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['positionMethod']" placeholder="请输入定位方式	" autocomplete="off"></a-input>
            </a-form-item>
          </a-col>

          <a-col :span="12" v-if='shipType==1'>
            <a-form-item label="支腿长度(m)	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['legLength']" placeholder="请输入支腿长度(m)	" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==1'>
            <a-form-item label="主吊起重能力	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['mainLiftingCapacity']" placeholder="请输入主吊起重能力	" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==1'>
            <a-form-item label="主吊起吊高度(m)	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['mainLiftingHeight']" placeholder="请输入主吊起吊高度(m)	" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==1'>
            <a-form-item label="副吊起重能力	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['deputyLiftingCapacity']" placeholder="请输入副吊起重能力	" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==1'>
            <a-form-item label="副吊起吊高度(m)	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['deputyLiftingHeight']" placeholder="请输入副吊起吊高度(m)	" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==1'>
            <a-form-item label="作业水深(m)	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['operatingWaterDepth']" placeholder="请输入作业水深(m)	" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==2'>
            <a-form-item label="起重机类型	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['craneType']" :trigger-change="true" dictCode="crane_type" placeholder="请选择起重机类型	" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==2'>
            <a-form-item label="全回转起重能力	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['fullLiftingCapacity']" placeholder="请输入全回转起重能力" autocomplete="off"  ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==2'>
            <a-form-item label="固定艉吊能力(t)" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['fixedLiftingAbility']" placeholder="请输入固定艉吊能力	" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==2'>
            <a-form-item label="吊高(m)" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['hangHigh']" placeholder="请输入吊高	" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==3'>
            <a-form-item label="桩架高度(m)	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['frameHeight']" placeholder="请输入桩架高度	" style="width: 100%" autocomplete="off"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==3'>
            <a-form-item label="起重能力(t)" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['elevatingCapacity']" placeholder="请输入起重能力	" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==3'>
            <a-form-item label="最大打桩长度(m)" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['maxPileLength']" placeholder="请输入最大打桩长度	" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==3'>
            <a-form-item label="最大径桩(m)" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['maxDiameterPile']" placeholder="请输入最大径桩	" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==4'>
            <a-form-item label="吨位（t）	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['tonnage']" placeholder="请输入吨位（t）	" style="width: 100%"  autocomplete="off"/>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==4'>
            <a-form-item label="可载人数	" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['carryNumber']" placeholder="请输入可载人数	" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==5'>
            <a-form-item label="总吨（t）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['grossTon']" placeholder="请输入总吨（t）" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==5'>
            <a-form-item label="净吨（t）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['natTon']" placeholder="请输入净吨（t）" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==5'>
            <a-form-item label="吃水（m）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['draught']" placeholder="请输入吃水（m）" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==5'>
            <a-form-item label="运输船类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="select"
                                 v-decorator="['transportShipType']"
                                 placeholder="请输入运输船类型"
                                 :trigger-change="true"
                                 dictCode="transport_ship_type" ></j-dict-select-tag>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if='shipType==5'>
            <a-form-item label="航速（节）" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number v-decorator="['shipSpeed']" placeholder="请输入航速（节）" style="width: 100%" autocomplete="off" />
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="图片" :labelCol="{ xs: { span: 24 },sm: { span: 3 }}" :wrapperCol="wrapperCol">
              <j-image-upload isMultiple  v-decorator="['picture']" ></j-image-upload>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if='showShip2'>
            <a-form-item label="适用海域	" :labelCol="{ xs: { span: 24 },sm: { span: 3 }}" :wrapperCol="wrapperCol">
              <a-textarea v-decorator="['applicableSeaArea']" rows="4" placeholder="请输入适用海域	" />
            </a-form-item>
          </a-col>
          <a-col :span="24" >
            <a-form-item label="备注" :labelCol="{ xs: { span: 24 },sm: { span: 3 }}" :wrapperCol="wrapperCol">
              <a-textarea v-decorator="['remark']" rows="4" placeholder="请输入备注" />
            </a-form-item>
          </a-col>


          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import pick from 'lodash.pick'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'DataShipInfoForm',
    components: {
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        form: this.$form.createForm(this),
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
          shipName:{
            rules: [{
              required: true, message: '请输入船舶名称!',
            }],
          },
          shipState:{
            rules: [{
              required: true, message: '请选择船舶状态!',
            }],
          },
          mmsi:{
            rules: [{
              required: true, message: '请输入MMSI!',
            }],
          },
          shipOwner:{
            rules: [{
              required: true, message: '请输入船东!',
            }],
          },
          linkPhone:{
            rules: [{
              required: false,
              pattern: /^1[3456789]\d{9}$/,
              message: '手机格式不正确!',
            }],
          },
        },
        url: {
          add: "/data/dataShipInfo/add",
          edit: "/data/dataShipInfo/edit",
          queryById: "/data/dataShipInfo/queryById"
        },
        shipType:'',
        ship4: ["1","2","3","4"],
        ship2: ["1","2"]
      }
    },
    computed: {
      formDisabled(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return false
          }
          return true
        }
        return this.disabled
      },
      showFlowSubmitButton(){
        if(this.formBpm===true){
          if(this.formData.disabled===false){
            return true
          }
        }
        return false
      },
      showShip4(){
        return this.ship4.includes(this.shipType);
      },
      showShip2(){
        return this.ship2.includes(this.shipType);
      }
    },
    created () {
      //如果是流程中表单，则需要加载流程表单data
      this.showFlowData();
    },
    methods: {
      add (extData) {
        console.info("extData",extData);
        this.shipType = extData.shipType.value;
        let tranShipType = {
          "shipType":extData.shipType.value
        }
        this.edit(tranShipType);
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.shipType = record.shipType;
        console.log("this.model",record)
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'shipName','mmsi','shipOwner','shipLink','linkPhone','shipState','shipLong','shipWide','typeDepth','homePort','picture','remark','unpoweredShip','resistSurgeSituation','resistWindLevel','positionMethod','applicableSeaArea','legLength','mainLiftingCapacity','mainLiftingHeight','deputyLiftingCapacity','deputyLiftingHeight','operatingWaterDepth','craneType','fullLiftingCapacity','fixedLiftingAbility','hangHigh','frameHeight','elevatingCapacity','maxPileLength','maxDiameterPile','tonnage','carryNumber','grossTon','natTon','draught','shipType','shipSpeed','transportShipType'))
        })
      },
      //渲染流程表单数据
      showFlowData(){
        if(this.formBpm === true){
          let params = {id:this.formData.dataId};
          getAction(this.url.queryById,params).then((res)=>{
            if(res.success){
              this.edit (res.result);
            }
          });
        }
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.form.validateFields((err, values) => {
          debugger
          if (!err) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            let formData = Object.assign(this.model, values);
            console.log("表单提交数据",formData)
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }

        })
      },
      popupCallback(row){
        this.form.setFieldsValue(pick(row,'shipName','mmsi','shipOwner','shipLink','linkPhone','shipState','shipLong','shipWide','typeDepth','homePort','picture','remark','unpoweredShip','resistSurgeSituation','resistWindLevel','positionMethod','applicableSeaArea','legLength','mainLiftingCapacity','mainLiftingHeight','deputyLiftingCapacity','deputyLiftingHeight','operatingWaterDepth','craneType','fullLiftingCapacity','fixedLiftingAbility','hangHigh','frameHeight','elevatingCapacity','maxPileLength','maxDiameterPile','tonnage','carryNumber','grossTon','natTon','draught','shipType','shipSpeed','transportShipType'))
      },
    }
  }
</script>