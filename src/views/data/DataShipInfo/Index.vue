<template>
  <div class="list-page data_shif_info_list">
    <table-layout
      ref="tableLayout1"
      :search-props="searchProps"
      :rightTitle="rightTitle"
      :table-props="tableProps"
      :tree-props="treeProps"
      @tree-init="onTreeInit"
      @tree-select="onTreeSelect"
      @search-submit="onSearchSubmit"
      @table-change="onSearchSubmit"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/mixins/DeviceMixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import Modal from './components/Modal'
import { getDictItems } from '@/components/dict/JDictSelectUtil'
import { getAction } from '@/api/manage'

export default {
  name: 'DataShipInfo',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    // LeftRightLayout,
    Modal,
  },
  data() {
    return {
      disableMixinCreated: true,
      rightTitle: '船舶信息列表',
      treeProps: {
        treeField: 'shipType',
        // dictCode: 'ship_typ_code', //此处是字典采用自定义函数获取treeData的方式来实现
        isSelectFirstChild: true,
        selectedKeys: [],
        isSelectParentNodes: false,
        replaceFields: { title: 'title', key: 'value' },
        treeData: [],
      },
      // 表头
      searchProps: {
        formModel: {
          name: null,
        },
        formItems: [
          { key: 'shipName', label: '船舶名称', placeholder: '请输入船舶名称' },
          {
            key: 'shipOwner',
            label: '船东',
            placeholder: '请输入船东',
          },
        ],
      },
      tableProps: {
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '#',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: function (t, r, index) {
              return parseInt(index) + 1
            },
          },
          {
            title: '船舶名称',
            align: 'center',
            dataIndex: 'shipName',
          },
          {
            title: 'MMSI',
            align: 'center',
            dataIndex: 'mmsi',
          },
          {
            title: '船东',
            align: 'center',
            dataIndex: 'shipOwner',
          },
          {
            title: '船东联系人	',
            align: 'center',
            dataIndex: 'shipLink',
          },
          {
            title: '联系人电话	',
            align: 'center',
            dataIndex: 'linkPhone',
          },
          {
            title: '目前状态	',
            align: 'center',
            dataIndex: 'shipState_dictText',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 210,
            scopedSlots: { customRender: 'action' },
          },
        ],
        treeField: 'type',
        actionButtons: [
          {
            text: '当前位置',
            handler: this.handleCurrentPosition,
          },
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
          },
          {
            text: '删除',
            type: 'danger',
            // slot: 'deleteItem',
            handler: this.handleDelete,
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],
      },
      url: {
        list: '/data/dataShipInfo/list',
        delete: '/data/dataShipInfo/delete',
        deleteBatch: '/data/dataShipInfo/deleteBatch',
        exportXlsUrl: '/data/dataShipInfo/exportXls',
        importExcelUrl: 'data/dataShipInfo/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
      selectedKeys: [],
      // treeData: [],
      iExpandedKeys: [],
      autoExpandParent: true,
      checkStrictly: true,
      extraData: {},
    }
  },
  created() {
    this.getSuperFieldList()
    this.treeLoadData()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    getCurrentTreeId() {
      let selectedKeys = this.$refs.tableLayout1.mergeParams.selectedKeys
      return selectedKeys ? selectedKeys[0] : undefined
    },

    onTreeInit(params) {
      console.log('tree init', params)
      this.type = params.params.shipType
      console.log('加载1')
      this.loadData({ shipType: this.type })
    },
    onTreeSelect(params) {
      this.onSearchSubmit(params)
    },
    onSearchSubmit(params) {
      console.log('searchData', this.searchProps, params)
      this.queryParam = this.searchProps.formModel
      this.queryParam.shipType = params.params.shipType
      console.log('加载2')
      this.loadData(this.queryParam)
    },
    handleCurrentPosition() {},
    handleAdd() {
      if (this.getCurrentTreeId() === '-1') {
        this.$warning({ title: '提示', content: '请选择船舶类型' })
        return
      }
      console.log('xxxxxxxxxxx', { shipType: this.getCurrentTreeId() })
      this.$refs.modalForm.edit({ shipType: this.getCurrentTreeId() })
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.disableSubmit = false
    },
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'shipName', text: '船舶名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'mmsi', text: 'MMSI', dictCode: '' })
      fieldList.push({ type: 'string', value: 'shipOwner', text: '船东', dictCode: '' })
      fieldList.push({ type: 'string', value: 'shipLink', text: '船东联系人	', dictCode: '' })
      fieldList.push({ type: 'int', value: 'linkPhone', text: '联系人电话	', dictCode: '' })
      fieldList.push({ type: 'string', value: 'shipState', text: '目前状态	', dictCode: 'ship_states' })
      fieldList.push({ type: 'double', value: 'shipLong', text: '长(m)', dictCode: '' })
      fieldList.push({ type: 'double', value: 'shipWide', text: '宽(m)	', dictCode: '' })
      fieldList.push({ type: 'double', value: 'typeDepth', text: '型深(m)	', dictCode: '' })
      fieldList.push({ type: 'string', value: 'homePort', text: '船籍港', dictCode: '' })
      fieldList.push({ type: 'string', value: 'picture', text: '图片', dictCode: '' })
      fieldList.push({ type: 'string', value: 'remark', text: '备注', dictCode: '' })
      fieldList.push({ type: 'string', value: 'unpoweredShip', text: '是否无动力船', dictCode: 'unpowered_type' })
      fieldList.push({ type: 'string', value: 'resistSurgeSituation', text: '抗浪涌情况(m)', dictCode: '' })
      fieldList.push({ type: 'double', value: 'resistWindLevel', text: '抗风等级	', dictCode: '' })
      fieldList.push({ type: 'string', value: 'positionMethod', text: '定位方式	', dictCode: '' })
      fieldList.push({ type: 'string', value: 'applicableSeaArea', text: '适用海域	', dictCode: '' })
      fieldList.push({ type: 'double', value: 'legLength', text: '支腿长度(m)	', dictCode: '' })
      fieldList.push({ type: 'double', value: 'mainLiftingCapacity', text: '主吊起重能力	', dictCode: '' })
      fieldList.push({ type: 'double', value: 'mainLiftingHeight', text: '主吊起吊高度(m)	', dictCode: '' })
      fieldList.push({ type: 'double', value: 'deputyLiftingCapacity', text: '副吊起重能力	', dictCode: '' })
      fieldList.push({ type: 'double', value: 'deputyLiftingHeight', text: '副吊起吊高度(m)	', dictCode: '' })
      fieldList.push({ type: 'double', value: 'operatingWaterDepth', text: '作业水深(m)	', dictCode: '' })
      fieldList.push({ type: 'string', value: 'craneType', text: '起重机类型	', dictCode: 'crane_type' })
      fieldList.push({ type: 'string', value: 'fullLiftingCapacity', text: '全回转起重能力	', dictCode: '' })
      fieldList.push({ type: 'double', value: 'fixedLiftingAbility', text: '固定艉吊能力	', dictCode: '' })
      fieldList.push({ type: 'double', value: 'hangHigh', text: '吊高	', dictCode: '' })
      fieldList.push({ type: 'double', value: 'frameHeight', text: '桩架高度	', dictCode: '' })
      fieldList.push({ type: 'double', value: 'elevatingCapacity', text: '起重能力	', dictCode: '' })
      fieldList.push({ type: 'double', value: 'maxPileLength', text: '最大打桩长度	', dictCode: '' })
      fieldList.push({ type: 'double', value: 'maxDiameterPile', text: '最大径桩	', dictCode: '' })
      fieldList.push({ type: 'double', value: 'tonnage', text: '吨位（t）	', dictCode: '' })
      fieldList.push({ type: 'int', value: 'carryNumber', text: '可载人数	', dictCode: '' })
      fieldList.push({ type: 'double', value: 'grossTon', text: '总吨（t）', dictCode: '' })
      fieldList.push({ type: 'double', value: 'natTon', text: '净吨（t）', dictCode: '' })
      fieldList.push({ type: 'double', value: 'draught', text: '吃水（m）', dictCode: '' })
      fieldList.push({ type: 'string', value: 'shipType', text: '类型', dictCode: 'ship_typ_code	,,' })
      fieldList.push({ type: 'double', value: 'shipSpeed', text: '航速（节）', dictCode: '' })
      this.superFieldList = fieldList
    },
    onSelect(selectedKeys, e) {
      console.log('selected', selectedKeys, e)
      this.selectedKeys = selectedKeys
      if (e.selectedNodes.length > 0) {
        this.extraData.shipType = e.selectedNodes[0].data.props
        console.log('加载3')
        this.loadData(this.extraData)
      }
    },
    onExpand(expandedKeys) {},
    treeLoadData() {
      console.info('loadTree!!!')
      this.loading = true
      this.loadTree()
    },
    loadTree() {
      console.log('loadTreeaaaa!!!')
      let that = this
      getDictItems('ship_typ_code').then((resp) => {
        console.info('ship_typ_code', resp)
        let root = {
          title: '船舶类型',
          value: '-1',
          children: resp,
        }
        console.log('this.extraData', this.extraData)
        that.treeProps.treeData.push(root)
        this.queryParam.shipType = this.extraData.shipType.value
      })
    },
    loadData_bak(arg) {
      console.info('loadData!!!')
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      // 加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() // 查询条件
      //选择船舶类型
      if (this.extraData.shipType) {
        if (this.extraData.shipType.value != '-1') {
          params.shipType = this.extraData.shipType.value
        }
      }
      this.loading = true
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          // update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          this.dataSource = res.result.records || res.result
          if (res.result.total) {
            this.ipagination.total = res.result.total
          } else {
            this.ipagination.total = 0
          }
          // update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
        }
        if (res.code === 510) {
          this.$message.warning(res.message)
        }
        this.loading = false
      })
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
