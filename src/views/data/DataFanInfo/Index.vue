<template>
  <div class='list-page data-fan-info-list'>
    <table-layout
      :search-props='searchProps'
      :rightTitle='rightTitle'
      :table-props='tableProps'
      @init-params='initParams'
      @search-submit='searchQuery'
      @table-change='onTableChange'
    >
      <template #installedIcon='{ record }'>
        <img
          v-if='record.installedIcon'
          :src='getImageUrl(record.installedIcon)'
          style='width: 30px; height: 30px'
          @click='clickImage(record.installedIcon)'
        />
      </template>
      <template #uninstallIcon='{ record }'>
        <img
          v-if='record.uninstallIcon'
          :src='getImageUrl(record.uninstallIcon)'
          style='width: 30px; height: 30px'
          @click='clickImage(record.uninstallIcon)'
        />
      </template>
    </table-layout>
    <!-- <a-modal v-model="visable" title="预览" ok-text="确定" cancel-text="取消" @ok="visable = false">
      <img :src="previewImageUrl" style="max-width: 100%; max-height: 100%" />
    </a-modal> -->
    <!-- 图标太小产品不要预览了 -->
    <modal ref='modalForm' @ok='modalFormOk'></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import JModal from '@/components/jeecg/JModal'
import { getFileAccessHttpUrl } from '@/api/manage'

import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'

export default {
  name: 'DataFanInfoList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
    JModal
  },
  data() {
    return {
      previewImageUrl: '',
      visable: false,
      // 搜索组件的props
      rightTitle: '风机型号列表',
      searchProps: {
        formModel: {
          fanType: null
        },
        formItems: [{ key: 'fanType', label: '风机类型名' }]
      },
      // 表格组件的props
      tableProps: {
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0
        },
        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            }
          },
          {
            title: '风机类型名',
            align: 'center',
            dataIndex: 'fanType'
          },
          {
            title: '备注',
            align: 'center',
            dataIndex: 'remark',
            width: 300,
            customRender: (t, r, index) => <div class='ellipsis'>{t}</div>
          },
          {
            title: '已安装图标',
            align: 'center',
            dataIndex: 'installedIcon',
            scopedSlots: { customRender: 'installedIcon' }
          },
          {
            title: '未安装图标',
            align: 'center',
            dataIndex: 'uninstallIcon',
            scopedSlots: { customRender: 'uninstallIcon' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 175,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail
          },
          {
            text: '编辑',
            handler: this.handleEdit
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete
          }
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd
          }
        ]
      },

      url: {
        list: '/data/dataFanInfo/list',
        delete: '/data/dataFanInfo/delete',
        deleteBatch: '/data/dataFanInfo/deleteBatch',
        exportXlsUrl: '/data/dataFanInfo/exportXls',
        importExcelUrl: 'data/dataFanInfo/importExcel'
      }
    }
  },
  created() {
  },
  computed: {},
  watch: {},
  methods: {
    clickImage(url) {
      this.previewImageUrl = this.getImageUrl(url)
      this.visable = true
    },
    getImageUrl(url) {
      return getFileAccessHttpUrl(url)
    },
    initParams(mergeParams) {
      console.log('initParams-mergeParams', mergeParams)
      this.queryParam = mergeParams.params
    }
  }
}
</script>

<style lang='less' scoped>
.ellipsis {
  width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ellipsis:hover {
  white-space: normal;
  width: 300px;
  overflow: visible;
}
</style>
