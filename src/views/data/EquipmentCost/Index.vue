<template>
  <div class="list-page wind-notification-list">
    <table-layout
      :search-props="searchProps"
      :table-props="tableProps"
      :rightTitle="rightTitle"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { putAction } from '@/api/manage'

export default {
  name: 'WindNotificationList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal,
  },
  data() {
    return {

      rightTitle: '关键设备成本',
      // 搜索组件的props
      searchProps: {
        formModel: {
          name: null,
        },
        formItems: [
          { key: 'name', label: '设备名称', type: 'text' }
        ],
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },

        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '设备名称',
            align: 'center',
            dataIndex: 'name',
          },
          {
            title: '规格',
            align: 'center',
            dataIndex: 'specs',
          },
          {
            title: '型号',
            align: 'center',
            dataIndex: 'model',
          },
          {
            title: '投标单价（万元）',
            align: 'center',
            dataIndex: 'bidPrice',
          },
          {
            title: '采购单价（万元）',
            align: 'center',
            dataIndex: 'purchasePrice',
          },
          {
            title: '供应商',
            align: 'center',
            dataIndex: 'supplier',
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 175,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete
          },
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd,
          },
        ],
      },

      url: {
        list: '/data/dataEquipmentCost/list',
        delete: '/data/dataEquipmentCost/delete',
        deleteBatch: '/wind/windNotification/deleteBatch'
      },
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    initParams(mergeParams) {
      console.log('initParams-mergeParams', mergeParams)
      this.queryParam = mergeParams.params
    },
  },
}
</script>
