<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="设备名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-tree-select
                  v-decorator="['name', validatorRules.name]"
                  allowClear
                  style="width: 100%"
                  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                  :tree-data="treeData"
                  placeholder="请选择设备"
                  tree-default-expand-all
                  :replaceFields="{
                    children: 'children',
                    value: 'name',
                    key: 'id',
                    label: 'name',
                  }"
                  tree-node-filter-prop="title"
                  @change = equipmentChange
                >
                </a-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="规格" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['specs']"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="型号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input disabled v-decorator="['model']"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="投标单价" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['bidPrice', validatorRules.bidPrice]" placeholder="请输入投标单价" suffix="万元"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="采购单价" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['purchasePrice', validatorRules.purchasePrice]" placeholder="请输入采购单价"suffix="万元"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="供应商" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['supplier', validatorRules.supplier]" placeholder="请输入供应商"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="省市区" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-area-linkage style="margin-top: 4px;" v-decorator="['area']"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="登记时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择登记时间"
                v-decorator="['registerTime']"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="备注" :labelCol="{
    xs: { span: 24 },
    sm: { span: 3 },
  }" :wrapperCol="{
    xs: { span: 24 },
    sm: { span: 21 },
  }">
              <a-textarea rows="4" autocomplete="off" v-decorator="['remark', validatorRules.remark]"
                placeholder="请输入"></a-textarea>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="附件" :labelCol="{
    xs: { span: 24 },
    sm: { span: 3 },
  }" :wrapperCol="{
    xs: { span: 24 },
    sm: { span: 21 },
  }">
              <j-upload v-decorator="['file']" :isMultiple="true" :limit="1"></j-upload>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction } from '@/api/manage'
import pick from 'lodash.pick'
import _ from 'lodash'
import { JUpload } from '@/components/jeecg/JUpload'
export default {
  name: 'WindNotificationForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => { },
      required: false,
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false,
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
      },
      equipmentList: [],
      treeData:[],
      confirmLoading: false,
      validatorRules: {
        name: {
          rules: [{ required: true, message: '请选择设备名称!' }],
        },
        bidPrice: {
          rules: [{validator: this.validatePrice}],
        },
        purchasePrice: {
          rules: [{validator: this.validatePrice}],
        },
        supplier: {
          rules: [{ max: 20, message: '供应商不能超过20个字' }],
        },
        remark: {
          rules: [{ max: 200, message: '备注不能超过200个字' }],
        },
      },
      url: {
        queryTreeList: '/project/myCategory/queryTreeList', //查询树结构 传code
        equipmentList: '/project/projectEquipment/list',
        add: '/data/dataEquipmentCost/add',
        edit: '/data/dataEquipmentCost/edit',
        queryById: '/data/dataEquipmentCost/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
  },
  created() {
    httpAction(this.url.queryTreeList + '?code=B09', '', 'get').then((res) => {
        if (res.success) {
          // this.treeData = res.result
          let treeRoot = res.result;
          httpAction(this.url.equipmentList+"?pageSize=10000", {}, "GET").then((response) => {
            if (response.success) {
              this.equipmentList = response.result.records
              treeRoot.map((item) => {
                if(item.children && item.children.length > 0){
                  item.children.map((itemC) => {
                    let child = [];
                    response.result.records.forEach(element =>{
                      if(element.type==itemC.code){
                        child.push({id:element.id,code:element.code, name:element.name});
                      }
                    })
                    if(child.length>0){
                      itemC.children = child;
                      itemC.hasChild = '1';
                      itemC.disabled = true
                    }
                  })
                  item.disabled = true
                }
              })
              this.treeData = treeRoot
            }
          })
          
        }
      })
  },
  methods: {
    add() {
      this.receiverInfo = []
      this.contactInfo = null
      this.edit({})
    },
    edit(oldRecord) {
      let record = _.cloneDeep(oldRecord)
      if (record.receiverInfo != null) {
        let obj = JSON.parse(record.receiverInfo)
        let arr = []
        for (let i = 0; i < obj.length; i++) {
          arr.push(obj[i].id)
        }
        this.receiverInfo = arr
        record.receiverInfo = this.receiverInfo
      }
      if (record.contactInfo != null) {
        let obj = JSON.parse(record.contactInfo)
        this.contactInfo = obj.id
        record.contactInfo = this.contactInfo
      }

      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue(
          pick(this.model, 'name', 'specs', 'model', 'bidPrice', 'purchasePrice', 'supplier', 'remark', 'file', 'area', 'registerTime')
        )
      })
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values);

          console.log('表单提交数据', formData, this.personList)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(
        pick(row, 'name', 'specs', 'model', 'bidPrice', 'purchasePrice', 'supplier', 'remark', 'file')
      )
    },
    validatePrice(rule, value, callback) {
      if (!value || new RegExp(/^\d+(\.\d+)?$/).test(value)) {
        callback();
      } else {
        callback("您填写的价格不正确!");
      }
    },
    equipmentChange(value, label) {
      let selected = this.equipmentList.filter(item => item.name == value);
      this.form.setFieldsValue({specs: selected[0].specification , model:selected[0].modelNumber})
    },
  },
}
</script>
