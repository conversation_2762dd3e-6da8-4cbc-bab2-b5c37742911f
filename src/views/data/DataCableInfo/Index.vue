<template>
  <div class='list-page data-fan-info-list'>
    <table-layout
      :rightTitle='rightTitle'
      :search-props='searchProps'
      @init-params='initParams'
      :table-props='tableProps'
      @search-submit='searchQuery'
      @table-change='onTableChange'
    >
      <template #color='{ record }'>
        <div class='color-cell'
             :style="{ backgroundColor: record.color }"></div>
      </template>
    </table-layout>
    <modal ref='modalForm' @ok='modalFormOk'></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'

export default {
  name: 'DataCableInfoList',
  mixins: [JeecgTreeListMixin],
  components: {
    Modal
  },
  data() {
    return {
      // 搜索组件的props
      rightTitle: '海缆规格列表',
      searchProps: {
        formModel: {
          cableType: null
        },
        formItems: [{ key: 'cableType', label: '海缆规格' }]
      },
      // 表格组件的props
      tableProps: {
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0
        },
        dataSource: [],
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            }
          },
          {
            title: '海缆规格',
            align: 'center',
            dataIndex: 'cableType'
          },

          {
            title: '颜色',
            align: 'center',
            dataIndex: 'color',
            width: '530px',
            scopedSlots: { customRender: 'color' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 175,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail
          },
          {
            text: '编辑',
            handler: this.handleEdit
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete
          }
        ],
        headerButtons: [
          {
            text: '新增',
            icon: 'plus-circle',
            handler: this.handleAdd
          }
        ]
      },

      url: {
        list: '/data/dataCableInfo/list',
        delete: '/data/dataCableInfo/delete',
        deleteBatch: '/data/dataCableInfo/deleteBatch',
        exportXlsUrl: '/data/dataCableInfo/exportXls',
        importExcelUrl: 'data/dataCableInfo/importExcel'
      }
    }
  },
  created() {
  },
  computed: {},
  watch: {},
  methods: {
    initParams(mergeParams) {
      console.log('initParams-mergeParams', mergeParams)
      this.queryParam = mergeParams.params
    }
  }
}
</script>
<style lang='less' scoped>
.ellipsis {
  width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ellipsis:hover {
  white-space: normal;
  width: 300px;
  overflow: visible;
}

.color-cell {
  width: 500px;
  height: 16px;
  margin: 0 auto; /* 居中显示 */
}
</style>
